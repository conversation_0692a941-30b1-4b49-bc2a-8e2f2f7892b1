{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\ProfilePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { UserStatsContext } from '../context/UserStatsContext';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport ImageUpload from '../components/ImageUpload';\nimport OrderCard from '../components/OrderCard';\nimport RewardCard from '../components/RewardCard';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProfilePage() {\n  _s();\n  const {\n    token,\n    user,\n    logout\n  } = useContext(AuthContext);\n  const {\n    userStats,\n    orders,\n    redeemReward\n  } = useContext(UserStatsContext);\n  const [profile, setProfile] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [editMode, setEditMode] = useState(false);\n  const [showImageUpload, setShowImageUpload] = useState(false);\n  const [profileImage, setProfileImage] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: ''\n    },\n    preferences: {\n      newsletter: true,\n      notifications: true,\n      ecoTips: true\n    }\n  });\n\n  // Available rewards\n  const availableRewards = [{\n    id: 1,\n    title: '10% Off Next Order',\n    description: 'Get 10% discount on your next purchase of any amount',\n    cost: 100,\n    type: 'discount',\n    value: '$10+ savings',\n    popular: true,\n    features: ['Valid for 30 days', 'Stackable with sales', 'No minimum purchase']\n  }, {\n    id: 2,\n    title: 'Free Shipping',\n    description: 'Get free shipping on any order, regardless of amount',\n    cost: 50,\n    type: 'shipping',\n    value: '$9.99 value',\n    features: ['Valid for 60 days', 'Any order size', 'Express shipping available']\n  }, {\n    id: 3,\n    title: 'Mystery Eco Box',\n    description: 'Receive a curated box of sustainable products worth $50+',\n    cost: 500,\n    type: 'product',\n    value: '$50+ value',\n    savings: '$25',\n    features: ['3-5 eco products', 'Surprise items', 'Limited edition items']\n  }, {\n    id: 4,\n    title: 'Plant a Tree',\n    description: 'We\\'ll plant a tree in your name to offset carbon emissions',\n    cost: 200,\n    type: 'eco',\n    value: 'Environmental impact',\n    features: ['Certificate included', 'GPS coordinates', 'Annual updates']\n  }];\n  useEffect(() => {\n    if (token) {\n      fetchProfile();\n    } else {\n      setLoading(false);\n      setError('Please login to view your profile');\n    }\n  }, [token]);\n  const fetchProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/users/profile', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setProfile(response.data);\n      setProfileImage(response.data.profileImage || null);\n      setFormData({\n        name: response.data.name || '',\n        email: response.data.email || '',\n        phone: response.data.phone || '',\n        address: response.data.address || {\n          street: '',\n          city: '',\n          state: '',\n          zipCode: '',\n          country: ''\n        },\n        preferences: response.data.preferences || {\n          newsletter: true,\n          notifications: true,\n          ecoTips: true\n        }\n      });\n    } catch (err) {\n      setError('Failed to load profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: type === 'checkbox' ? checked : value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: type === 'checkbox' ? checked : value\n      }));\n    }\n  };\n  const handleSaveProfile = async () => {\n    try {\n      // Simulate API call\n      setProfile({\n        ...profile,\n        ...formData,\n        profileImage\n      });\n      setEditMode(false);\n      // Show success message\n    } catch (err) {\n      setError('Failed to update profile');\n    }\n  };\n  const handleImageChange = newImage => {\n    setProfileImage(newImage);\n    // Simulate saving to profile\n    setProfile(prev => ({\n      ...prev,\n      profileImage: newImage\n    }));\n  };\n  const getInitials = name => {\n    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const getLoyaltyLevel = points => {\n    if (points >= 1000) return {\n      level: 'Platinum',\n      color: '#8b5cf6',\n      next: null\n    };\n    if (points >= 500) return {\n      level: 'Gold',\n      color: '#f59e0b',\n      next: 1000\n    };\n    if (points >= 200) return {\n      level: 'Silver',\n      color: '#6b7280',\n      next: 500\n    };\n    return {\n      level: 'Bronze',\n      color: '#92400e',\n      next: 200\n    };\n  };\n  const handleReorder = order => {\n    // Implement reorder functionality\n    console.log('Reordering:', order);\n  };\n  const handleReview = order => {\n    // Implement review functionality\n    console.log('Writing review for:', order);\n  };\n  const handleRedeemReward = async reward => {\n    try {\n      await redeemReward(reward.cost);\n      alert(`Successfully redeemed: ${reward.title}`);\n    } catch (error) {\n      alert('Failed to redeem reward');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      size: \"large\",\n      message: \"Loading your profile...\",\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      title: \"Profile Error\",\n      message: error,\n      onRetry: fetchProfile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 12\n    }, this);\n  }\n  if (!profile) {\n    return /*#__PURE__*/_jsxDEV(\"main\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please log in to view your profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/login',\n          className: \"login-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sign-in-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), \"Go to Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this);\n  }\n  const loyaltyInfo = getLoyaltyLevel(userStats.loyaltyPoints || 0);\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-avatar-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-avatar\",\n            children: profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: profileImage,\n              alt: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"avatar-initials\",\n              children: getInitials(profile.name)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"edit-avatar-btn\",\n            onClick: () => setShowImageUpload(true),\n            title: \"Change profile picture\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-camera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: [\"Welcome back, \", profile.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"profile-email\",\n            children: profile.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loyalty-badge\",\n            style: {\n              backgroundColor: loyaltyInfo.color\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-crown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), loyaltyInfo.level, \" Member\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: userStats.loyaltyPoints\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Loyalty Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: userStats.totalOrders\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: [\"$\", userStats.totalSpent.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), loyaltyInfo.next && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loyalty-progress\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Progress to \", loyaltyInfo.next === 1000 ? 'Platinum' : loyaltyInfo.next === 500 ? 'Gold' : 'Silver']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [userStats.loyaltyPoints, \" / \", loyaltyInfo.next, \" points\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${userStats.loyaltyPoints / loyaltyInfo.next * 100}%`,\n              backgroundColor: loyaltyInfo.color\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"progress-text\",\n          children: [\"Earn \", loyaltyInfo.next - userStats.loyaltyPoints, \" more points to reach the next level!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'overview' ? 'active' : ''}`,\n          onClick: () => setActiveTab('overview'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chart-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), \"Overview\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'orders' ? 'active' : ''}`,\n          onClick: () => setActiveTab('orders'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-box\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), \"Order History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'settings' ? 'active' : ''}`,\n          onClick: () => setActiveTab('settings'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-cog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), \"Account Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'rewards' ? 'active' : ''}`,\n          onClick: () => setActiveTab('rewards'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-gift\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), \"Rewards\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overview-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overview-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-leaf\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this), \"Eco Impact\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"eco-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eco-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-value\",\n                    children: [userStats.ecoImpact.co2Saved, \" kg\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-label\",\n                    children: \"CO\\u2082 Saved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eco-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-value\",\n                    children: userStats.ecoImpact.ecoProducts\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-label\",\n                    children: \"Eco Products\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eco-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-value\",\n                    children: userStats.ecoImpact.treesPlanted\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"eco-label\",\n                    children: \"Trees Planted\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overview-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), \"Recent Activity\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"activity-list\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-shopping-cart activity-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-text\",\n                      children: \"Ordered Solar Power Bank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-date\",\n                      children: \"2 days ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-heart activity-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-text\",\n                      children: \"Added Bamboo Toothbrush to wishlist\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-date\",\n                      children: \"1 week ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-star activity-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-text\",\n                      children: \"Reviewed Organic Cotton T-Shirt\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"activity-date\",\n                      children: \"2 weeks ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overview-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-trophy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 23\n                  }, this), \"Achievements\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"achievements\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement earned\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-seedling\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Eco Warrior\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement earned\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-shopping-bag\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"First Purchase\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-users\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Referral Master\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Monthly Shopper\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), activeTab === 'orders' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"orders-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Order History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-filters\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"filter-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"delivered\",\n                  children: \"Delivered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"shipped\",\n                  children: \"Shipped\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"processing\",\n                  children: \"Processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-list\",\n            children: orders.map(order => /*#__PURE__*/_jsxDEV(OrderCard, {\n              order: order,\n              onReorder: handleReorder,\n              onReview: handleReview\n            }, order.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), activeTab === 'settings' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Account Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setEditMode(!editMode),\n              className: `edit-btn ${editMode ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${editMode ? 'fa-times' : 'fa-edit'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this), editMode ? 'Cancel' : 'Edit Profile']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Profile Picture\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-picture-section\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"current-picture\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"picture-preview\",\n                    children: profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: profileImage,\n                      alt: \"Profile\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"picture-placeholder\",\n                      children: getInitials(profile.name)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"picture-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Your profile picture is visible to other users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowImageUpload(true),\n                      className: \"change-picture-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-camera\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 27\n                      }, this), \"Change Picture\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Personal Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"name\",\n                    children: \"Full Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"name\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"email\",\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    id: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"phone\",\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  id: \"phone\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleInputChange,\n                  disabled: !editMode,\n                  placeholder: \"(*************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Shipping Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"address.street\",\n                  children: \"Street Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"address.street\",\n                  name: \"address.street\",\n                  value: formData.address.street,\n                  onChange: handleInputChange,\n                  disabled: !editMode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"address.city\",\n                    children: \"City\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"address.city\",\n                    name: \"address.city\",\n                    value: formData.address.city,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"address.state\",\n                    children: \"State\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"address.state\",\n                    name: \"address.state\",\n                    value: formData.address.state,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"address.zipCode\",\n                    children: \"ZIP Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"address.zipCode\",\n                    name: \"address.zipCode\",\n                    value: formData.address.zipCode,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Preferences\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preferences-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-label\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"preferences.newsletter\",\n                    checked: formData.preferences.newsletter,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Newsletter\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Receive updates about new eco-friendly products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-label\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"preferences.notifications\",\n                    checked: formData.preferences.notifications,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Order Notifications\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Get notified about order status updates\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-label\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"preferences.ecoTips\",\n                    checked: formData.preferences.ecoTips,\n                    onChange: handleInputChange,\n                    disabled: !editMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Eco Tips\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Receive sustainability tips and advice\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this), editMode && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setEditMode(false),\n                className: \"btn-secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSaveProfile,\n                className: \"btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-save\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 23\n                }, this), \"Save Changes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this), activeTab === 'rewards' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rewards-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rewards-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Loyalty Rewards\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"points-balance\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-value\",\n                children: profile.points || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-label\",\n                children: \"Available Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rewards-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reward-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-percentage\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"10% Off Next Order\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Use your points to get a discount on your next purchase\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-cost\",\n                children: \"100 points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"redeem-btn\",\n                disabled: (profile.points || 0) < 100,\n                children: \"Redeem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reward-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shipping-fast\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Free Shipping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Get free shipping on any order, regardless of amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-cost\",\n                children: \"50 points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"redeem-btn\",\n                disabled: (profile.points || 0) < 50,\n                children: \"Redeem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reward-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-gift\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Mystery Eco Box\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Receive a curated box of sustainable products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-cost\",\n                children: \"500 points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"redeem-btn\",\n                disabled: (profile.points || 0) < 500,\n                children: \"Redeem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reward-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-tree\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Plant a Tree\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"We'll plant a tree in your name to offset carbon emissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reward-cost\",\n                children: \"200 points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"redeem-btn\",\n                disabled: (profile.points || 0) < 200,\n                children: \"Redeem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"earn-points\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"How to Earn More Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"earn-methods\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"earn-method\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Make Purchases\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Earn 1 point for every $1 spent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"earn-method\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-star\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Write Reviews\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Get 10 points for each product review\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"earn-method\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Refer Friends\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Earn 50 points for each successful referral\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"earn-method\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-birthday-cake\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Birthday Bonus\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Get 100 points on your birthday\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sign-out-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this), \"Sign Out\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), showImageUpload && /*#__PURE__*/_jsxDEV(ImageUpload, {\n      currentImage: profileImage,\n      onImageChange: handleImageChange,\n      onClose: () => setShowImageUpload(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .profile-container {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .auth-required {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          min-height: 60vh;\n          text-align: center;\n          gap: 1.5rem;\n        }\n\n        .auth-icon {\n          font-size: 4rem;\n          color: var(--text-secondary);\n        }\n\n        .login-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 1rem 2rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .profile-header {\n          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\n          color: white;\n          padding: 2rem;\n          border-radius: 1rem;\n          display: grid;\n          grid-template-columns: auto 1fr auto;\n          gap: 2rem;\n          align-items: center;\n          margin-bottom: 2rem;\n        }\n\n        .profile-avatar-container {\n          position: relative;\n          display: inline-block;\n        }\n\n        .profile-avatar {\n          width: 100px;\n          height: 100px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 2rem;\n          overflow: hidden;\n          border: 4px solid rgba(255, 255, 255, 0.3);\n          transition: all 0.3s ease;\n        }\n\n        .profile-avatar img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .avatar-initials {\n          background: rgba(255, 255, 255, 0.2);\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 2rem;\n          font-weight: 700;\n          color: white;\n        }\n\n        .edit-avatar-btn {\n          position: absolute;\n          bottom: 0;\n          right: 0;\n          background: var(--primary-color);\n          color: white;\n          border: 2px solid white;\n          border-radius: 50%;\n          width: 36px;\n          height: 36px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          font-size: 0.875rem;\n          transition: all 0.2s ease;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n        }\n\n        .edit-avatar-btn:hover {\n          background: var(--primary-dark);\n          transform: scale(1.1);\n        }\n\n        .profile-avatar-container:hover .profile-avatar {\n          transform: scale(1.05);\n          border-color: rgba(255, 255, 255, 0.5);\n        }\n\n        .profile-info h1 {\n          margin: 0 0 0.5rem 0;\n          font-size: 2rem;\n          font-weight: 700;\n        }\n\n        .profile-email {\n          opacity: 0.9;\n          margin: 0 0 1rem 0;\n        }\n\n        .loyalty-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          font-weight: 600;\n          font-size: 0.875rem;\n        }\n\n        .profile-stats {\n          display: flex;\n          gap: 2rem;\n        }\n\n        .stat {\n          text-align: center;\n        }\n\n        .stat-value {\n          font-size: 2rem;\n          font-weight: 700;\n          line-height: 1;\n        }\n\n        .stat-label {\n          font-size: 0.875rem;\n          opacity: 0.9;\n          margin-top: 0.25rem;\n        }\n\n        .loyalty-progress {\n          background: white;\n          padding: 1.5rem;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          margin-bottom: 2rem;\n        }\n\n        .progress-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 1rem;\n          font-weight: 500;\n        }\n\n        .progress-bar {\n          height: 8px;\n          background: var(--bg-secondary);\n          border-radius: 4px;\n          overflow: hidden;\n          margin-bottom: 0.75rem;\n        }\n\n        .progress-fill {\n          height: 100%;\n          transition: width 0.3s ease;\n        }\n\n        .progress-text {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin: 0;\n        }\n\n        .profile-tabs {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 2rem;\n          background: white;\n          padding: 0.5rem;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n        }\n\n        .tab {\n          flex: 1;\n          background: none;\n          border: none;\n          padding: 1rem;\n          border-radius: 0.75rem;\n          cursor: pointer;\n          font-weight: 500;\n          color: var(--text-secondary);\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .tab:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .tab.active {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .tab-content {\n          background: white;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n        }\n\n        .overview-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 2rem;\n          padding: 2rem;\n        }\n\n        .overview-card {\n          background: var(--bg-secondary);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .card-header {\n          margin-bottom: 1.5rem;\n        }\n\n        .card-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .eco-stats {\n          display: grid;\n          grid-template-columns: repeat(3, 1fr);\n          gap: 1rem;\n        }\n\n        .eco-stat {\n          text-align: center;\n        }\n\n        .eco-value {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--secondary-color);\n          line-height: 1;\n        }\n\n        .eco-label {\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n          margin-top: 0.25rem;\n        }\n\n        .activity-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .activity-item {\n          display: flex;\n          gap: 1rem;\n          align-items: flex-start;\n        }\n\n        .activity-icon {\n          width: 40px;\n          height: 40px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          flex-shrink: 0;\n        }\n\n        .activity-text {\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .activity-date {\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .achievements {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          gap: 1rem;\n        }\n\n        .achievement {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 1rem;\n          border-radius: 0.75rem;\n          border: 2px solid var(--border-color);\n          opacity: 0.5;\n          transition: all 0.2s;\n        }\n\n        .achievement.earned {\n          opacity: 1;\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\n          color: white;\n          border-color: var(--secondary-color);\n        }\n\n        .achievement i {\n          font-size: 1.25rem;\n        }\n\n        @media (max-width: 768px) {\n          .profile-header {\n            grid-template-columns: 1fr;\n            text-align: center;\n            gap: 1.5rem;\n          }\n\n          .profile-avatar-container {\n            align-self: center;\n            justify-self: center;\n          }\n\n          .profile-avatar {\n            width: 120px;\n            height: 120px;\n          }\n\n          .edit-avatar-btn {\n            width: 40px;\n            height: 40px;\n            font-size: 1rem;\n          }\n\n          .profile-stats {\n            justify-content: center;\n          }\n\n          .profile-tabs {\n            flex-direction: column;\n          }\n\n          .overview-grid {\n            grid-template-columns: 1fr;\n            padding: 1rem;\n          }\n\n          .current-picture {\n            flex-direction: column;\n            text-align: center;\n            gap: 1rem;\n          }\n        }\n\n        /* Profile Picture Section Styles */\n        .profile-picture-section {\n          margin-bottom: 2rem;\n        }\n\n        .current-picture {\n          display: flex;\n          align-items: center;\n          gap: 2rem;\n          padding: 1.5rem;\n          background: var(--bg-secondary);\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .picture-preview {\n          width: 100px;\n          height: 100px;\n          border-radius: 50%;\n          overflow: hidden;\n          border: 3px solid var(--border-color);\n          flex-shrink: 0;\n        }\n\n        .picture-preview img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .picture-placeholder {\n          width: 100%;\n          height: 100%;\n          background: var(--primary-color);\n          color: white;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 2rem;\n          font-weight: 700;\n        }\n\n        .picture-info {\n          flex: 1;\n        }\n\n        .picture-info p {\n          margin: 0 0 1rem 0;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .change-picture-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .change-picture-btn:hover {\n          background: var(--primary-dark);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfilePage, \"QQhOpMJTpVV6TMPp1Rn/fc8GZW0=\");\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "AuthContext", "UserStatsContext", "LoadingSpinner", "ErrorMessage", "ImageUpload", "OrderCard", "RewardCard", "axios", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "token", "user", "logout", "userStats", "orders", "redeemReward", "profile", "setProfile", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "editMode", "setEditMode", "showImageUpload", "setShowImageUpload", "profileImage", "setProfileImage", "formData", "setFormData", "name", "email", "phone", "address", "street", "city", "state", "zipCode", "country", "preferences", "newsletter", "notifications", "ecoTips", "availableRewards", "id", "title", "description", "cost", "type", "value", "popular", "features", "savings", "fetchProfile", "response", "get", "headers", "Authorization", "data", "err", "handleInputChange", "e", "checked", "target", "includes", "parent", "child", "split", "prev", "handleSaveProfile", "handleImageChange", "newImage", "getInitials", "map", "word", "char<PERSON>t", "join", "toUpperCase", "slice", "getLoyaltyLevel", "points", "level", "color", "next", "handleReorder", "order", "console", "log", "handleReview", "handleRedeemReward", "reward", "alert", "size", "message", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "children", "className", "onClick", "window", "location", "href", "loyaltyInfo", "loyaltyPoints", "src", "alt", "style", "backgroundColor", "totalOrders", "totalSpent", "toFixed", "width", "ecoImpact", "co2Saved", "ecoProducts", "treesPlanted", "onReorder", "onReview", "htmlFor", "onChange", "disabled", "placeholder", "currentImage", "onImageChange", "onClose", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProfilePage.jsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\r\nimport { AuthContext } from '../context/AuthContext';\r\nimport { UserStatsContext } from '../context/UserStatsContext';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport ImageUpload from '../components/ImageUpload';\r\nimport OrderCard from '../components/OrderCard';\r\nimport RewardCard from '../components/RewardCard';\r\nimport axios from 'axios';\r\n\r\nfunction ProfilePage() {\r\n  const { token, user, logout } = useContext(AuthContext);\r\n  const { userStats, orders, redeemReward } = useContext(UserStatsContext);\r\n  const [profile, setProfile] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [editMode, setEditMode] = useState(false);\r\n  const [showImageUpload, setShowImageUpload] = useState(false);\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    phone: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: ''\r\n    },\r\n    preferences: {\r\n      newsletter: true,\r\n      notifications: true,\r\n      ecoTips: true\r\n    }\r\n  });\r\n\r\n  // Available rewards\r\n  const availableRewards = [\r\n    {\r\n      id: 1,\r\n      title: '10% Off Next Order',\r\n      description: 'Get 10% discount on your next purchase of any amount',\r\n      cost: 100,\r\n      type: 'discount',\r\n      value: '$10+ savings',\r\n      popular: true,\r\n      features: ['Valid for 30 days', 'Stackable with sales', 'No minimum purchase']\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'Free Shipping',\r\n      description: 'Get free shipping on any order, regardless of amount',\r\n      cost: 50,\r\n      type: 'shipping',\r\n      value: '$9.99 value',\r\n      features: ['Valid for 60 days', 'Any order size', 'Express shipping available']\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'Mystery Eco Box',\r\n      description: 'Receive a curated box of sustainable products worth $50+',\r\n      cost: 500,\r\n      type: 'product',\r\n      value: '$50+ value',\r\n      savings: '$25',\r\n      features: ['3-5 eco products', 'Surprise items', 'Limited edition items']\r\n    },\r\n    {\r\n      id: 4,\r\n      title: 'Plant a Tree',\r\n      description: 'We\\'ll plant a tree in your name to offset carbon emissions',\r\n      cost: 200,\r\n      type: 'eco',\r\n      value: 'Environmental impact',\r\n      features: ['Certificate included', 'GPS coordinates', 'Annual updates']\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      fetchProfile();\r\n    } else {\r\n      setLoading(false);\r\n      setError('Please login to view your profile');\r\n    }\r\n  }, [token]);\r\n\r\n  const fetchProfile = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get('http://localhost:5000/api/users/profile', {\r\n        headers: { Authorization: `Bearer ${token}` }\r\n      });\r\n      setProfile(response.data);\r\n      setProfileImage(response.data.profileImage || null);\r\n      setFormData({\r\n        name: response.data.name || '',\r\n        email: response.data.email || '',\r\n        phone: response.data.phone || '',\r\n        address: response.data.address || {\r\n          street: '',\r\n          city: '',\r\n          state: '',\r\n          zipCode: '',\r\n          country: ''\r\n        },\r\n        preferences: response.data.preferences || {\r\n          newsletter: true,\r\n          notifications: true,\r\n          ecoTips: true\r\n        }\r\n      });\r\n    } catch (err) {\r\n      setError('Failed to load profile');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [parent]: {\r\n          ...prev[parent],\r\n          [child]: type === 'checkbox' ? checked : value\r\n        }\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [name]: type === 'checkbox' ? checked : value\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleSaveProfile = async () => {\r\n    try {\r\n      // Simulate API call\r\n      setProfile({ ...profile, ...formData, profileImage });\r\n      setEditMode(false);\r\n      // Show success message\r\n    } catch (err) {\r\n      setError('Failed to update profile');\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (newImage) => {\r\n    setProfileImage(newImage);\r\n    // Simulate saving to profile\r\n    setProfile(prev => ({ ...prev, profileImage: newImage }));\r\n  };\r\n\r\n  const getInitials = (name) => {\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n\r\n\r\n  const getLoyaltyLevel = (points) => {\r\n    if (points >= 1000) return { level: 'Platinum', color: '#8b5cf6', next: null };\r\n    if (points >= 500) return { level: 'Gold', color: '#f59e0b', next: 1000 };\r\n    if (points >= 200) return { level: 'Silver', color: '#6b7280', next: 500 };\r\n    return { level: 'Bronze', color: '#92400e', next: 200 };\r\n  };\r\n\r\n  const handleReorder = (order) => {\r\n    // Implement reorder functionality\r\n    console.log('Reordering:', order);\r\n  };\r\n\r\n  const handleReview = (order) => {\r\n    // Implement review functionality\r\n    console.log('Writing review for:', order);\r\n  };\r\n\r\n  const handleRedeemReward = async (reward) => {\r\n    try {\r\n      await redeemReward(reward.cost);\r\n      alert(`Successfully redeemed: ${reward.title}`);\r\n    } catch (error) {\r\n      alert('Failed to redeem reward');\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner size=\"large\" message=\"Loading your profile...\" fullScreen />;\r\n  }\r\n\r\n  if (error) {\r\n    return <ErrorMessage title=\"Profile Error\" message={error} onRetry={fetchProfile} />;\r\n  }\r\n\r\n  if (!profile) {\r\n    return (\r\n      <main>\r\n        <div className=\"auth-required\">\r\n          <div className=\"auth-icon\">\r\n            <i className=\"fas fa-user-lock\"></i>\r\n          </div>\r\n          <h2>Authentication Required</h2>\r\n          <p>Please log in to view your profile</p>\r\n          <button onClick={() => window.location.href = '/login'} className=\"login-btn\">\r\n            <i className=\"fas fa-sign-in-alt\"></i>\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  const loyaltyInfo = getLoyaltyLevel(userStats.loyaltyPoints || 0);\r\n\r\n  return (\r\n    <main>\r\n      <div className=\"profile-container\">\r\n        {/* Profile Header */}\r\n        <div className=\"profile-header\">\r\n          <div className=\"profile-avatar-container\">\r\n            <div className=\"profile-avatar\">\r\n              {profileImage ? (\r\n                <img src={profileImage} alt={profile.name} />\r\n              ) : (\r\n                <div className=\"avatar-initials\">\r\n                  {getInitials(profile.name)}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <button\r\n              className=\"edit-avatar-btn\"\r\n              onClick={() => setShowImageUpload(true)}\r\n              title=\"Change profile picture\"\r\n            >\r\n              <i className=\"fas fa-camera\"></i>\r\n            </button>\r\n          </div>\r\n          <div className=\"profile-info\">\r\n            <h1>Welcome back, {profile.name}!</h1>\r\n            <p className=\"profile-email\">{profile.email}</p>\r\n            <div className=\"loyalty-badge\" style={{ backgroundColor: loyaltyInfo.color }}>\r\n              <i className=\"fas fa-crown\"></i>\r\n              {loyaltyInfo.level} Member\r\n            </div>\r\n          </div>\r\n          <div className=\"profile-stats\">\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">{userStats.loyaltyPoints}</div>\r\n              <div className=\"stat-label\">Loyalty Points</div>\r\n            </div>\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">{userStats.totalOrders}</div>\r\n              <div className=\"stat-label\">Total Orders</div>\r\n            </div>\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">${userStats.totalSpent.toFixed(2)}</div>\r\n              <div className=\"stat-label\">Total Spent</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Loyalty Progress */}\r\n        {loyaltyInfo.next && (\r\n          <div className=\"loyalty-progress\">\r\n            <div className=\"progress-header\">\r\n              <span>Progress to {loyaltyInfo.next === 1000 ? 'Platinum' : loyaltyInfo.next === 500 ? 'Gold' : 'Silver'}</span>\r\n              <span>{userStats.loyaltyPoints} / {loyaltyInfo.next} points</span>\r\n            </div>\r\n            <div className=\"progress-bar\">\r\n              <div\r\n                className=\"progress-fill\"\r\n                style={{\r\n                  width: `${(userStats.loyaltyPoints / loyaltyInfo.next) * 100}%`,\r\n                  backgroundColor: loyaltyInfo.color\r\n                }}\r\n              ></div>\r\n            </div>\r\n            <p className=\"progress-text\">\r\n              Earn {loyaltyInfo.next - userStats.loyaltyPoints} more points to reach the next level!\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Navigation Tabs */}\r\n        <div className=\"profile-tabs\">\r\n          <button\r\n            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('overview')}\r\n          >\r\n            <i className=\"fas fa-chart-line\"></i>\r\n            Overview\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'orders' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('orders')}\r\n          >\r\n            <i className=\"fas fa-box\"></i>\r\n            Order History\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'settings' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('settings')}\r\n          >\r\n            <i className=\"fas fa-cog\"></i>\r\n            Account Settings\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'rewards' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('rewards')}\r\n          >\r\n            <i className=\"fas fa-gift\"></i>\r\n            Rewards\r\n          </button>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        <div className=\"tab-content\">\r\n          {activeTab === 'overview' && (\r\n            <div className=\"overview-content\">\r\n              <div className=\"overview-grid\">\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-leaf\"></i>\r\n                      Eco Impact\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"eco-stats\">\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.co2Saved} kg</div>\r\n                      <div className=\"eco-label\">CO₂ Saved</div>\r\n                    </div>\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.ecoProducts}</div>\r\n                      <div className=\"eco-label\">Eco Products</div>\r\n                    </div>\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.treesPlanted}</div>\r\n                      <div className=\"eco-label\">Trees Planted</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-clock\"></i>\r\n                      Recent Activity\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"activity-list\">\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-shopping-cart activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Ordered Solar Power Bank</div>\r\n                        <div className=\"activity-date\">2 days ago</div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-heart activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Added Bamboo Toothbrush to wishlist</div>\r\n                        <div className=\"activity-date\">1 week ago</div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-star activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Reviewed Organic Cotton T-Shirt</div>\r\n                        <div className=\"activity-date\">2 weeks ago</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-trophy\"></i>\r\n                      Achievements\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"achievements\">\r\n                    <div className=\"achievement earned\">\r\n                      <i className=\"fas fa-seedling\"></i>\r\n                      <span>Eco Warrior</span>\r\n                    </div>\r\n                    <div className=\"achievement earned\">\r\n                      <i className=\"fas fa-shopping-bag\"></i>\r\n                      <span>First Purchase</span>\r\n                    </div>\r\n                    <div className=\"achievement\">\r\n                      <i className=\"fas fa-users\"></i>\r\n                      <span>Referral Master</span>\r\n                    </div>\r\n                    <div className=\"achievement\">\r\n                      <i className=\"fas fa-calendar\"></i>\r\n                      <span>Monthly Shopper</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'orders' && (\r\n            <div className=\"orders-content\">\r\n              <div className=\"orders-header\">\r\n                <h3>Order History</h3>\r\n                <div className=\"order-filters\">\r\n                  <select className=\"filter-select\">\r\n                    <option value=\"all\">All Orders</option>\r\n                    <option value=\"delivered\">Delivered</option>\r\n                    <option value=\"shipped\">Shipped</option>\r\n                    <option value=\"processing\">Processing</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"orders-list\">\r\n                {orders.map(order => (\r\n                  <OrderCard\r\n                    key={order.id}\r\n                    order={order}\r\n                    onReorder={handleReorder}\r\n                    onReview={handleReview}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'settings' && (\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-header\">\r\n                <h3>Account Settings</h3>\r\n                <button\r\n                  onClick={() => setEditMode(!editMode)}\r\n                  className={`edit-btn ${editMode ? 'active' : ''}`}\r\n                >\r\n                  <i className={`fas ${editMode ? 'fa-times' : 'fa-edit'}`}></i>\r\n                  {editMode ? 'Cancel' : 'Edit Profile'}\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h4>Profile Picture</h4>\r\n                  <div className=\"profile-picture-section\">\r\n                    <div className=\"current-picture\">\r\n                      <div className=\"picture-preview\">\r\n                        {profileImage ? (\r\n                          <img src={profileImage} alt=\"Profile\" />\r\n                        ) : (\r\n                          <div className=\"picture-placeholder\">\r\n                            {getInitials(profile.name)}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"picture-info\">\r\n                        <p>Your profile picture is visible to other users</p>\r\n                        <button\r\n                          onClick={() => setShowImageUpload(true)}\r\n                          className=\"change-picture-btn\"\r\n                        >\r\n                          <i className=\"fas fa-camera\"></i>\r\n                          Change Picture\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Personal Information</h4>\r\n                  <div className=\"form-row\">\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"name\">Full Name</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"name\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"email\">Email Address</label>\r\n                      <input\r\n                        type=\"email\"\r\n                        id=\"email\"\r\n                        name=\"email\"\r\n                        value={formData.email}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"phone\">Phone Number</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      id=\"phone\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      disabled={!editMode}\r\n                      placeholder=\"(*************\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Shipping Address</h4>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"address.street\">Street Address</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"address.street\"\r\n                      name=\"address.street\"\r\n                      value={formData.address.street}\r\n                      onChange={handleInputChange}\r\n                      disabled={!editMode}\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-row\">\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.city\">City</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.city\"\r\n                        name=\"address.city\"\r\n                        value={formData.address.city}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.state\">State</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.state\"\r\n                        name=\"address.state\"\r\n                        value={formData.address.state}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.zipCode\">ZIP Code</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.zipCode\"\r\n                        name=\"address.zipCode\"\r\n                        value={formData.address.zipCode}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Preferences</h4>\r\n                  <div className=\"preferences-grid\">\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.newsletter\"\r\n                        checked={formData.preferences.newsletter}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Newsletter</strong>\r\n                        <p>Receive updates about new eco-friendly products</p>\r\n                      </div>\r\n                    </label>\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.notifications\"\r\n                        checked={formData.preferences.notifications}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Order Notifications</strong>\r\n                        <p>Get notified about order status updates</p>\r\n                      </div>\r\n                    </label>\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.ecoTips\"\r\n                        checked={formData.preferences.ecoTips}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Eco Tips</strong>\r\n                        <p>Receive sustainability tips and advice</p>\r\n                      </div>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                {editMode && (\r\n                  <div className=\"form-actions\">\r\n                    <button onClick={() => setEditMode(false)} className=\"btn-secondary\">\r\n                      Cancel\r\n                    </button>\r\n                    <button onClick={handleSaveProfile} className=\"btn-primary\">\r\n                      <i className=\"fas fa-save\"></i>\r\n                      Save Changes\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'rewards' && (\r\n            <div className=\"rewards-content\">\r\n              <div className=\"rewards-header\">\r\n                <h3>Loyalty Rewards</h3>\r\n                <div className=\"points-balance\">\r\n                  <span className=\"points-value\">{profile.points || 0}</span>\r\n                  <span className=\"points-label\">Available Points</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"rewards-grid\">\r\n                <div className=\"reward-card\">\r\n                  <div className=\"reward-icon\">\r\n                    <i className=\"fas fa-percentage\"></i>\r\n                  </div>\r\n                  <h4>10% Off Next Order</h4>\r\n                  <p>Use your points to get a discount on your next purchase</p>\r\n                  <div className=\"reward-cost\">100 points</div>\r\n                  <button\r\n                    className=\"redeem-btn\"\r\n                    disabled={(profile.points || 0) < 100}\r\n                  >\r\n                    Redeem\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"reward-card\">\r\n                  <div className=\"reward-icon\">\r\n                    <i className=\"fas fa-shipping-fast\"></i>\r\n                  </div>\r\n                  <h4>Free Shipping</h4>\r\n                  <p>Get free shipping on any order, regardless of amount</p>\r\n                  <div className=\"reward-cost\">50 points</div>\r\n                  <button\r\n                    className=\"redeem-btn\"\r\n                    disabled={(profile.points || 0) < 50}\r\n                  >\r\n                    Redeem\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"reward-card\">\r\n                  <div className=\"reward-icon\">\r\n                    <i className=\"fas fa-gift\"></i>\r\n                  </div>\r\n                  <h4>Mystery Eco Box</h4>\r\n                  <p>Receive a curated box of sustainable products</p>\r\n                  <div className=\"reward-cost\">500 points</div>\r\n                  <button\r\n                    className=\"redeem-btn\"\r\n                    disabled={(profile.points || 0) < 500}\r\n                  >\r\n                    Redeem\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"reward-card\">\r\n                  <div className=\"reward-icon\">\r\n                    <i className=\"fas fa-tree\"></i>\r\n                  </div>\r\n                  <h4>Plant a Tree</h4>\r\n                  <p>We'll plant a tree in your name to offset carbon emissions</p>\r\n                  <div className=\"reward-cost\">200 points</div>\r\n                  <button\r\n                    className=\"redeem-btn\"\r\n                    disabled={(profile.points || 0) < 200}\r\n                  >\r\n                    Redeem\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"earn-points\">\r\n                <h4>How to Earn More Points</h4>\r\n                <div className=\"earn-methods\">\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-shopping-cart\"></i>\r\n                    <div>\r\n                      <strong>Make Purchases</strong>\r\n                      <p>Earn 1 point for every $1 spent</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-star\"></i>\r\n                    <div>\r\n                      <strong>Write Reviews</strong>\r\n                      <p>Get 10 points for each product review</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-users\"></i>\r\n                    <div>\r\n                      <strong>Refer Friends</strong>\r\n                      <p>Earn 50 points for each successful referral</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-birthday-cake\"></i>\r\n                    <div>\r\n                      <strong>Birthday Bonus</strong>\r\n                      <p>Get 100 points on your birthday</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Logout Button */}\r\n        <div className=\"profile-footer\">\r\n          <button onClick={logout} className=\"logout-btn\">\r\n            <i className=\"fas fa-sign-out-alt\"></i>\r\n            Sign Out\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Image Upload Modal */}\r\n      {showImageUpload && (\r\n        <ImageUpload\r\n          currentImage={profileImage}\r\n          onImageChange={handleImageChange}\r\n          onClose={() => setShowImageUpload(false)}\r\n        />\r\n      )}\r\n\r\n      <style jsx>{`\r\n        .profile-container {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n          padding: 2rem;\r\n        }\r\n\r\n        .auth-required {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-height: 60vh;\r\n          text-align: center;\r\n          gap: 1.5rem;\r\n        }\r\n\r\n        .auth-icon {\r\n          font-size: 4rem;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .login-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 1rem 2rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .profile-header {\r\n          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\r\n          color: white;\r\n          padding: 2rem;\r\n          border-radius: 1rem;\r\n          display: grid;\r\n          grid-template-columns: auto 1fr auto;\r\n          gap: 2rem;\r\n          align-items: center;\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .profile-avatar-container {\r\n          position: relative;\r\n          display: inline-block;\r\n        }\r\n\r\n        .profile-avatar {\r\n          width: 100px;\r\n          height: 100px;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          overflow: hidden;\r\n          border: 4px solid rgba(255, 255, 255, 0.3);\r\n          transition: all 0.3s ease;\r\n        }\r\n\r\n        .profile-avatar img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .avatar-initials {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          width: 100%;\r\n          height: 100%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n          color: white;\r\n        }\r\n\r\n        .edit-avatar-btn {\r\n          position: absolute;\r\n          bottom: 0;\r\n          right: 0;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: 2px solid white;\r\n          border-radius: 50%;\r\n          width: 36px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          font-size: 0.875rem;\r\n          transition: all 0.2s ease;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        .edit-avatar-btn:hover {\r\n          background: var(--primary-dark);\r\n          transform: scale(1.1);\r\n        }\r\n\r\n        .profile-avatar-container:hover .profile-avatar {\r\n          transform: scale(1.05);\r\n          border-color: rgba(255, 255, 255, 0.5);\r\n        }\r\n\r\n        .profile-info h1 {\r\n          margin: 0 0 0.5rem 0;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n        }\r\n\r\n        .profile-email {\r\n          opacity: 0.9;\r\n          margin: 0 0 1rem 0;\r\n        }\r\n\r\n        .loyalty-badge {\r\n          display: inline-flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          padding: 0.5rem 1rem;\r\n          border-radius: 9999px;\r\n          font-weight: 600;\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .profile-stats {\r\n          display: flex;\r\n          gap: 2rem;\r\n        }\r\n\r\n        .stat {\r\n          text-align: center;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 0.875rem;\r\n          opacity: 0.9;\r\n          margin-top: 0.25rem;\r\n        }\r\n\r\n        .loyalty-progress {\r\n          background: white;\r\n          padding: 1.5rem;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .progress-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 1rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .progress-bar {\r\n          height: 8px;\r\n          background: var(--bg-secondary);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-bottom: 0.75rem;\r\n        }\r\n\r\n        .progress-fill {\r\n          height: 100%;\r\n          transition: width 0.3s ease;\r\n        }\r\n\r\n        .progress-text {\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n          margin: 0;\r\n        }\r\n\r\n        .profile-tabs {\r\n          display: flex;\r\n          gap: 0.5rem;\r\n          margin-bottom: 2rem;\r\n          background: white;\r\n          padding: 0.5rem;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .tab {\r\n          flex: 1;\r\n          background: none;\r\n          border: none;\r\n          padding: 1rem;\r\n          border-radius: 0.75rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          color: var(--text-secondary);\r\n          transition: all 0.2s;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .tab:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .tab.active {\r\n          background: var(--primary-color);\r\n          color: white;\r\n        }\r\n\r\n        .tab-content {\r\n          background: white;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          overflow: hidden;\r\n        }\r\n\r\n        .overview-grid {\r\n          display: grid;\r\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n          gap: 2rem;\r\n          padding: 2rem;\r\n        }\r\n\r\n        .overview-card {\r\n          background: var(--bg-secondary);\r\n          border-radius: 1rem;\r\n          padding: 1.5rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .card-header {\r\n          margin-bottom: 1.5rem;\r\n        }\r\n\r\n        .card-header h3 {\r\n          margin: 0;\r\n          font-size: 1.25rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .eco-stats {\r\n          display: grid;\r\n          grid-template-columns: repeat(3, 1fr);\r\n          gap: 1rem;\r\n        }\r\n\r\n        .eco-stat {\r\n          text-align: center;\r\n        }\r\n\r\n        .eco-value {\r\n          font-size: 1.5rem;\r\n          font-weight: 700;\r\n          color: var(--secondary-color);\r\n          line-height: 1;\r\n        }\r\n\r\n        .eco-label {\r\n          font-size: 0.875rem;\r\n          color: var(--text-secondary);\r\n          margin-top: 0.25rem;\r\n        }\r\n\r\n        .activity-list {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 1rem;\r\n        }\r\n\r\n        .activity-item {\r\n          display: flex;\r\n          gap: 1rem;\r\n          align-items: flex-start;\r\n        }\r\n\r\n        .activity-icon {\r\n          width: 40px;\r\n          height: 40px;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .activity-text {\r\n          font-weight: 500;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .activity-date {\r\n          font-size: 0.875rem;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .achievements {\r\n          display: grid;\r\n          grid-template-columns: repeat(2, 1fr);\r\n          gap: 1rem;\r\n        }\r\n\r\n        .achievement {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          padding: 1rem;\r\n          border-radius: 0.75rem;\r\n          border: 2px solid var(--border-color);\r\n          opacity: 0.5;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .achievement.earned {\r\n          opacity: 1;\r\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\r\n          color: white;\r\n          border-color: var(--secondary-color);\r\n        }\r\n\r\n        .achievement i {\r\n          font-size: 1.25rem;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .profile-header {\r\n            grid-template-columns: 1fr;\r\n            text-align: center;\r\n            gap: 1.5rem;\r\n          }\r\n\r\n          .profile-avatar-container {\r\n            align-self: center;\r\n            justify-self: center;\r\n          }\r\n\r\n          .profile-avatar {\r\n            width: 120px;\r\n            height: 120px;\r\n          }\r\n\r\n          .edit-avatar-btn {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: 1rem;\r\n          }\r\n\r\n          .profile-stats {\r\n            justify-content: center;\r\n          }\r\n\r\n          .profile-tabs {\r\n            flex-direction: column;\r\n          }\r\n\r\n          .overview-grid {\r\n            grid-template-columns: 1fr;\r\n            padding: 1rem;\r\n          }\r\n\r\n          .current-picture {\r\n            flex-direction: column;\r\n            text-align: center;\r\n            gap: 1rem;\r\n          }\r\n        }\r\n\r\n        /* Profile Picture Section Styles */\r\n        .profile-picture-section {\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .current-picture {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 2rem;\r\n          padding: 1.5rem;\r\n          background: var(--bg-secondary);\r\n          border-radius: 1rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .picture-preview {\r\n          width: 100px;\r\n          height: 100px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n          border: 3px solid var(--border-color);\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .picture-preview img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .picture-placeholder {\r\n          width: 100%;\r\n          height: 100%;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n        }\r\n\r\n        .picture-info {\r\n          flex: 1;\r\n        }\r\n\r\n        .picture-info p {\r\n          margin: 0 0 1rem 0;\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .change-picture-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 0.75rem 1.5rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .change-picture-btn:hover {\r\n          background: var(--primary-dark);\r\n        }\r\n      `}</style>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default ProfilePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGjB,UAAU,CAACG,WAAW,CAAC;EACvD,MAAM;IAAEe,SAAS;IAAEC,MAAM;IAAEC;EAAa,CAAC,GAAGpB,UAAU,CAACI,gBAAgB,CAAC;EACxE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,sDAAsD;IACnEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,qBAAqB;EAC/E,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,sDAAsD;IACnEC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,aAAa;IACpBE,QAAQ,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,4BAA4B;EAChF,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,0DAA0D;IACvEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,YAAY;IACnBG,OAAO,EAAE,KAAK;IACdD,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,uBAAuB;EAC1E,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,6DAA6D;IAC1EC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,sBAAsB;IAC7BE,QAAQ,EAAE,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,gBAAgB;EACxE,CAAC,CACF;EAEDzD,SAAS,CAAC,MAAM;IACd,IAAIc,KAAK,EAAE;MACT6C,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLpC,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,mCAAmC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACX,KAAK,CAAC,CAAC;EAEX,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,yCAAyC,EAAE;QAC1EC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUjD,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFO,UAAU,CAACuC,QAAQ,CAACI,IAAI,CAAC;MACzB/B,eAAe,CAAC2B,QAAQ,CAACI,IAAI,CAAChC,YAAY,IAAI,IAAI,CAAC;MACnDG,WAAW,CAAC;QACVC,IAAI,EAAEwB,QAAQ,CAACI,IAAI,CAAC5B,IAAI,IAAI,EAAE;QAC9BC,KAAK,EAAEuB,QAAQ,CAACI,IAAI,CAAC3B,KAAK,IAAI,EAAE;QAChCC,KAAK,EAAEsB,QAAQ,CAACI,IAAI,CAAC1B,KAAK,IAAI,EAAE;QAChCC,OAAO,EAAEqB,QAAQ,CAACI,IAAI,CAACzB,OAAO,IAAI;UAChCC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE;QACX,CAAC;QACDC,WAAW,EAAEe,QAAQ,CAACI,IAAI,CAACnB,WAAW,IAAI;UACxCC,UAAU,EAAE,IAAI;UAChBC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZxC,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE/B,IAAI;MAAEmB,KAAK;MAAED,IAAI;MAAEc;IAAQ,CAAC,GAAGD,CAAC,CAACE,MAAM;IAE/C,IAAIjC,IAAI,CAACkC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGpC,IAAI,CAACqC,KAAK,CAAC,GAAG,CAAC;MACvCtC,WAAW,CAACuC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACH,MAAM,GAAG;UACR,GAAGG,IAAI,CAACH,MAAM,CAAC;UACf,CAACC,KAAK,GAAGlB,IAAI,KAAK,UAAU,GAAGc,OAAO,GAAGb;QAC3C;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLpB,WAAW,CAACuC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACtC,IAAI,GAAGkB,IAAI,KAAK,UAAU,GAAGc,OAAO,GAAGb;MAC1C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACAtD,UAAU,CAAC;QAAE,GAAGD,OAAO;QAAE,GAAGc,QAAQ;QAAEF;MAAa,CAAC,CAAC;MACrDH,WAAW,CAAC,KAAK,CAAC;MAClB;IACF,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZxC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMmD,iBAAiB,GAAIC,QAAQ,IAAK;IACtC5C,eAAe,CAAC4C,QAAQ,CAAC;IACzB;IACAxD,UAAU,CAACqD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,YAAY,EAAE6C;IAAS,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,WAAW,GAAI1C,IAAI,IAAK;IAC5B,OAAOA,IAAI,CACRqC,KAAK,CAAC,GAAG,CAAC,CACVM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAID,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClC,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC9E,IAAIH,MAAM,IAAI,GAAG,EAAE,OAAO;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACzE,IAAIH,MAAM,IAAI,GAAG,EAAE,OAAO;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAC;IAC1E,OAAO;MAAEF,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAC;EACzD,CAAC;EAED,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC/B;IACAC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,KAAK,CAAC;EACnC,CAAC;EAED,MAAMG,YAAY,GAAIH,KAAK,IAAK;IAC9B;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,KAAK,CAAC;EAC3C,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI;MACF,MAAM7E,YAAY,CAAC6E,MAAM,CAAC3C,IAAI,CAAC;MAC/B4C,KAAK,CAAC,0BAA0BD,MAAM,CAAC7C,KAAK,EAAE,CAAC;IACjD,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdyE,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;EAED,IAAI3E,OAAO,EAAE;IACX,oBAAOX,OAAA,CAACP,cAAc;MAAC8F,IAAI,EAAC,OAAO;MAACC,OAAO,EAAC,yBAAyB;MAACC,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrF;EAEA,IAAIhF,KAAK,EAAE;IACT,oBAAOb,OAAA,CAACN,YAAY;MAAC8C,KAAK,EAAC,eAAe;MAACgD,OAAO,EAAE3E,KAAM;MAACiF,OAAO,EAAE9C;IAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtF;EAEA,IAAI,CAACpF,OAAO,EAAE;IACZ,oBACET,OAAA;MAAA+F,QAAA,eACE/F,OAAA;QAAKgG,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC5B/F,OAAA;UAAKgG,SAAS,EAAC,WAAW;UAAAD,QAAA,eACxB/F,OAAA;YAAGgG,SAAS,EAAC;UAAkB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACN7F,OAAA;UAAA+F,QAAA,EAAI;QAAuB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChC7F,OAAA;UAAA+F,QAAA,EAAG;QAAkC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzC7F,OAAA;UAAQiG,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;UAACJ,SAAS,EAAC,WAAW;UAAAD,QAAA,gBAC3E/F,OAAA;YAAGgG,SAAS,EAAC;UAAoB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,MAAMQ,WAAW,GAAG3B,eAAe,CAACpE,SAAS,CAACgG,aAAa,IAAI,CAAC,CAAC;EAEjE,oBACEtG,OAAA;IAAA+F,QAAA,gBACE/F,OAAA;MAAKgG,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAEhC/F,OAAA;QAAKgG,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7B/F,OAAA;UAAKgG,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACvC/F,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC5B1E,YAAY,gBACXrB,OAAA;cAAKuG,GAAG,EAAElF,YAAa;cAACmF,GAAG,EAAE/F,OAAO,CAACgB;YAAK;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7C7F,OAAA;cAAKgG,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC7B5B,WAAW,CAAC1D,OAAO,CAACgB,IAAI;YAAC;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN7F,OAAA;YACEgG,SAAS,EAAC,iBAAiB;YAC3BC,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC,IAAI,CAAE;YACxCoB,KAAK,EAAC,wBAAwB;YAAAuD,QAAA,eAE9B/F,OAAA;cAAGgG,SAAS,EAAC;YAAe;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7F,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3B/F,OAAA;YAAA+F,QAAA,GAAI,gBAAc,EAACtF,OAAO,CAACgB,IAAI,EAAC,GAAC;UAAA;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC7F,OAAA;YAAGgG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAEtF,OAAO,CAACiB;UAAK;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD7F,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAACS,KAAK,EAAE;cAAEC,eAAe,EAAEL,WAAW,CAACxB;YAAM,CAAE;YAAAkB,QAAA,gBAC3E/F,OAAA;cAAGgG,SAAS,EAAC;YAAc;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/BQ,WAAW,CAACzB,KAAK,EAAC,SACrB;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7F,OAAA;UAAKgG,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5B/F,OAAA;YAAKgG,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB/F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAEzF,SAAS,CAACgG;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3D7F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN7F,OAAA;YAAKgG,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB/F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAEzF,SAAS,CAACqG;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzD7F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN7F,OAAA;YAAKgG,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB/F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,GAAC,GAAC,EAACzF,SAAS,CAACsG,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpE7F,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLQ,WAAW,CAACvB,IAAI,iBACf9E,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/B/F,OAAA;UAAKgG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9B/F,OAAA;YAAA+F,QAAA,GAAM,cAAY,EAACM,WAAW,CAACvB,IAAI,KAAK,IAAI,GAAG,UAAU,GAAGuB,WAAW,CAACvB,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,QAAQ;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChH7F,OAAA;YAAA+F,QAAA,GAAOzF,SAAS,CAACgG,aAAa,EAAC,KAAG,EAACD,WAAW,CAACvB,IAAI,EAAC,SAAO;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACN7F,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3B/F,OAAA;YACEgG,SAAS,EAAC,eAAe;YACzBS,KAAK,EAAE;cACLK,KAAK,EAAE,GAAIxG,SAAS,CAACgG,aAAa,GAAGD,WAAW,CAACvB,IAAI,GAAI,GAAG,GAAG;cAC/D4B,eAAe,EAAEL,WAAW,CAACxB;YAC/B;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7F,OAAA;UAAGgG,SAAS,EAAC,eAAe;UAAAD,QAAA,GAAC,OACtB,EAACM,WAAW,CAACvB,IAAI,GAAGxE,SAAS,CAACgG,aAAa,EAAC,uCACnD;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN,eAGD7F,OAAA;QAAKgG,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B/F,OAAA;UACEgG,SAAS,EAAE,OAAOjF,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,UAAU,CAAE;UAAA+E,QAAA,gBAExC/F,OAAA;YAAGgG,SAAS,EAAC;UAAmB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,YAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7F,OAAA;UACEgG,SAAS,EAAE,OAAOjF,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3DkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,QAAQ,CAAE;UAAA+E,QAAA,gBAEtC/F,OAAA;YAAGgG,SAAS,EAAC;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7F,OAAA;UACEgG,SAAS,EAAE,OAAOjF,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,UAAU,CAAE;UAAA+E,QAAA,gBAExC/F,OAAA;YAAGgG,SAAS,EAAC;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7F,OAAA;UACEgG,SAAS,EAAE,OAAOjF,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC5DkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,SAAS,CAAE;UAAA+E,QAAA,gBAEvC/F,OAAA;YAAGgG,SAAS,EAAC;UAAa;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN7F,OAAA;QAAKgG,SAAS,EAAC,aAAa;QAAAD,QAAA,GACzBhF,SAAS,KAAK,UAAU,iBACvBf,OAAA;UAAKgG,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC/B/F,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5B/F,OAAA;cAAKgG,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAa;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,cAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxB/F,OAAA;kBAAKgG,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvB/F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,GAAEzF,SAAS,CAACyG,SAAS,CAACC,QAAQ,EAAC,KAAG;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClE7F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvB/F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAEzF,SAAS,CAACyG,SAAS,CAACE;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE7F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAY;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvB/F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAEzF,SAAS,CAACyG,SAAS,CAACG;kBAAY;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnE7F,OAAA;oBAAKgG,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAc;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,mBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5B/F,OAAA;kBAAKgG,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5B/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAoC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtD7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAwB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7D7F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5B/F,OAAA;oBAAGgG,SAAS,EAAC;kBAA4B;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9C7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAmC;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxE7F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5B/F,OAAA;oBAAGgG,SAAS,EAAC;kBAA2B;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7C7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAA+B;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpE7F,OAAA;sBAAKgG,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAC;oBAAW;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAe;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,gBAEnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3B/F,OAAA;kBAAKgG,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjC/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAiB;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnC7F,OAAA;oBAAA+F,QAAA,EAAM;kBAAW;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjC/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAqB;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvC7F,OAAA;oBAAA+F,QAAA,EAAM;kBAAc;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1B/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAc;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChC7F,OAAA;oBAAA+F,QAAA,EAAM;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1B/F,OAAA;oBAAGgG,SAAS,EAAC;kBAAiB;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnC7F,OAAA;oBAAA+F,QAAA,EAAM;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9E,SAAS,KAAK,QAAQ,iBACrBf,OAAA;UAAKgG,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B/F,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5B/F,OAAA;cAAA+F,QAAA,EAAI;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB7F,OAAA;cAAKgG,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B/F,OAAA;gBAAQgG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC/B/F,OAAA;kBAAQ4C,KAAK,EAAC,KAAK;kBAAAmD,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC7F,OAAA;kBAAQ4C,KAAK,EAAC,WAAW;kBAAAmD,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C7F,OAAA;kBAAQ4C,KAAK,EAAC,SAAS;kBAAAmD,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC7F,OAAA;kBAAQ4C,KAAK,EAAC,YAAY;kBAAAmD,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7F,OAAA;YAAKgG,SAAS,EAAC,aAAa;YAAAD,QAAA,EACzBxF,MAAM,CAAC6D,GAAG,CAACY,KAAK,iBACfhF,OAAA,CAACJ,SAAS;cAERoF,KAAK,EAAEA,KAAM;cACbmC,SAAS,EAAEpC,aAAc;cACzBqC,QAAQ,EAAEjC;YAAa,GAHlBH,KAAK,CAACzC,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAId,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9E,SAAS,KAAK,UAAU,iBACvBf,OAAA;UAAKgG,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC/B/F,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9B/F,OAAA;cAAA+F,QAAA,EAAI;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB7F,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtC+E,SAAS,EAAE,YAAY/E,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAA8E,QAAA,gBAElD/F,OAAA;gBAAGgG,SAAS,EAAE,OAAO/E,QAAQ,GAAG,UAAU,GAAG,SAAS;cAAG;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC7D5E,QAAQ,GAAG,QAAQ,GAAG,cAAc;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7F,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5B/F,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAA+F,QAAA,EAAI;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB7F,OAAA;gBAAKgG,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,eACtC/F,OAAA;kBAAKgG,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B/F,OAAA;oBAAKgG,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,EAC7B1E,YAAY,gBACXrB,OAAA;sBAAKuG,GAAG,EAAElF,YAAa;sBAACmF,GAAG,EAAC;oBAAS;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAExC7F,OAAA;sBAAKgG,SAAS,EAAC,qBAAqB;sBAAAD,QAAA,EACjC5B,WAAW,CAAC1D,OAAO,CAACgB,IAAI;oBAAC;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN7F,OAAA;oBAAKgG,SAAS,EAAC,cAAc;oBAAAD,QAAA,gBAC3B/F,OAAA;sBAAA+F,QAAA,EAAG;oBAA8C;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrD7F,OAAA;sBACEiG,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC,IAAI,CAAE;sBACxC4E,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,gBAE9B/F,OAAA;wBAAGgG,SAAS,EAAC;sBAAe;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,kBAEnC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAA+F,QAAA,EAAI;cAAoB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B7F,OAAA;gBAAKgG,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvB/F,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB/F,OAAA;oBAAOqH,OAAO,EAAC,MAAM;oBAAAtB,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvC7F,OAAA;oBACE2C,IAAI,EAAC,MAAM;oBACXJ,EAAE,EAAC,MAAM;oBACTd,IAAI,EAAC,MAAM;oBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;oBACrB6F,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB/F,OAAA;oBAAOqH,OAAO,EAAC,OAAO;oBAAAtB,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5C7F,OAAA;oBACE2C,IAAI,EAAC,OAAO;oBACZJ,EAAE,EAAC,OAAO;oBACVd,IAAI,EAAC,OAAO;oBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;oBACtB4F,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB/F,OAAA;kBAAOqH,OAAO,EAAC,OAAO;kBAAAtB,QAAA,EAAC;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3C7F,OAAA;kBACE2C,IAAI,EAAC,KAAK;kBACVJ,EAAE,EAAC,OAAO;kBACVd,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACI,KAAM;kBACtB2F,QAAQ,EAAE/D,iBAAkB;kBAC5BgE,QAAQ,EAAE,CAACtG,QAAS;kBACpBuG,WAAW,EAAC;gBAAgB;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAA+F,QAAA,EAAI;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB7F,OAAA;gBAAKgG,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB/F,OAAA;kBAAOqH,OAAO,EAAC,gBAAgB;kBAAAtB,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD7F,OAAA;kBACE2C,IAAI,EAAC,MAAM;kBACXJ,EAAE,EAAC,gBAAgB;kBACnBd,IAAI,EAAC,gBAAgB;kBACrBmB,KAAK,EAAErB,QAAQ,CAACK,OAAO,CAACC,MAAO;kBAC/ByF,QAAQ,EAAE/D,iBAAkB;kBAC5BgE,QAAQ,EAAE,CAACtG;gBAAS;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvB/F,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB/F,OAAA;oBAAOqH,OAAO,EAAC,cAAc;oBAAAtB,QAAA,EAAC;kBAAI;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1C7F,OAAA;oBACE2C,IAAI,EAAC,MAAM;oBACXJ,EAAE,EAAC,cAAc;oBACjBd,IAAI,EAAC,cAAc;oBACnBmB,KAAK,EAAErB,QAAQ,CAACK,OAAO,CAACE,IAAK;oBAC7BwF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB/F,OAAA;oBAAOqH,OAAO,EAAC,eAAe;oBAAAtB,QAAA,EAAC;kBAAK;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5C7F,OAAA;oBACE2C,IAAI,EAAC,MAAM;oBACXJ,EAAE,EAAC,eAAe;oBAClBd,IAAI,EAAC,eAAe;oBACpBmB,KAAK,EAAErB,QAAQ,CAACK,OAAO,CAACG,KAAM;oBAC9BuF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7F,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB/F,OAAA;oBAAOqH,OAAO,EAAC,iBAAiB;oBAAAtB,QAAA,EAAC;kBAAQ;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjD7F,OAAA;oBACE2C,IAAI,EAAC,MAAM;oBACXJ,EAAE,EAAC,iBAAiB;oBACpBd,IAAI,EAAC,iBAAiB;oBACtBmB,KAAK,EAAErB,QAAQ,CAACK,OAAO,CAACI,OAAQ;oBAChCsF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAA+F,QAAA,EAAI;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB7F,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/B/F,OAAA;kBAAOgG,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC/B/F,OAAA;oBACE2C,IAAI,EAAC,UAAU;oBACflB,IAAI,EAAC,wBAAwB;oBAC7BgC,OAAO,EAAElC,QAAQ,CAACW,WAAW,CAACC,UAAW;oBACzCmF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF7F,OAAA;oBAAMgG,SAAS,EAAC;kBAAW;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnC7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAA+F,QAAA,EAAQ;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3B7F,OAAA;sBAAA+F,QAAA,EAAG;oBAA+C;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACR7F,OAAA;kBAAOgG,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC/B/F,OAAA;oBACE2C,IAAI,EAAC,UAAU;oBACflB,IAAI,EAAC,2BAA2B;oBAChCgC,OAAO,EAAElC,QAAQ,CAACW,WAAW,CAACE,aAAc;oBAC5CkF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF7F,OAAA;oBAAMgG,SAAS,EAAC;kBAAW;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnC7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAA+F,QAAA,EAAQ;oBAAmB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpC7F,OAAA;sBAAA+F,QAAA,EAAG;oBAAuC;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACR7F,OAAA;kBAAOgG,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC/B/F,OAAA;oBACE2C,IAAI,EAAC,UAAU;oBACflB,IAAI,EAAC,qBAAqB;oBAC1BgC,OAAO,EAAElC,QAAQ,CAACW,WAAW,CAACG,OAAQ;oBACtCiF,QAAQ,EAAE/D,iBAAkB;oBAC5BgE,QAAQ,EAAE,CAACtG;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF7F,OAAA;oBAAMgG,SAAS,EAAC;kBAAW;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnC7F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAA+F,QAAA,EAAQ;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzB7F,OAAA;sBAAA+F,QAAA,EAAG;oBAAsC;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL5E,QAAQ,iBACPjB,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAQiG,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,KAAK,CAAE;gBAAC8E,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAErE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7F,OAAA;gBAAQiG,OAAO,EAAEjC,iBAAkB;gBAACgC,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBACzD/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9E,SAAS,KAAK,SAAS,iBACtBf,OAAA;UAAKgG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9B/F,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC7B/F,OAAA;cAAA+F,QAAA,EAAI;YAAe;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB7F,OAAA;cAAKgG,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B/F,OAAA;gBAAMgG,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEtF,OAAO,CAACkE,MAAM,IAAI;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3D7F,OAAA;gBAAMgG,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7F,OAAA;YAAKgG,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3B/F,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAmB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACN7F,OAAA;gBAAA+F,QAAA,EAAI;cAAkB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B7F,OAAA;gBAAA+F,QAAA,EAAG;cAAuD;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9D7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7C7F,OAAA;gBACEgG,SAAS,EAAC,YAAY;gBACtBuB,QAAQ,EAAE,CAAC9G,OAAO,CAACkE,MAAM,IAAI,CAAC,IAAI,GAAI;gBAAAoB,QAAA,EACvC;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAsB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACN7F,OAAA;gBAAA+F,QAAA,EAAI;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB7F,OAAA;gBAAA+F,QAAA,EAAG;cAAoD;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3D7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5C7F,OAAA;gBACEgG,SAAS,EAAC,YAAY;gBACtBuB,QAAQ,EAAE,CAAC9G,OAAO,CAACkE,MAAM,IAAI,CAAC,IAAI,EAAG;gBAAAoB,QAAA,EACtC;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACN7F,OAAA;gBAAA+F,QAAA,EAAI;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB7F,OAAA;gBAAA+F,QAAA,EAAG;cAA6C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpD7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7C7F,OAAA;gBACEgG,SAAS,EAAC,YAAY;gBACtBuB,QAAQ,EAAE,CAAC9G,OAAO,CAACkE,MAAM,IAAI,CAAC,IAAI,GAAI;gBAAAoB,QAAA,EACvC;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7F,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACN7F,OAAA;gBAAA+F,QAAA,EAAI;cAAY;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB7F,OAAA;gBAAA+F,QAAA,EAAG;cAA0D;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjE7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7C7F,OAAA;gBACEgG,SAAS,EAAC,YAAY;gBACtBuB,QAAQ,EAAE,CAAC9G,OAAO,CAACkE,MAAM,IAAI,CAAC,IAAI,GAAI;gBAAAoB,QAAA,EACvC;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7F,OAAA;YAAKgG,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B/F,OAAA;cAAA+F,QAAA,EAAI;YAAuB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC7F,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3B/F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAsB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxC7F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAA+F,QAAA,EAAQ;kBAAc;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/B7F,OAAA;oBAAA+F,QAAA,EAAG;kBAA+B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/B7F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAA+F,QAAA,EAAQ;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B7F,OAAA;oBAAA+F,QAAA,EAAG;kBAAqC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAc;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChC7F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAA+F,QAAA,EAAQ;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B7F,OAAA;oBAAA+F,QAAA,EAAG;kBAA2C;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7F,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1B/F,OAAA;kBAAGgG,SAAS,EAAC;gBAAsB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxC7F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAA+F,QAAA,EAAQ;kBAAc;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/B7F,OAAA;oBAAA+F,QAAA,EAAG;kBAA+B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7F,OAAA;QAAKgG,SAAS,EAAC,gBAAgB;QAAAD,QAAA,eAC7B/F,OAAA;UAAQiG,OAAO,EAAE5F,MAAO;UAAC2F,SAAS,EAAC,YAAY;UAAAD,QAAA,gBAC7C/F,OAAA;YAAGgG,SAAS,EAAC;UAAqB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,YAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1E,eAAe,iBACdnB,OAAA,CAACL,WAAW;MACV8H,YAAY,EAAEpG,YAAa;MAC3BqG,aAAa,EAAEzD,iBAAkB;MACjC0D,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAAC,KAAK;IAAE;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACF,eAED7F,OAAA;MAAO4H,GAAG;MAAA7B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX;AAAC3F,EAAA,CAhsCQD,WAAW;AAAA4H,EAAA,GAAX5H,WAAW;AAksCpB,eAAeA,WAAW;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}