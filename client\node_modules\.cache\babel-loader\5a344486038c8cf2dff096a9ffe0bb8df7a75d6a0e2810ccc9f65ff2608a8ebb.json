{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\WishlistPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { WishlistContext } from '../context/WishlistContext';\nimport { CartContext } from '../context/CartContext';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WishlistPage() {\n  _s();\n  const {\n    wishlist,\n    removeFromWishlist,\n    clearWishlist\n  } = useContext(WishlistContext);\n  const {\n    addToCart\n  } = useContext(CartContext);\n  const handleAddToCart = product => {\n    addToCart(product);\n    // Optionally remove from wishlist after adding to cart\n    // removeFromWishlist(product._id);\n  };\n  if (wishlist.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-wishlist\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"far fa-heart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Your wishlist is empty\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Save items you love to your wishlist and shop them later\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"shop-now-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-shopping-bag\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), \"Start Shopping\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        jsx: true,\n        children: `\n          .empty-wishlist {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            min-height: 60vh;\n            text-align: center;\n            padding: 2rem;\n          }\n\n          .empty-icon {\n            font-size: 4rem;\n            color: #d1d5db;\n            margin-bottom: 1.5rem;\n          }\n\n          .empty-wishlist h2 {\n            font-size: 1.75rem;\n            font-weight: 600;\n            color: var(--text-primary);\n            margin: 0 0 0.5rem 0;\n          }\n\n          .empty-wishlist p {\n            color: var(--text-secondary);\n            margin: 0 0 2rem 0;\n            font-size: 1.125rem;\n          }\n\n          .shop-now-btn {\n            background: var(--primary-color);\n            color: white;\n            text-decoration: none;\n            padding: 0.875rem 2rem;\n            border-radius: 0.5rem;\n            font-weight: 500;\n            display: flex;\n            align-items: center;\n            gap: 0.5rem;\n            transition: all 0.2s;\n          }\n\n          .shop-now-btn:hover {\n            background: var(--primary-dark);\n            transform: translateY(-1px);\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"wishlist-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-heart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), \"My Wishlist\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"wishlist-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"item-count\",\n          children: [wishlist.length, \" item\", wishlist.length !== 1 ? 's' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearWishlist,\n          className: \"clear-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-trash\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), \"Clear All\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"wishlist-grid\",\n      children: wishlist.map(product => {\n        var _product$description, _product$tags;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"wishlist-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-image\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.imageUrl,\n              alt: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"remove-btn\",\n              onClick: () => removeFromWishlist(product._id),\n              title: \"Remove from wishlist\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-times\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"product-description\",\n              children: [(_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.substring(0, 100), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-tags\",\n              children: (_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.slice(0, 2).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tag\",\n                children: tag\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), product.isEcoFriendly && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"eco-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-leaf\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), \"Eco-Friendly\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price\",\n              children: [\"$\", product.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: `/product/${product._id}`,\n                className: \"view-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-eye\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), \"View Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAddToCart(product),\n                className: \"add-to-cart-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), \"Add to Cart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, product._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .wishlist-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 2px solid var(--border-color);\n        }\n\n        .wishlist-header h1 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0;\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .wishlist-header h1 i {\n          color: #ef4444;\n        }\n\n        .wishlist-actions {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .item-count {\n          color: var(--text-secondary);\n          font-weight: 500;\n        }\n\n        .clear-btn {\n          background: #ef4444;\n          color: white;\n          border: none;\n          padding: 0.5rem 1rem;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n          transition: all 0.2s;\n        }\n\n        .clear-btn:hover {\n          background: #dc2626;\n        }\n\n        .wishlist-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 2rem;\n        }\n\n        .wishlist-item {\n          background: white;\n          border-radius: 1rem;\n          overflow: hidden;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          transition: all 0.3s ease;\n        }\n\n        .wishlist-item:hover {\n          transform: translateY(-2px);\n          box-shadow: var(--shadow-md);\n        }\n\n        .product-image {\n          position: relative;\n          height: 200px;\n          overflow: hidden;\n        }\n\n        .product-image img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        .wishlist-item:hover .product-image img {\n          transform: scale(1.05);\n        }\n\n        .remove-btn {\n          position: absolute;\n          top: 0.75rem;\n          right: 0.75rem;\n          background: rgba(239, 68, 68, 0.9);\n          color: white;\n          border: none;\n          border-radius: 50%;\n          width: 32px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .remove-btn:hover {\n          background: #dc2626;\n          transform: scale(1.1);\n        }\n\n        .product-info {\n          padding: 1.5rem;\n        }\n\n        .product-info h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 0.5rem 0;\n        }\n\n        .product-description {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin: 0 0 1rem 0;\n          line-height: 1.5;\n        }\n\n        .product-tags {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 1rem;\n          flex-wrap: wrap;\n        }\n\n        .tag {\n          background: #f3f4f6;\n          color: #374151;\n          padding: 0.25rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .eco-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.25rem;\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          margin-bottom: 1rem;\n        }\n\n        .price {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          margin-bottom: 1.5rem;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 0.75rem;\n        }\n\n        .view-btn {\n          flex: 1;\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          text-decoration: none;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .view-btn:hover {\n          background: var(--border-color);\n        }\n\n        .add-to-cart-btn {\n          flex: 1.5;\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .add-to-cart-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .wishlist-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .wishlist-grid {\n            grid-template-columns: 1fr;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_s(WishlistPage, \"D1ggxBt2Iw/KFyVHCJAmNwCk+SI=\");\n_c = WishlistPage;\nexport default WishlistPage;\nvar _c;\n$RefreshReg$(_c, \"WishlistPage\");", "map": {"version": 3, "names": ["React", "useContext", "WishlistContext", "CartContext", "Link", "jsxDEV", "_jsxDEV", "WishlistPage", "_s", "wishlist", "removeFromWishlist", "clearWishlist", "addToCart", "handleAddToCart", "product", "length", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "jsx", "onClick", "map", "_product$description", "_product$tags", "src", "imageUrl", "alt", "name", "_id", "title", "description", "substring", "tags", "slice", "tag", "index", "isEcoFriendly", "price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/WishlistPage.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\nimport { WishlistContext } from '../context/WishlistContext';\nimport { CartContext } from '../context/CartContext';\nimport { Link } from 'react-router-dom';\n\nfunction WishlistPage() {\n  const { wishlist, removeFromWishlist, clearWishlist } = useContext(WishlistContext);\n  const { addToCart } = useContext(CartContext);\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    // Optionally remove from wishlist after adding to cart\n    // removeFromWishlist(product._id);\n  };\n\n  if (wishlist.length === 0) {\n    return (\n      <main>\n        <div className=\"empty-wishlist\">\n          <div className=\"empty-icon\">\n            <i className=\"far fa-heart\"></i>\n          </div>\n          <h2>Your wishlist is empty</h2>\n          <p>Save items you love to your wishlist and shop them later</p>\n          <Link to=\"/\" className=\"shop-now-btn\">\n            <i className=\"fas fa-shopping-bag\"></i>\n            Start Shopping\n          </Link>\n        </div>\n\n        <style jsx>{`\n          .empty-wishlist {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            min-height: 60vh;\n            text-align: center;\n            padding: 2rem;\n          }\n\n          .empty-icon {\n            font-size: 4rem;\n            color: #d1d5db;\n            margin-bottom: 1.5rem;\n          }\n\n          .empty-wishlist h2 {\n            font-size: 1.75rem;\n            font-weight: 600;\n            color: var(--text-primary);\n            margin: 0 0 0.5rem 0;\n          }\n\n          .empty-wishlist p {\n            color: var(--text-secondary);\n            margin: 0 0 2rem 0;\n            font-size: 1.125rem;\n          }\n\n          .shop-now-btn {\n            background: var(--primary-color);\n            color: white;\n            text-decoration: none;\n            padding: 0.875rem 2rem;\n            border-radius: 0.5rem;\n            font-weight: 500;\n            display: flex;\n            align-items: center;\n            gap: 0.5rem;\n            transition: all 0.2s;\n          }\n\n          .shop-now-btn:hover {\n            background: var(--primary-dark);\n            transform: translateY(-1px);\n          }\n        `}</style>\n      </main>\n    );\n  }\n\n  return (\n    <main>\n      <div className=\"wishlist-header\">\n        <h1>\n          <i className=\"fas fa-heart\"></i>\n          My Wishlist\n        </h1>\n        <div className=\"wishlist-actions\">\n          <span className=\"item-count\">{wishlist.length} item{wishlist.length !== 1 ? 's' : ''}</span>\n          <button onClick={clearWishlist} className=\"clear-btn\">\n            <i className=\"fas fa-trash\"></i>\n            Clear All\n          </button>\n        </div>\n      </div>\n\n      <div className=\"wishlist-grid\">\n        {wishlist.map(product => (\n          <div key={product._id} className=\"wishlist-item\">\n            <div className=\"product-image\">\n              <img src={product.imageUrl} alt={product.name} />\n              <button \n                className=\"remove-btn\"\n                onClick={() => removeFromWishlist(product._id)}\n                title=\"Remove from wishlist\"\n              >\n                <i className=\"fas fa-times\"></i>\n              </button>\n            </div>\n            \n            <div className=\"product-info\">\n              <h3>{product.name}</h3>\n              <p className=\"product-description\">\n                {product.description?.substring(0, 100)}...\n              </p>\n              \n              <div className=\"product-tags\">\n                {product.tags?.slice(0, 2).map((tag, index) => (\n                  <span key={index} className=\"tag\">\n                    {tag}\n                  </span>\n                ))}\n              </div>\n              \n              {product.isEcoFriendly && (\n                <div className=\"eco-badge\">\n                  <i className=\"fas fa-leaf\"></i>\n                  Eco-Friendly\n                </div>\n              )}\n              \n              <div className=\"price\">${product.price}</div>\n              \n              <div className=\"item-actions\">\n                <Link to={`/product/${product._id}`} className=\"view-btn\">\n                  <i className=\"fas fa-eye\"></i>\n                  View Details\n                </Link>\n                <button \n                  onClick={() => handleAddToCart(product)}\n                  className=\"add-to-cart-btn\"\n                >\n                  <i className=\"fas fa-shopping-cart\"></i>\n                  Add to Cart\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <style jsx>{`\n        .wishlist-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 2px solid var(--border-color);\n        }\n\n        .wishlist-header h1 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0;\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .wishlist-header h1 i {\n          color: #ef4444;\n        }\n\n        .wishlist-actions {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .item-count {\n          color: var(--text-secondary);\n          font-weight: 500;\n        }\n\n        .clear-btn {\n          background: #ef4444;\n          color: white;\n          border: none;\n          padding: 0.5rem 1rem;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n          transition: all 0.2s;\n        }\n\n        .clear-btn:hover {\n          background: #dc2626;\n        }\n\n        .wishlist-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 2rem;\n        }\n\n        .wishlist-item {\n          background: white;\n          border-radius: 1rem;\n          overflow: hidden;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          transition: all 0.3s ease;\n        }\n\n        .wishlist-item:hover {\n          transform: translateY(-2px);\n          box-shadow: var(--shadow-md);\n        }\n\n        .product-image {\n          position: relative;\n          height: 200px;\n          overflow: hidden;\n        }\n\n        .product-image img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        .wishlist-item:hover .product-image img {\n          transform: scale(1.05);\n        }\n\n        .remove-btn {\n          position: absolute;\n          top: 0.75rem;\n          right: 0.75rem;\n          background: rgba(239, 68, 68, 0.9);\n          color: white;\n          border: none;\n          border-radius: 50%;\n          width: 32px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .remove-btn:hover {\n          background: #dc2626;\n          transform: scale(1.1);\n        }\n\n        .product-info {\n          padding: 1.5rem;\n        }\n\n        .product-info h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 0.5rem 0;\n        }\n\n        .product-description {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin: 0 0 1rem 0;\n          line-height: 1.5;\n        }\n\n        .product-tags {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 1rem;\n          flex-wrap: wrap;\n        }\n\n        .tag {\n          background: #f3f4f6;\n          color: #374151;\n          padding: 0.25rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .eco-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.25rem;\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          margin-bottom: 1rem;\n        }\n\n        .price {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          margin-bottom: 1.5rem;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 0.75rem;\n        }\n\n        .view-btn {\n          flex: 1;\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          text-decoration: none;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .view-btn:hover {\n          background: var(--border-color);\n        }\n\n        .add-to-cart-btn {\n          flex: 1.5;\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .add-to-cart-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .wishlist-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .wishlist-grid {\n            grid-template-columns: 1fr;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default WishlistPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC;EAAc,CAAC,GAAGV,UAAU,CAACC,eAAe,CAAC;EACnF,MAAM;IAAEU;EAAU,CAAC,GAAGX,UAAU,CAACE,WAAW,CAAC;EAE7C,MAAMU,eAAe,GAAIC,OAAO,IAAK;IACnCF,SAAS,CAACE,OAAO,CAAC;IAClB;IACA;EACF,CAAC;EAED,IAAIL,QAAQ,CAACM,MAAM,KAAK,CAAC,EAAE;IACzB,oBACET,OAAA;MAAAU,QAAA,gBACEV,OAAA;QAAKW,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BV,OAAA;UAAKW,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzBV,OAAA;YAAGW,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNf,OAAA;UAAAU,QAAA,EAAI;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/Bf,OAAA;UAAAU,QAAA,EAAG;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/Df,OAAA,CAACF,IAAI;UAACkB,EAAE,EAAC,GAAG;UAACL,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACnCV,OAAA;YAAGW,SAAS,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENf,OAAA;QAAOiB,GAAG;QAAAP,QAAA,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEX;EAEA,oBACEf,OAAA;IAAAU,QAAA,gBACEV,OAAA;MAAKW,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC9BV,OAAA;QAAAU,QAAA,gBACEV,OAAA;UAAGW,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLf,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BV,OAAA;UAAMW,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAEP,QAAQ,CAACM,MAAM,EAAC,OAAK,EAACN,QAAQ,CAACM,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5Ff,OAAA;UAAQkB,OAAO,EAAEb,aAAc;UAACM,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACnDV,OAAA;YAAGW,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,aAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAD,QAAA,EAC3BP,QAAQ,CAACgB,GAAG,CAACX,OAAO;QAAA,IAAAY,oBAAA,EAAAC,aAAA;QAAA,oBACnBrB,OAAA;UAAuBW,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC9CV,OAAA;YAAKW,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5BV,OAAA;cAAKsB,GAAG,EAAEd,OAAO,CAACe,QAAS;cAACC,GAAG,EAAEhB,OAAO,CAACiB;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDf,OAAA;cACEW,SAAS,EAAC,YAAY;cACtBO,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAACI,OAAO,CAACkB,GAAG,CAAE;cAC/CC,KAAK,EAAC,sBAAsB;cAAAjB,QAAA,eAE5BV,OAAA;gBAAGW,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENf,OAAA;YAAKW,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3BV,OAAA;cAAAU,QAAA,EAAKF,OAAO,CAACiB;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvBf,OAAA;cAAGW,SAAS,EAAC,qBAAqB;cAAAD,QAAA,IAAAU,oBAAA,GAC/BZ,OAAO,CAACoB,WAAW,cAAAR,oBAAA,uBAAnBA,oBAAA,CAAqBS,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAC1C;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJf,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAD,QAAA,GAAAW,aAAA,GAC1Bb,OAAO,CAACsB,IAAI,cAAAT,aAAA,uBAAZA,aAAA,CAAcU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACZ,GAAG,CAAC,CAACa,GAAG,EAAEC,KAAK,kBACxCjC,OAAA;gBAAkBW,SAAS,EAAC,KAAK;gBAAAD,QAAA,EAC9BsB;cAAG,GADKC,KAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELP,OAAO,CAAC0B,aAAa,iBACpBlC,OAAA;cAAKW,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxBV,OAAA;gBAAGW,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,gBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAEDf,OAAA;cAAKW,SAAS,EAAC,OAAO;cAAAD,QAAA,GAAC,GAAC,EAACF,OAAO,CAAC2B,KAAK;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE7Cf,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3BV,OAAA,CAACF,IAAI;gBAACkB,EAAE,EAAE,YAAYR,OAAO,CAACkB,GAAG,EAAG;gBAACf,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvDV,OAAA;kBAAGW,SAAS,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAEhC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPf,OAAA;gBACEkB,OAAO,EAAEA,CAAA,KAAMX,eAAe,CAACC,OAAO,CAAE;gBACxCG,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAE3BV,OAAA;kBAAGW,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhDEP,OAAO,CAACkB,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiDhB,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENf,OAAA;MAAOiB,GAAG;MAAAP,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX;AAACb,EAAA,CArXQD,YAAY;AAAAmC,EAAA,GAAZnC,YAAY;AAuXrB,eAAeA,YAAY;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}