{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport { types as t, type NodePath } from \"@babel/core\";\n// https://crbug.com/v8/11558\n\n// check if there is a spread element followed by another argument.\n// (...[], 0) or (...[], ...[])\n\nfunction matchAffectedArguments(argumentNodes: t.CallExpression[\"arguments\"]) {\n  const spreadIndex = argumentNodes.findIndex(node => t.isSpreadElement(node));\n  return spreadIndex >= 0 && spreadIndex !== argumentNodes.length - 1;\n}\n\n/**\n * Check whether the optional chain is affected by https://crbug.com/v8/11558.\n * This routine MUST not manipulate NodePath\n *\n * @export\n * @param {(NodePath<t.OptionalMemberExpression | t.OptionalCallExpression>)} path\n * @returns {boolean}\n */\nexport function shouldTransform(\n  path: NodePath<t.OptionalMemberExpression | t.OptionalCallExpression>,\n): boolean {\n  let optionalPath: NodePath<t.Expression> = path;\n  const chains: (t.OptionalCallExpression | t.OptionalMemberExpression)[] = [];\n  for (;;) {\n    if (optionalPath.isOptionalMemberExpression()) {\n      chains.push(optionalPath.node);\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"object\"));\n    } else if (optionalPath.isOptionalCallExpression()) {\n      chains.push(optionalPath.node);\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"callee\"));\n    } else {\n      break;\n    }\n  }\n  for (let i = 0; i < chains.length; i++) {\n    const node = chains[i];\n    if (\n      t.isOptionalCallExpression(node) &&\n      matchAffectedArguments(node.arguments)\n    ) {\n      // f?.(...[], 0)\n      if (node.optional) {\n        return true;\n      }\n      // o?.m(...[], 0)\n      // when node.optional is false, chains[i + 1] is always well defined\n      const callee = chains[i + 1];\n      if (t.isOptionalMemberExpression(callee, { optional: true })) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { transform } from \"@babel/plugin-transform-optional-chaining\";\nimport { shouldTransform } from \"./util.ts\";\nimport type { NodePath, types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? false;\n  const pureGetters = api.assumption(\"pureGetters\") ?? false;\n\n  return {\n    name: \"bugfix-v8-spread-parameters-in-optional-chaining\",\n\n    visitor: {\n      \"OptionalCallExpression|OptionalMemberExpression\"(\n        path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n      ) {\n        if (shouldTransform(path)) {\n          transform(path, { noDocumentAll, pureGetters });\n        }\n      },\n    },\n  };\n});\n"], "names": ["matchAffectedArguments", "argumentNodes", "spreadIndex", "findIndex", "node", "t", "isSpreadElement", "length", "shouldTransform", "path", "optionalPath", "chains", "isOptionalMemberExpression", "push", "skipTransparentExprWrappers", "get", "isOptionalCallExpression", "i", "arguments", "optional", "callee", "declare", "api", "_api$assumption", "_api$assumption2", "assertVersion", "noDocumentAll", "assumption", "pureGetters", "name", "visitor", "OptionalCallExpression|OptionalMemberExpression", "transform"], "mappings": ";;;;;;;;;AAOA,SAASA,sBAAsBA,CAACC,aAA4C,EAAE;AAC5E,EAAA,MAAMC,WAAW,GAAGD,aAAa,CAACE,SAAS,CAACC,IAAI,IAAIC,UAAC,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC,CAAA;EAC5E,OAAOF,WAAW,IAAI,CAAC,IAAIA,WAAW,KAAKD,aAAa,CAACM,MAAM,GAAG,CAAC,CAAA;AACrE,CAAA;AAUO,SAASC,eAAeA,CAC7BC,IAAqE,EAC5D;EACT,IAAIC,YAAoC,GAAGD,IAAI,CAAA;EAC/C,MAAME,MAAiE,GAAG,EAAE,CAAA;EAC5E,SAAS;AACP,IAAA,IAAID,YAAY,CAACE,0BAA0B,EAAE,EAAE;AAC7CD,MAAAA,MAAM,CAACE,IAAI,CAACH,YAAY,CAACN,IAAI,CAAC,CAAA;MAC9BM,YAAY,GAAGI,mEAA2B,CAACJ,YAAY,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAC,MAAM,IAAIL,YAAY,CAACM,wBAAwB,EAAE,EAAE;AAClDL,MAAAA,MAAM,CAACE,IAAI,CAACH,YAAY,CAACN,IAAI,CAAC,CAAA;MAC9BM,YAAY,GAAGI,mEAA2B,CAACJ,YAAY,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAC,MAAM;AACL,MAAA,MAAA;AACF,KAAA;AACF,GAAA;AACA,EAAA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACJ,MAAM,EAAEU,CAAC,EAAE,EAAE;AACtC,IAAA,MAAMb,IAAI,GAAGO,MAAM,CAACM,CAAC,CAAC,CAAA;AACtB,IAAA,IACEZ,UAAC,CAACW,wBAAwB,CAACZ,IAAI,CAAC,IAChCJ,sBAAsB,CAACI,IAAI,CAACc,SAAS,CAAC,EACtC;MAEA,IAAId,IAAI,CAACe,QAAQ,EAAE;AACjB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAGA,MAAA,MAAMC,MAAM,GAAGT,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,CAAA;AAC5B,MAAA,IAAIZ,UAAC,CAACO,0BAA0B,CAACQ,MAAM,EAAE;AAAED,QAAAA,QAAQ,EAAE,IAAA;AAAK,OAAC,CAAC,EAAE;AAC5D,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd;;AClDA,YAAeE,yBAAO,CAACC,GAAG,IAAI;EAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;AAC5BF,EAAAA,GAAG,CAACG,aAAa,CAAkB,CAAE,CAAC,CAAA;AAEtC,EAAA,MAAMC,aAAa,GAAA,CAAAH,eAAA,GAAGD,GAAG,CAACK,UAAU,CAAC,eAAe,CAAC,KAAAJ,IAAAA,GAAAA,eAAA,GAAI,KAAK,CAAA;AAC9D,EAAA,MAAMK,WAAW,GAAA,CAAAJ,gBAAA,GAAGF,GAAG,CAACK,UAAU,CAAC,aAAa,CAAC,KAAAH,IAAAA,GAAAA,gBAAA,GAAI,KAAK,CAAA;EAE1D,OAAO;AACLK,IAAAA,IAAI,EAAE,kDAAkD;AAExDC,IAAAA,OAAO,EAAE;MACP,iDAAiDC,CAC/CtB,IAAqE,EACrE;AACA,QAAA,IAAID,eAAe,CAACC,IAAI,CAAC,EAAE;UACzBuB,yCAAS,CAACvB,IAAI,EAAE;YAAEiB,aAAa;AAAEE,YAAAA,WAAAA;AAAY,WAAC,CAAC,CAAA;AACjD,SAAA;AACF,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}