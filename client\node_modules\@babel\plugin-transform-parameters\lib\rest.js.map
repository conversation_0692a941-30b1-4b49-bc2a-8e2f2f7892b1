{"version": 3, "names": ["_core", "require", "_shadowUtils", "buildRest", "template", "statement", "restIndex", "expression", "restIndexImpure", "restLength", "referencesRest", "path", "state", "node", "name", "scope", "bindingIdentifierEquals", "outerBinding", "memberExpressionOptimisationVisitor", "<PERSON><PERSON>", "skip", "Flow", "isTypeCastExpression", "Function", "oldNoOptimise", "noOptimise", "traverse", "ReferencedIdentifier", "deopted", "parentPath", "<PERSON><PERSON><PERSON>", "key", "offset", "isMemberExpression", "object", "grandparent<PERSON><PERSON>", "argsOptEligible", "isAssignmentExpression", "left", "isLVal", "isForXStatement", "isUpdateExpression", "isUnaryExpression", "operator", "isCallExpression", "isNewExpression", "callee", "computed", "get", "isBaseType", "candidates", "push", "cause", "property", "isSpreadElement", "call", "arguments", "length", "references", "BindingIdentifier", "getParamsCount", "count", "params", "t", "isIdentifier", "hasRest", "isRestElement", "optimiseIndexGetter", "argsId", "offsetLiteral", "numericLiteral", "index", "parent", "isNumericLiteral", "value", "binaryExpression", "cloneNode", "isPure", "temp", "generateUidIdentifierBasedOnNode", "id", "kind", "replaceWith", "ARGUMENTS", "OFFSET", "INDEX", "REF", "replaced<PERSON><PERSON>nt<PERSON><PERSON>", "offsetTestPath", "valRes", "evaluate", "confident", "buildUndefinedNode", "optimiseLengthGetter", "convertFunctionRest", "restPath", "shadowedP<PERSON><PERSON>", "Set", "collectShadowedParamsNames", "needsIIFE", "size", "needsOuterBinding", "iifeVisitor", "ensureBlock", "set", "blockStatement", "buildScopeIIFE", "body", "rest", "pop", "isPattern", "pattern", "generateUidIdentifier", "declar", "variableDeclaration", "variableDeclarator", "unshift", "rename", "identifier", "paramsCount", "argumentsNode", "getBindingIdentifier", "clonedArgsId", "map", "start", "len", "a<PERSON><PERSON><PERSON>", "arr<PERSON>en", "conditionalExpression", "loop", "ARRAY_KEY", "ARRAY_LEN", "START", "ARRAY", "KEY", "LEN", "target", "getEarliestCommonAncestorFrom", "getStatementParent", "findParent", "isLoop", "isFunction", "insertBefore"], "sources": ["../src/rest.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { NodePath, Visitor } from \"@babel/core\";\n\nimport {\n  iifeVisitor,\n  collectShadowedParamsNames,\n  buildScopeIIFE,\n} from \"./shadow-utils.ts\";\n\nconst buildRest = template.statement(`\n  for (var LEN = ARGUMENTS.length,\n           ARRAY = new Array(ARRAY_LEN),\n           KEY = START;\n       KEY < LEN;\n       KEY++) {\n    ARRAY[ARRAY_KEY] = ARGUMENTS[KEY];\n  }\n`);\n\nconst restIndex = template.expression(`\n  (INDEX < OFFSET || ARGUMENTS.length <= INDEX) ? undefined : ARGUMENTS[INDEX]\n`);\n\nconst restIndexImpure = template.expression(`\n  REF = INDEX, (REF < OFFSET || ARGUMENTS.length <= REF) ? undefined : ARGUMENTS[REF]\n`);\n\nconst restLength = template.expression(`\n  ARGUMENTS.length <= OFFSET ? 0 : ARGUMENTS.length - OFFSET\n`);\n\nfunction referencesRest(\n  path: NodePath<t.Identifier | t.JSXIdentifier>,\n  state: State,\n) {\n  if (path.node.name === state.name) {\n    // Check rest parameter is not shadowed by a binding in another scope.\n    return path.scope.bindingIdentifierEquals(state.name, state.outerBinding);\n  }\n\n  return false;\n}\n\ntype Candidate = {\n  cause: \"argSpread\" | \"indexGetter\" | \"lengthGetter\";\n  path: NodePath<t.Identifier | t.JSXIdentifier>;\n};\n\ntype State = {\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  offset: number;\n\n  argumentsNode: t.Identifier;\n  outerBinding: t.Identifier;\n\n  // candidate member expressions we could optimise if there are no other references\n  candidates: Candidate[];\n\n  // local rest binding name\n  name: string;\n\n  /*\n  It may be possible to optimize the output code in certain ways, such as\n  not generating code to initialize an array (perhaps substituting direct\n  references to arguments[i] or arguments.length for reads of the\n  corresponding rest parameter property) or positioning the initialization\n  code so that it may not have to execute depending on runtime conditions.\n\n  This property tracks eligibility for optimization. \"deopted\" means give up\n  and don't perform optimization. For example, when any of rest's elements /\n  properties is assigned to at the top level, or referenced at all in a\n  nested function.\n  */\n  deopted: boolean;\n  noOptimise?: boolean;\n};\n\nconst memberExpressionOptimisationVisitor: Visitor<State> = {\n  Scope(path, state) {\n    // check if this scope has a local binding that will shadow the rest parameter\n    if (!path.scope.bindingIdentifierEquals(state.name, state.outerBinding)) {\n      path.skip();\n    }\n  },\n\n  Flow(path: NodePath<t.Flow>) {\n    // Do not skip TypeCastExpressions as the contain valid non flow code\n    if (path.isTypeCastExpression()) return;\n    // don't touch reference in type annotations\n    path.skip();\n  },\n\n  Function(path, state) {\n    // Detect whether any reference to rest is contained in nested functions to\n    // determine if deopt is necessary.\n    const oldNoOptimise = state.noOptimise;\n    state.noOptimise = true;\n    path.traverse(memberExpressionOptimisationVisitor, state);\n    state.noOptimise = oldNoOptimise;\n\n    // Skip because optimizing references to rest would refer to the `arguments`\n    // of the nested function.\n    path.skip();\n  },\n\n  ReferencedIdentifier(path, state) {\n    const { node } = path;\n\n    // we can't guarantee the purity of arguments\n    if (node.name === \"arguments\") {\n      state.deopted = true;\n    }\n\n    // is this a referenced identifier and is it referencing the rest parameter?\n    if (!referencesRest(path, state)) return;\n\n    if (state.noOptimise) {\n      state.deopted = true;\n    } else {\n      const { parentPath } = path;\n\n      // Is this identifier the right hand side of a default parameter?\n      if (\n        parentPath.listKey === \"params\" &&\n        (parentPath.key as number) < state.offset\n      ) {\n        return;\n      }\n\n      // ex: `args[0]`\n      // ex: `args.whatever`\n      if (parentPath.isMemberExpression({ object: node })) {\n        const grandparentPath = parentPath.parentPath;\n\n        const argsOptEligible =\n          !state.deopted &&\n          !(\n            // ex: `args[0] = \"whatever\"`\n            (\n              (grandparentPath.isAssignmentExpression() &&\n                parentPath.node === grandparentPath.node.left) ||\n              // ex: `[args[0]] = [\"whatever\"]`\n              grandparentPath.isLVal() ||\n              // ex: `for (rest[0] in this)`\n              // ex: `for (rest[0] of this)`\n              grandparentPath.isForXStatement() ||\n              // ex: `++args[0]`\n              // ex: `args[0]--`\n              grandparentPath.isUpdateExpression() ||\n              // ex: `delete args[0]`\n              grandparentPath.isUnaryExpression({ operator: \"delete\" }) ||\n              // ex: `args[0]()`\n              // ex: `new args[0]()`\n              // ex: `new args[0]`\n              ((grandparentPath.isCallExpression() ||\n                grandparentPath.isNewExpression()) &&\n                parentPath.node === grandparentPath.node.callee)\n            )\n          );\n\n        if (argsOptEligible) {\n          if (parentPath.node.computed) {\n            // if we know that this member expression is referencing a number then\n            // we can safely optimise it\n            if (parentPath.get(\"property\").isBaseType(\"number\")) {\n              state.candidates.push({ cause: \"indexGetter\", path });\n              return;\n            }\n          } else if (\n            // @ts-expect-error .length must not be a private name\n            parentPath.node.property.name === \"length\"\n          ) {\n            // args.length\n            state.candidates.push({ cause: \"lengthGetter\", path });\n            return;\n          }\n        }\n      }\n\n      // we can only do these optimizations if the rest variable would match\n      // the arguments exactly\n      // optimise single spread args in calls\n      // ex: fn(...args)\n      if (state.offset === 0 && parentPath.isSpreadElement()) {\n        const call = parentPath.parentPath;\n        if (call.isCallExpression() && call.node.arguments.length === 1) {\n          state.candidates.push({ cause: \"argSpread\", path });\n          return;\n        }\n      }\n\n      state.references.push(path);\n    }\n  },\n\n  /**\n   * Deopt on use of a binding identifier with the same name as our rest param.\n   *\n   * See https://github.com/babel/babel/issues/2091\n   */\n\n  BindingIdentifier(path, state) {\n    if (referencesRest(path, state)) {\n      state.deopted = true;\n    }\n  },\n};\n\nfunction getParamsCount(node: t.Function) {\n  let count = node.params.length;\n  // skip the first parameter if it is a TypeScript 'this parameter'\n  if (count > 0 && t.isIdentifier(node.params[0], { name: \"this\" })) {\n    count -= 1;\n  }\n  return count;\n}\n\nfunction hasRest(node: t.Function) {\n  const length = node.params.length;\n  return length > 0 && t.isRestElement(node.params[length - 1]);\n}\n\nfunction optimiseIndexGetter(\n  path: NodePath<t.Identifier | t.JSXIdentifier>,\n  argsId: t.Identifier,\n  offset: number,\n) {\n  const offsetLiteral = t.numericLiteral(offset);\n  let index;\n  const parent = path.parent as t.MemberExpression;\n\n  if (t.isNumericLiteral(parent.property)) {\n    index = t.numericLiteral(parent.property.value + offset);\n  } else if (offset === 0) {\n    // Avoid unnecessary '+ 0'\n    index = parent.property;\n  } else {\n    index = t.binaryExpression(\n      \"+\",\n      parent.property,\n      t.cloneNode(offsetLiteral),\n    );\n  }\n\n  const { scope, parentPath } = path;\n  if (!scope.isPure(index)) {\n    const temp = scope.generateUidIdentifierBasedOnNode(index);\n    scope.push({ id: temp, kind: \"var\" });\n    parentPath.replaceWith(\n      restIndexImpure({\n        ARGUMENTS: argsId,\n        OFFSET: offsetLiteral,\n        INDEX: index,\n        REF: t.cloneNode(temp),\n      }),\n    );\n  } else {\n    parentPath.replaceWith(\n      restIndex({\n        ARGUMENTS: argsId,\n        OFFSET: offsetLiteral,\n        INDEX: index,\n      }),\n    );\n    const replacedParentPath = parentPath as NodePath<t.ConditionalExpression>;\n\n    // See if we can statically evaluate the first test (i.e. index < offset)\n    // and optimize the AST accordingly.\n    const offsetTestPath = replacedParentPath.get(\n      \"test\",\n    ) as NodePath<t.BinaryExpression>;\n    const valRes = offsetTestPath.get(\"left\").evaluate();\n    if (valRes.confident) {\n      if (valRes.value === true) {\n        replacedParentPath.replaceWith(scope.buildUndefinedNode());\n      } else {\n        offsetTestPath.replaceWith(offsetTestPath.get(\"right\"));\n      }\n    }\n  }\n}\n\nfunction optimiseLengthGetter(\n  path: NodePath<t.Identifier | t.JSXIdentifier>,\n  argsId: t.Identifier,\n  offset: number,\n) {\n  if (offset) {\n    path.parentPath.replaceWith(\n      restLength({\n        ARGUMENTS: argsId,\n        OFFSET: t.numericLiteral(offset),\n      }),\n    );\n  } else {\n    path.replaceWith(argsId);\n  }\n}\n\nexport default function convertFunctionRest(path: NodePath<t.Function>) {\n  const { node, scope } = path;\n  if (!hasRest(node)) return false;\n\n  const restPath = path.get(\n    `params.${node.params.length - 1}.argument`,\n  ) as NodePath<t.ArrayPattern | t.ObjectPattern | t.Identifier>;\n\n  if (!restPath.isIdentifier()) {\n    const shadowedParams = new Set<string>();\n    collectShadowedParamsNames(restPath, path.scope, shadowedParams);\n\n    let needsIIFE = shadowedParams.size > 0;\n    if (!needsIIFE) {\n      const state = {\n        needsOuterBinding: false,\n        scope,\n      };\n      restPath.traverse(iifeVisitor, state);\n      needsIIFE = state.needsOuterBinding;\n    }\n\n    if (needsIIFE) {\n      path.ensureBlock();\n      path.set(\n        \"body\",\n        t.blockStatement([\n          buildScopeIIFE(shadowedParams, path.node.body as t.BlockStatement),\n        ]),\n      );\n    }\n  }\n\n  let rest = restPath.node;\n  node.params.pop(); // This returns 'rest'\n\n  if (t.isPattern(rest)) {\n    const pattern = rest;\n    rest = scope.generateUidIdentifier(\"ref\");\n\n    const declar = t.variableDeclaration(\"let\", [\n      t.variableDeclarator(pattern, rest),\n    ]);\n    path.ensureBlock();\n    (node.body as t.BlockStatement).body.unshift(declar);\n  } else if (rest.name === \"arguments\") {\n    scope.rename(rest.name);\n  }\n\n  const argsId = t.identifier(\"arguments\");\n  const paramsCount = getParamsCount(node);\n\n  // check and optimise for extremely common cases\n  const state: State = {\n    references: [],\n    offset: paramsCount,\n    argumentsNode: argsId,\n    outerBinding: scope.getBindingIdentifier(rest.name),\n    candidates: [],\n    name: rest.name,\n    deopted: false,\n  };\n\n  path.traverse(memberExpressionOptimisationVisitor, state);\n\n  // There are only \"shorthand\" references\n  if (!state.deopted && !state.references.length) {\n    for (const { path, cause } of state.candidates) {\n      const clonedArgsId = t.cloneNode(argsId);\n      switch (cause) {\n        case \"indexGetter\":\n          optimiseIndexGetter(path, clonedArgsId, state.offset);\n          break;\n        case \"lengthGetter\":\n          optimiseLengthGetter(path, clonedArgsId, state.offset);\n          break;\n        default:\n          path.replaceWith(clonedArgsId);\n      }\n    }\n    return true;\n  }\n\n  state.references.push(...state.candidates.map(({ path }) => path));\n\n  const start = t.numericLiteral(paramsCount);\n  const key = scope.generateUidIdentifier(\"key\");\n  const len = scope.generateUidIdentifier(\"len\");\n\n  let arrKey, arrLen;\n  if (paramsCount) {\n    // this method has additional params, so we need to subtract\n    // the index of the current argument position from the\n    // position in the array that we want to populate\n    arrKey = t.binaryExpression(\"-\", t.cloneNode(key), t.cloneNode(start));\n\n    // we need to work out the size of the array that we're\n    // going to store all the rest parameters\n    //\n    // we need to add a check to avoid constructing the array\n    // with <0 if there are less arguments than params as it'll\n    // cause an error\n    arrLen = t.conditionalExpression(\n      t.binaryExpression(\">\", t.cloneNode(len), t.cloneNode(start)),\n      t.binaryExpression(\"-\", t.cloneNode(len), t.cloneNode(start)),\n      t.numericLiteral(0),\n    );\n  } else {\n    arrKey = t.identifier(key.name);\n    arrLen = t.identifier(len.name);\n  }\n\n  const loop = buildRest({\n    ARGUMENTS: argsId,\n    ARRAY_KEY: arrKey,\n    ARRAY_LEN: arrLen,\n    START: start,\n    ARRAY: rest,\n    KEY: key,\n    LEN: len,\n  });\n\n  if (state.deopted) {\n    (node.body as t.BlockStatement).body.unshift(loop);\n  } else {\n    let target = path\n      .getEarliestCommonAncestorFrom(state.references)\n      .getStatementParent();\n\n    // don't perform the allocation inside a loop\n    target.findParent(path => {\n      if (path.isLoop()) {\n        target = path;\n      } else {\n        // Stop crawling up if this is a function.\n        return path.isFunction();\n      }\n    });\n\n    target.insertBefore(loop);\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,YAAA,GAAAD,OAAA;AAMA,MAAME,SAAS,GAAGC,cAAQ,CAACC,SAAS,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,SAAS,GAAGF,cAAQ,CAACG,UAAU,CAAC;AACtC;AACA,CAAC,CAAC;AAEF,MAAMC,eAAe,GAAGJ,cAAQ,CAACG,UAAU,CAAC;AAC5C;AACA,CAAC,CAAC;AAEF,MAAME,UAAU,GAAGL,cAAQ,CAACG,UAAU,CAAC;AACvC;AACA,CAAC,CAAC;AAEF,SAASG,cAAcA,CACrBC,IAA8C,EAC9CC,KAAY,EACZ;EACA,IAAID,IAAI,CAACE,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;IAEjC,OAAOH,IAAI,CAACI,KAAK,CAACC,uBAAuB,CAACJ,KAAK,CAACE,IAAI,EAAEF,KAAK,CAACK,YAAY,CAAC;EAC3E;EAEA,OAAO,KAAK;AACd;AAoCA,MAAMC,mCAAmD,GAAG;EAC1DC,KAAKA,CAACR,IAAI,EAAEC,KAAK,EAAE;IAEjB,IAAI,CAACD,IAAI,CAACI,KAAK,CAACC,uBAAuB,CAACJ,KAAK,CAACE,IAAI,EAAEF,KAAK,CAACK,YAAY,CAAC,EAAE;MACvEN,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAEDC,IAAIA,CAACV,IAAsB,EAAE;IAE3B,IAAIA,IAAI,CAACW,oBAAoB,CAAC,CAAC,EAAE;IAEjCX,IAAI,CAACS,IAAI,CAAC,CAAC;EACb,CAAC;EAEDG,QAAQA,CAACZ,IAAI,EAAEC,KAAK,EAAE;IAGpB,MAAMY,aAAa,GAAGZ,KAAK,CAACa,UAAU;IACtCb,KAAK,CAACa,UAAU,GAAG,IAAI;IACvBd,IAAI,CAACe,QAAQ,CAACR,mCAAmC,EAAEN,KAAK,CAAC;IACzDA,KAAK,CAACa,UAAU,GAAGD,aAAa;IAIhCb,IAAI,CAACS,IAAI,CAAC,CAAC;EACb,CAAC;EAEDO,oBAAoBA,CAAChB,IAAI,EAAEC,KAAK,EAAE;IAChC,MAAM;MAAEC;IAAK,CAAC,GAAGF,IAAI;IAGrB,IAAIE,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC7BF,KAAK,CAACgB,OAAO,GAAG,IAAI;IACtB;IAGA,IAAI,CAAClB,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC,EAAE;IAElC,IAAIA,KAAK,CAACa,UAAU,EAAE;MACpBb,KAAK,CAACgB,OAAO,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,MAAM;QAAEC;MAAW,CAAC,GAAGlB,IAAI;MAG3B,IACEkB,UAAU,CAACC,OAAO,KAAK,QAAQ,IAC9BD,UAAU,CAACE,GAAG,GAAcnB,KAAK,CAACoB,MAAM,EACzC;QACA;MACF;MAIA,IAAIH,UAAU,CAACI,kBAAkB,CAAC;QAAEC,MAAM,EAAErB;MAAK,CAAC,CAAC,EAAE;QACnD,MAAMsB,eAAe,GAAGN,UAAU,CAACA,UAAU;QAE7C,MAAMO,eAAe,GACnB,CAACxB,KAAK,CAACgB,OAAO,IACd,EAGKO,eAAe,CAACE,sBAAsB,CAAC,CAAC,IACvCR,UAAU,CAAChB,IAAI,KAAKsB,eAAe,CAACtB,IAAI,CAACyB,IAAI,IAE/CH,eAAe,CAACI,MAAM,CAAC,CAAC,IAGxBJ,eAAe,CAACK,eAAe,CAAC,CAAC,IAGjCL,eAAe,CAACM,kBAAkB,CAAC,CAAC,IAEpCN,eAAe,CAACO,iBAAiB,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC,IAIxD,CAACR,eAAe,CAACS,gBAAgB,CAAC,CAAC,IAClCT,eAAe,CAACU,eAAe,CAAC,CAAC,KACjChB,UAAU,CAAChB,IAAI,KAAKsB,eAAe,CAACtB,IAAI,CAACiC,MAAO,CAErD;QAEH,IAAIV,eAAe,EAAE;UACnB,IAAIP,UAAU,CAAChB,IAAI,CAACkC,QAAQ,EAAE;YAG5B,IAAIlB,UAAU,CAACmB,GAAG,CAAC,UAAU,CAAC,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;cACnDrC,KAAK,CAACsC,UAAU,CAACC,IAAI,CAAC;gBAAEC,KAAK,EAAE,aAAa;gBAAEzC;cAAK,CAAC,CAAC;cACrD;YACF;UACF,CAAC,MAAM,IAELkB,UAAU,CAAChB,IAAI,CAACwC,QAAQ,CAACvC,IAAI,KAAK,QAAQ,EAC1C;YAEAF,KAAK,CAACsC,UAAU,CAACC,IAAI,CAAC;cAAEC,KAAK,EAAE,cAAc;cAAEzC;YAAK,CAAC,CAAC;YACtD;UACF;QACF;MACF;MAMA,IAAIC,KAAK,CAACoB,MAAM,KAAK,CAAC,IAAIH,UAAU,CAACyB,eAAe,CAAC,CAAC,EAAE;QACtD,MAAMC,IAAI,GAAG1B,UAAU,CAACA,UAAU;QAClC,IAAI0B,IAAI,CAACX,gBAAgB,CAAC,CAAC,IAAIW,IAAI,CAAC1C,IAAI,CAAC2C,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;UAC/D7C,KAAK,CAACsC,UAAU,CAACC,IAAI,CAAC;YAAEC,KAAK,EAAE,WAAW;YAAEzC;UAAK,CAAC,CAAC;UACnD;QACF;MACF;MAEAC,KAAK,CAAC8C,UAAU,CAACP,IAAI,CAACxC,IAAI,CAAC;IAC7B;EACF,CAAC;EAQDgD,iBAAiBA,CAAChD,IAAI,EAAEC,KAAK,EAAE;IAC7B,IAAIF,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC,EAAE;MAC/BA,KAAK,CAACgB,OAAO,GAAG,IAAI;IACtB;EACF;AACF,CAAC;AAED,SAASgC,cAAcA,CAAC/C,IAAgB,EAAE;EACxC,IAAIgD,KAAK,GAAGhD,IAAI,CAACiD,MAAM,CAACL,MAAM;EAE9B,IAAII,KAAK,GAAG,CAAC,IAAIE,WAAC,CAACC,YAAY,CAACnD,IAAI,CAACiD,MAAM,CAAC,CAAC,CAAC,EAAE;IAAEhD,IAAI,EAAE;EAAO,CAAC,CAAC,EAAE;IACjE+C,KAAK,IAAI,CAAC;EACZ;EACA,OAAOA,KAAK;AACd;AAEA,SAASI,OAAOA,CAACpD,IAAgB,EAAE;EACjC,MAAM4C,MAAM,GAAG5C,IAAI,CAACiD,MAAM,CAACL,MAAM;EACjC,OAAOA,MAAM,GAAG,CAAC,IAAIM,WAAC,CAACG,aAAa,CAACrD,IAAI,CAACiD,MAAM,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC;AAC/D;AAEA,SAASU,mBAAmBA,CAC1BxD,IAA8C,EAC9CyD,MAAoB,EACpBpC,MAAc,EACd;EACA,MAAMqC,aAAa,GAAGN,WAAC,CAACO,cAAc,CAACtC,MAAM,CAAC;EAC9C,IAAIuC,KAAK;EACT,MAAMC,MAAM,GAAG7D,IAAI,CAAC6D,MAA4B;EAEhD,IAAIT,WAAC,CAACU,gBAAgB,CAACD,MAAM,CAACnB,QAAQ,CAAC,EAAE;IACvCkB,KAAK,GAAGR,WAAC,CAACO,cAAc,CAACE,MAAM,CAACnB,QAAQ,CAACqB,KAAK,GAAG1C,MAAM,CAAC;EAC1D,CAAC,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;IAEvBuC,KAAK,GAAGC,MAAM,CAACnB,QAAQ;EACzB,CAAC,MAAM;IACLkB,KAAK,GAAGR,WAAC,CAACY,gBAAgB,CACxB,GAAG,EACHH,MAAM,CAACnB,QAAQ,EACfU,WAAC,CAACa,SAAS,CAACP,aAAa,CAC3B,CAAC;EACH;EAEA,MAAM;IAAEtD,KAAK;IAAEc;EAAW,CAAC,GAAGlB,IAAI;EAClC,IAAI,CAACI,KAAK,CAAC8D,MAAM,CAACN,KAAK,CAAC,EAAE;IACxB,MAAMO,IAAI,GAAG/D,KAAK,CAACgE,gCAAgC,CAACR,KAAK,CAAC;IAC1DxD,KAAK,CAACoC,IAAI,CAAC;MAAE6B,EAAE,EAAEF,IAAI;MAAEG,IAAI,EAAE;IAAM,CAAC,CAAC;IACrCpD,UAAU,CAACqD,WAAW,CACpB1E,eAAe,CAAC;MACd2E,SAAS,EAAEf,MAAM;MACjBgB,MAAM,EAAEf,aAAa;MACrBgB,KAAK,EAAEd,KAAK;MACZe,GAAG,EAAEvB,WAAC,CAACa,SAAS,CAACE,IAAI;IACvB,CAAC,CACH,CAAC;EACH,CAAC,MAAM;IACLjD,UAAU,CAACqD,WAAW,CACpB5E,SAAS,CAAC;MACR6E,SAAS,EAAEf,MAAM;MACjBgB,MAAM,EAAEf,aAAa;MACrBgB,KAAK,EAAEd;IACT,CAAC,CACH,CAAC;IACD,MAAMgB,kBAAkB,GAAG1D,UAA+C;IAI1E,MAAM2D,cAAc,GAAGD,kBAAkB,CAACvC,GAAG,CAC3C,MACF,CAAiC;IACjC,MAAMyC,MAAM,GAAGD,cAAc,CAACxC,GAAG,CAAC,MAAM,CAAC,CAAC0C,QAAQ,CAAC,CAAC;IACpD,IAAID,MAAM,CAACE,SAAS,EAAE;MACpB,IAAIF,MAAM,CAACf,KAAK,KAAK,IAAI,EAAE;QACzBa,kBAAkB,CAACL,WAAW,CAACnE,KAAK,CAAC6E,kBAAkB,CAAC,CAAC,CAAC;MAC5D,CAAC,MAAM;QACLJ,cAAc,CAACN,WAAW,CAACM,cAAc,CAACxC,GAAG,CAAC,OAAO,CAAC,CAAC;MACzD;IACF;EACF;AACF;AAEA,SAAS6C,oBAAoBA,CAC3BlF,IAA8C,EAC9CyD,MAAoB,EACpBpC,MAAc,EACd;EACA,IAAIA,MAAM,EAAE;IACVrB,IAAI,CAACkB,UAAU,CAACqD,WAAW,CACzBzE,UAAU,CAAC;MACT0E,SAAS,EAAEf,MAAM;MACjBgB,MAAM,EAAErB,WAAC,CAACO,cAAc,CAACtC,MAAM;IACjC,CAAC,CACH,CAAC;EACH,CAAC,MAAM;IACLrB,IAAI,CAACuE,WAAW,CAACd,MAAM,CAAC;EAC1B;AACF;AAEe,SAAS0B,mBAAmBA,CAACnF,IAA0B,EAAE;EACtE,MAAM;IAAEE,IAAI;IAAEE;EAAM,CAAC,GAAGJ,IAAI;EAC5B,IAAI,CAACsD,OAAO,CAACpD,IAAI,CAAC,EAAE,OAAO,KAAK;EAEhC,MAAMkF,QAAQ,GAAGpF,IAAI,CAACqC,GAAG,CACvB,UAAUnC,IAAI,CAACiD,MAAM,CAACL,MAAM,GAAG,CAAC,WAClC,CAA8D;EAE9D,IAAI,CAACsC,QAAQ,CAAC/B,YAAY,CAAC,CAAC,EAAE;IAC5B,MAAMgC,cAAc,GAAG,IAAIC,GAAG,CAAS,CAAC;IACxC,IAAAC,uCAA0B,EAACH,QAAQ,EAAEpF,IAAI,CAACI,KAAK,EAAEiF,cAAc,CAAC;IAEhE,IAAIG,SAAS,GAAGH,cAAc,CAACI,IAAI,GAAG,CAAC;IACvC,IAAI,CAACD,SAAS,EAAE;MACd,MAAMvF,KAAK,GAAG;QACZyF,iBAAiB,EAAE,KAAK;QACxBtF;MACF,CAAC;MACDgF,QAAQ,CAACrE,QAAQ,CAAC4E,wBAAW,EAAE1F,KAAK,CAAC;MACrCuF,SAAS,GAAGvF,KAAK,CAACyF,iBAAiB;IACrC;IAEA,IAAIF,SAAS,EAAE;MACbxF,IAAI,CAAC4F,WAAW,CAAC,CAAC;MAClB5F,IAAI,CAAC6F,GAAG,CACN,MAAM,EACNzC,WAAC,CAAC0C,cAAc,CAAC,CACf,IAAAC,2BAAc,EAACV,cAAc,EAAErF,IAAI,CAACE,IAAI,CAAC8F,IAAwB,CAAC,CACnE,CACH,CAAC;IACH;EACF;EAEA,IAAIC,IAAI,GAAGb,QAAQ,CAAClF,IAAI;EACxBA,IAAI,CAACiD,MAAM,CAAC+C,GAAG,CAAC,CAAC;EAEjB,IAAI9C,WAAC,CAAC+C,SAAS,CAACF,IAAI,CAAC,EAAE;IACrB,MAAMG,OAAO,GAAGH,IAAI;IACpBA,IAAI,GAAG7F,KAAK,CAACiG,qBAAqB,CAAC,KAAK,CAAC;IAEzC,MAAMC,MAAM,GAAGlD,WAAC,CAACmD,mBAAmB,CAAC,KAAK,EAAE,CAC1CnD,WAAC,CAACoD,kBAAkB,CAACJ,OAAO,EAAEH,IAAI,CAAC,CACpC,CAAC;IACFjG,IAAI,CAAC4F,WAAW,CAAC,CAAC;IACjB1F,IAAI,CAAC8F,IAAI,CAAsBA,IAAI,CAACS,OAAO,CAACH,MAAM,CAAC;EACtD,CAAC,MAAM,IAAIL,IAAI,CAAC9F,IAAI,KAAK,WAAW,EAAE;IACpCC,KAAK,CAACsG,MAAM,CAACT,IAAI,CAAC9F,IAAI,CAAC;EACzB;EAEA,MAAMsD,MAAM,GAAGL,WAAC,CAACuD,UAAU,CAAC,WAAW,CAAC;EACxC,MAAMC,WAAW,GAAG3D,cAAc,CAAC/C,IAAI,CAAC;EAGxC,MAAMD,KAAY,GAAG;IACnB8C,UAAU,EAAE,EAAE;IACd1B,MAAM,EAAEuF,WAAW;IACnBC,aAAa,EAAEpD,MAAM;IACrBnD,YAAY,EAAEF,KAAK,CAAC0G,oBAAoB,CAACb,IAAI,CAAC9F,IAAI,CAAC;IACnDoC,UAAU,EAAE,EAAE;IACdpC,IAAI,EAAE8F,IAAI,CAAC9F,IAAI;IACfc,OAAO,EAAE;EACX,CAAC;EAEDjB,IAAI,CAACe,QAAQ,CAACR,mCAAmC,EAAEN,KAAK,CAAC;EAGzD,IAAI,CAACA,KAAK,CAACgB,OAAO,IAAI,CAAChB,KAAK,CAAC8C,UAAU,CAACD,MAAM,EAAE;IAC9C,KAAK,MAAM;MAAE9C,IAAI;MAAEyC;IAAM,CAAC,IAAIxC,KAAK,CAACsC,UAAU,EAAE;MAC9C,MAAMwE,YAAY,GAAG3D,WAAC,CAACa,SAAS,CAACR,MAAM,CAAC;MACxC,QAAQhB,KAAK;QACX,KAAK,aAAa;UAChBe,mBAAmB,CAACxD,IAAI,EAAE+G,YAAY,EAAE9G,KAAK,CAACoB,MAAM,CAAC;UACrD;QACF,KAAK,cAAc;UACjB6D,oBAAoB,CAAClF,IAAI,EAAE+G,YAAY,EAAE9G,KAAK,CAACoB,MAAM,CAAC;UACtD;QACF;UACErB,IAAI,CAACuE,WAAW,CAACwC,YAAY,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EAEA9G,KAAK,CAAC8C,UAAU,CAACP,IAAI,CAAC,GAAGvC,KAAK,CAACsC,UAAU,CAACyE,GAAG,CAAC,CAAC;IAAEhH;EAAK,CAAC,KAAKA,IAAI,CAAC,CAAC;EAElE,MAAMiH,KAAK,GAAG7D,WAAC,CAACO,cAAc,CAACiD,WAAW,CAAC;EAC3C,MAAMxF,GAAG,GAAGhB,KAAK,CAACiG,qBAAqB,CAAC,KAAK,CAAC;EAC9C,MAAMa,GAAG,GAAG9G,KAAK,CAACiG,qBAAqB,CAAC,KAAK,CAAC;EAE9C,IAAIc,MAAM,EAAEC,MAAM;EAClB,IAAIR,WAAW,EAAE;IAIfO,MAAM,GAAG/D,WAAC,CAACY,gBAAgB,CAAC,GAAG,EAAEZ,WAAC,CAACa,SAAS,CAAC7C,GAAG,CAAC,EAAEgC,WAAC,CAACa,SAAS,CAACgD,KAAK,CAAC,CAAC;IAQtEG,MAAM,GAAGhE,WAAC,CAACiE,qBAAqB,CAC9BjE,WAAC,CAACY,gBAAgB,CAAC,GAAG,EAAEZ,WAAC,CAACa,SAAS,CAACiD,GAAG,CAAC,EAAE9D,WAAC,CAACa,SAAS,CAACgD,KAAK,CAAC,CAAC,EAC7D7D,WAAC,CAACY,gBAAgB,CAAC,GAAG,EAAEZ,WAAC,CAACa,SAAS,CAACiD,GAAG,CAAC,EAAE9D,WAAC,CAACa,SAAS,CAACgD,KAAK,CAAC,CAAC,EAC7D7D,WAAC,CAACO,cAAc,CAAC,CAAC,CACpB,CAAC;EACH,CAAC,MAAM;IACLwD,MAAM,GAAG/D,WAAC,CAACuD,UAAU,CAACvF,GAAG,CAACjB,IAAI,CAAC;IAC/BiH,MAAM,GAAGhE,WAAC,CAACuD,UAAU,CAACO,GAAG,CAAC/G,IAAI,CAAC;EACjC;EAEA,MAAMmH,IAAI,GAAG9H,SAAS,CAAC;IACrBgF,SAAS,EAAEf,MAAM;IACjB8D,SAAS,EAAEJ,MAAM;IACjBK,SAAS,EAAEJ,MAAM;IACjBK,KAAK,EAAER,KAAK;IACZS,KAAK,EAAEzB,IAAI;IACX0B,GAAG,EAAEvG,GAAG;IACRwG,GAAG,EAAEV;EACP,CAAC,CAAC;EAEF,IAAIjH,KAAK,CAACgB,OAAO,EAAE;IAChBf,IAAI,CAAC8F,IAAI,CAAsBA,IAAI,CAACS,OAAO,CAACa,IAAI,CAAC;EACpD,CAAC,MAAM;IACL,IAAIO,MAAM,GAAG7H,IAAI,CACd8H,6BAA6B,CAAC7H,KAAK,CAAC8C,UAAU,CAAC,CAC/CgF,kBAAkB,CAAC,CAAC;IAGvBF,MAAM,CAACG,UAAU,CAAChI,IAAI,IAAI;MACxB,IAAIA,IAAI,CAACiI,MAAM,CAAC,CAAC,EAAE;QACjBJ,MAAM,GAAG7H,IAAI;MACf,CAAC,MAAM;QAEL,OAAOA,IAAI,CAACkI,UAAU,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IAEFL,MAAM,CAACM,YAAY,CAACb,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb", "ignoreList": []}