{"ast": null, "code": "import React,{useState,useContext,useEffect}from'react';import{useNavigate}from'react-router-dom';import{AuthContext}from'../context/AuthContext';import AuthModal from'../components/AuthModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function LoginPage(){const[showModal,setShowModal]=useState(true);const{user}=useContext(AuthContext);const navigate=useNavigate();useEffect(()=>{if(user){navigate('/profile');}},[user,navigate]);const handleCloseModal=()=>{setShowModal(false);navigate('/');};return/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsx(AuthModal,{isOpen:showModal,onClose:handleCloseModal,initialMode:\"login\"}),/*#__PURE__*/_jsx(\"div\",{className:\"login-page-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hero-section\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Join EcoCommerce\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Sign in to access your account and start shopping sustainably\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-heart\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Save Favorites\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create wishlists of your favorite eco-friendly products\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Earn Points\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Get loyalty points with every purchase\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Track Orders\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Monitor your order status and delivery\"})]})]})]})}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .login-page-content {\\n          max-width: 1200px;\\n          margin: 0 auto;\\n          padding: 2rem;\\n        }\\n\\n        .hero-section {\\n          text-align: center;\\n          padding: 4rem 0;\\n        }\\n\\n        .hero-section h1 {\\n          font-size: 3rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n          margin: 0 0 1rem 0;\\n        }\\n\\n        .hero-section p {\\n          font-size: 1.25rem;\\n          color: var(--text-secondary);\\n          margin: 0 0 3rem 0;\\n        }\\n\\n        .features {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n          gap: 2rem;\\n          margin-top: 3rem;\\n        }\\n\\n        .feature {\\n          background: white;\\n          padding: 2rem;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .feature i {\\n          font-size: 2.5rem;\\n          color: var(--primary-color);\\n          margin-bottom: 1rem;\\n        }\\n\\n        .feature h3 {\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          margin: 0 0 0.5rem 0;\\n        }\\n\\n        .feature p {\\n          color: var(--text-secondary);\\n          margin: 0;\\n          line-height: 1.6;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .hero-section h1 {\\n            font-size: 2rem;\\n          }\\n          \\n          .features {\\n            grid-template-columns: 1fr;\\n          }\\n        }\\n      \"})]});}export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "AuthContext", "AuthModal", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "showModal", "setShowModal", "user", "navigate", "handleCloseModal", "children", "isOpen", "onClose", "initialMode", "className"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../context/AuthContext';\nimport AuthModal from '../components/AuthModal';\n\nfunction LoginPage() {\n  const [showModal, setShowModal] = useState(true);\n  const { user } = useContext(AuthContext);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/profile');\n    }\n  }, [user, navigate]);\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    navigate('/');\n  };\n\n  return (\n    <main>\n      <AuthModal \n        isOpen={showModal} \n        onClose={handleCloseModal}\n        initialMode=\"login\"\n      />\n      \n      <div className=\"login-page-content\">\n        <div className=\"hero-section\">\n          <h1>Join EcoCommerce</h1>\n          <p>Sign in to access your account and start shopping sustainably</p>\n          \n          <div className=\"features\">\n            <div className=\"feature\">\n              <i className=\"fas fa-heart\"></i>\n              <h3>Save Favorites</h3>\n              <p>Create wishlists of your favorite eco-friendly products</p>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-star\"></i>\n              <h3>Earn Points</h3>\n              <p>Get loyalty points with every purchase</p>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-truck\"></i>\n              <h3>Track Orders</h3>\n              <p>Monitor your order status and delivery</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .login-page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .hero-section {\n          text-align: center;\n          padding: 4rem 0;\n        }\n\n        .hero-section h1 {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .hero-section p {\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          margin: 0 0 3rem 0;\n        }\n\n        .features {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 2rem;\n          margin-top: 3rem;\n        }\n\n        .feature {\n          background: white;\n          padding: 2rem;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 2.5rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .feature h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 0.5rem 0;\n        }\n\n        .feature p {\n          color: var(--text-secondary);\n          margin: 0;\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .hero-section h1 {\n            font-size: 2rem;\n          }\n          \n          .features {\n            grid-template-columns: 1fr;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default LoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,UAAU,CAAEC,SAAS,KAAQ,OAAO,CAC9D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,wBAAwB,CACpD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,QAAS,CAAAC,SAASA,CAAA,CAAG,CACnB,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAEa,IAAK,CAAC,CAAGZ,UAAU,CAACG,WAAW,CAAC,CACxC,KAAM,CAAAU,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd,GAAIW,IAAI,CAAE,CACRC,QAAQ,CAAC,UAAU,CAAC,CACtB,CACF,CAAC,CAAE,CAACD,IAAI,CAAEC,QAAQ,CAAC,CAAC,CAEpB,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BH,YAAY,CAAC,KAAK,CAAC,CACnBE,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,mBACEL,KAAA,SAAAO,QAAA,eACET,IAAA,CAACF,SAAS,EACRY,MAAM,CAAEN,SAAU,CAClBO,OAAO,CAAEH,gBAAiB,CAC1BI,WAAW,CAAC,OAAO,CACpB,CAAC,cAEFZ,IAAA,QAAKa,SAAS,CAAC,oBAAoB,CAAAJ,QAAA,cACjCP,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAJ,QAAA,eAC3BT,IAAA,OAAAS,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBT,IAAA,MAAAS,QAAA,CAAG,+DAA6D,CAAG,CAAC,cAEpEP,KAAA,QAAKW,SAAS,CAAC,UAAU,CAAAJ,QAAA,eACvBP,KAAA,QAAKW,SAAS,CAAC,SAAS,CAAAJ,QAAA,eACtBT,IAAA,MAAGa,SAAS,CAAC,cAAc,CAAI,CAAC,cAChCb,IAAA,OAAAS,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBT,IAAA,MAAAS,QAAA,CAAG,yDAAuD,CAAG,CAAC,EAC3D,CAAC,cACNP,KAAA,QAAKW,SAAS,CAAC,SAAS,CAAAJ,QAAA,eACtBT,IAAA,MAAGa,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/Bb,IAAA,OAAAS,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBT,IAAA,MAAAS,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,cACNP,KAAA,QAAKW,SAAS,CAAC,SAAS,CAAAJ,QAAA,eACtBT,IAAA,MAAGa,SAAS,CAAC,cAAc,CAAI,CAAC,cAChCb,IAAA,OAAAS,QAAA,CAAI,cAAY,CAAI,CAAC,cACrBT,IAAA,MAAAS,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENT,IAAA,UAAOD,GAAG,MAAAU,QAAA,gjDAoED,CAAC,EACN,CAAC,CAEX,CAEA,cAAe,CAAAN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}