{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useContext,useState}from'react';import{CartContext}from'../context/CartContext';import{AuthContext}from'../context/AuthContext';import{UserStatsContext}from'../context/UserStatsContext';import{Link}from'react-router-dom';import LoadingSpinner from'../components/LoadingSpinner';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CartPage(){const{cart,removeFromCart,clearCart,updateQuantity}=useContext(CartContext);const{token,user}=useContext(AuthContext);const{addOrder}=useContext(UserStatsContext);const[loading,setLoading]=useState(false);const[promoCode,setPromoCode]=useState('');const[discount,setDiscount]=useState(0);const[showCheckoutForm,setShowCheckoutForm]=useState(false);const subtotal=cart.reduce((sum,item)=>sum+item.price*item.quantity,0);const shipping=subtotal>50?0:9.99;const tax=subtotal*0.08;// 8% tax\nconst total=subtotal+shipping+tax-discount;const handleQuantityChange=(itemId,newQuantity)=>{if(newQuantity<=0){removeFromCart(itemId);}else{updateQuantity(itemId,newQuantity);}};const handlePromoCode=()=>{const validCodes={'SAVE10':0.1,'WELCOME':0.15,'ECO20':0.2};if(validCodes[promoCode.toUpperCase()]){setDiscount(subtotal*validCodes[promoCode.toUpperCase()]);}else{alert('Invalid promo code');}};async function handleCheckout(){if(!user){alert('Please login to checkout');return;}setLoading(true);try{// Create order object\nconst orderData={items:cart.map(item=>_objectSpread(_objectSpread({},item),{},{id:item._id})),total:total,shippingAddress:'123 Main St, City, State 12345'// This would come from user profile\n};// Simulate API call\nawait axios.post('http://localhost:5000/api/orders',orderData,{headers:{Authorization:\"Bearer \".concat(token)}});// Add order to user stats context\naddOrder(orderData);clearCart();setDiscount(0);setPromoCode('');// Show success message with points earned\nconst pointsEarned=Math.floor(total);alert(\"Order placed successfully! You earned \".concat(pointsEarned,\" loyalty points.\"));}catch(err){alert('Checkout failed. Please try again.');}finally{setLoading(false);}}if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{size:\"large\",message:\"Processing your order...\",fullScreen:true});}return/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cart-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cart-header\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"}),\"Shopping Cart\"]}),cart.length>0&&/*#__PURE__*/_jsxs(\"button\",{onClick:clearCart,className:\"clear-cart-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trash\"}),\"Clear Cart\"]})]}),cart.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-cart\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-cart-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"})}),/*#__PURE__*/_jsx(\"h2\",{children:\"Your cart is empty\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Looks like you haven't added any items to your cart yet\"}),/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"continue-shopping-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left\"}),\"Continue Shopping\"]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"cart-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cart-items\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"items-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[\"Items in your cart (\",cart.length,\")\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"items-list\",children:cart.map(item=>{var _item$description,_item$tags;return/*#__PURE__*/_jsxs(\"div\",{className:\"cart-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"item-image\",children:/*#__PURE__*/_jsx(\"img\",{src:item.imageUrl,alt:item.name})}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-details\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"item-name\",children:item.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"item-description\",children:[(_item$description=item.description)===null||_item$description===void 0?void 0:_item$description.substring(0,100),\"...\"]}),item.isEcoFriendly&&/*#__PURE__*/_jsxs(\"div\",{className:\"eco-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-leaf\"}),\"Eco-Friendly\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"item-tags\",children:(_item$tags=item.tags)===null||_item$tags===void 0?void 0:_item$tags.slice(0,2).map((tag,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"tag\",children:tag},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-price\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"price-per-unit\",children:[\"$\",item.price]}),/*#__PURE__*/_jsx(\"div\",{className:\"price-label\",children:\"per item\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"quantity-controls\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleQuantityChange(item._id,item.quantity-1),className:\"quantity-btn\",disabled:item.quantity<=1,children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-minus\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"quantity-display\",children:item.quantity}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleQuantityChange(item._id,item.quantity+1),className:\"quantity-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"item-total\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"total-price\",children:[\"$\",(item.price*item.quantity).toFixed(2)]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>removeFromCart(item._id),className:\"remove-btn\",title:\"Remove from cart\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trash\"})}),/*#__PURE__*/_jsx(Link,{to:\"/product/\".concat(item._id),className:\"view-btn\",title:\"View product details\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-eye\"})})]})]},item._id);})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"cart-summary\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"summary-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Order Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-line\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"Subtotal (\",cart.length,\" items)\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"$\",subtotal.toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-line\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Shipping\"}),/*#__PURE__*/_jsx(\"span\",{children:shipping===0?'FREE':\"$\".concat(shipping.toFixed(2))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-line\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Tax\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"$\",tax.toFixed(2)]})]}),discount>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"summary-line discount\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Discount\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"-$\",discount.toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-line total\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"$\",total.toFixed(2)]})]}),shipping>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"shipping-notice\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle\"}),\"Add $\",(50-subtotal).toFixed(2),\" more for free shipping\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"promo-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Promo Code\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"promo-input\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:promoCode,onChange:e=>setPromoCode(e.target.value),placeholder:\"Enter promo code\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handlePromoCode,className:\"apply-btn\",children:\"Apply\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"promo-suggestions\",children:/*#__PURE__*/_jsx(\"small\",{children:\"Try: SAVE10, WELCOME, ECO20\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"checkout-section\",children:[user?/*#__PURE__*/_jsxs(\"button\",{onClick:handleCheckout,className:\"checkout-btn\",disabled:cart.length===0,children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-credit-card\"}),\"Proceed to Checkout\"]}):/*#__PURE__*/_jsxs(\"div\",{className:\"login-prompt\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Please login to checkout\"}),/*#__PURE__*/_jsxs(Link,{to:\"/login\",className:\"login-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-in-alt\"}),\"Login\"]})]}),/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"continue-shopping\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left\"}),\"Continue Shopping\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-badges\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"security-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shield-alt\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Secure Checkout\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-undo\"}),/*#__PURE__*/_jsx(\"span\",{children:\"30-Day Returns\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Fast Shipping\"})]})]})]})})]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .cart-container {\\n          max-width: 1400px;\\n          margin: 0 auto;\\n          padding: 2rem;\\n        }\\n\\n        .cart-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 2rem;\\n          padding-bottom: 1rem;\\n          border-bottom: 2px solid var(--border-color);\\n        }\\n\\n        .cart-header h1 {\\n          font-size: 2rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n          margin: 0;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n        }\\n\\n        .clear-cart-btn {\\n          background: #ef4444;\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .clear-cart-btn:hover {\\n          background: #dc2626;\\n        }\\n\\n        .empty-cart {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          min-height: 60vh;\\n          text-align: center;\\n          gap: 1.5rem;\\n        }\\n\\n        .empty-cart-icon {\\n          font-size: 4rem;\\n          color: var(--text-secondary);\\n          opacity: 0.5;\\n        }\\n\\n        .empty-cart h2 {\\n          font-size: 1.75rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          margin: 0;\\n        }\\n\\n        .empty-cart p {\\n          color: var(--text-secondary);\\n          margin: 0;\\n          font-size: 1.125rem;\\n        }\\n\\n        .continue-shopping-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          text-decoration: none;\\n          padding: 1rem 2rem;\\n          border-radius: 0.5rem;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .continue-shopping-btn:hover {\\n          background: var(--primary-dark);\\n          transform: translateY(-1px);\\n        }\\n\\n        .cart-content {\\n          display: grid;\\n          grid-template-columns: 1fr 400px;\\n          gap: 3rem;\\n        }\\n\\n        .cart-items {\\n          background: white;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n          overflow: hidden;\\n        }\\n\\n        .items-header {\\n          padding: 1.5rem 2rem;\\n          border-bottom: 1px solid var(--border-color);\\n          background: var(--bg-secondary);\\n        }\\n\\n        .items-header h3 {\\n          margin: 0;\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .items-list {\\n          padding: 1rem;\\n        }\\n\\n        .cart-item {\\n          display: grid;\\n          grid-template-columns: 100px 1fr auto auto auto auto;\\n          gap: 1.5rem;\\n          align-items: center;\\n          padding: 1.5rem;\\n          border-bottom: 1px solid var(--border-color);\\n          transition: all 0.2s;\\n        }\\n\\n        .cart-item:last-child {\\n          border-bottom: none;\\n        }\\n\\n        .cart-item:hover {\\n          background: var(--bg-secondary);\\n        }\\n\\n        .item-image {\\n          width: 100px;\\n          height: 100px;\\n          border-radius: 0.75rem;\\n          overflow: hidden;\\n          box-shadow: var(--shadow-sm);\\n        }\\n\\n        .item-image img {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n        }\\n\\n        .item-details {\\n          min-width: 0;\\n        }\\n\\n        .item-name {\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          margin: 0 0 0.5rem 0;\\n          line-height: 1.3;\\n        }\\n\\n        .item-description {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n          margin: 0 0 0.75rem 0;\\n          line-height: 1.4;\\n        }\\n\\n        .eco-badge {\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.25rem;\\n          background: var(--secondary-color);\\n          color: white;\\n          padding: 0.25rem 0.75rem;\\n          border-radius: 9999px;\\n          font-size: 0.75rem;\\n          font-weight: 500;\\n          margin-bottom: 0.5rem;\\n        }\\n\\n        .item-tags {\\n          display: flex;\\n          gap: 0.5rem;\\n          flex-wrap: wrap;\\n        }\\n\\n        .tag {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          padding: 0.125rem 0.5rem;\\n          border-radius: 0.375rem;\\n          font-size: 0.75rem;\\n          font-weight: 500;\\n        }\\n\\n        .item-price {\\n          text-align: center;\\n        }\\n\\n        .price-per-unit {\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          line-height: 1;\\n        }\\n\\n        .price-label {\\n          font-size: 0.75rem;\\n          color: var(--text-secondary);\\n          margin-top: 0.25rem;\\n        }\\n\\n        .quantity-controls {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          background: var(--bg-secondary);\\n          border-radius: 0.5rem;\\n          padding: 0.25rem;\\n        }\\n\\n        .quantity-btn {\\n          background: white;\\n          border: 1px solid var(--border-color);\\n          width: 32px;\\n          height: 32px;\\n          border-radius: 0.375rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          font-size: 0.875rem;\\n        }\\n\\n        .quantity-btn:hover:not(:disabled) {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .quantity-btn:disabled {\\n          opacity: 0.5;\\n          cursor: not-allowed;\\n        }\\n\\n        .quantity-display {\\n          min-width: 40px;\\n          text-align: center;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .item-total {\\n          text-align: center;\\n        }\\n\\n        .total-price {\\n          font-size: 1.25rem;\\n          font-weight: 700;\\n          color: var(--primary-color);\\n        }\\n\\n        .item-actions {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 0.5rem;\\n        }\\n\\n        .remove-btn, .view-btn {\\n          background: none;\\n          border: 1px solid var(--border-color);\\n          width: 40px;\\n          height: 40px;\\n          border-radius: 0.5rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          text-decoration: none;\\n          color: var(--text-secondary);\\n        }\\n\\n        .remove-btn:hover {\\n          background: #ef4444;\\n          color: white;\\n          border-color: #ef4444;\\n        }\\n\\n        .view-btn:hover {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .cart-summary {\\n          position: sticky;\\n          top: 2rem;\\n          height: fit-content;\\n        }\\n\\n        .summary-card {\\n          background: white;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n          padding: 2rem;\\n        }\\n\\n        .summary-card h3 {\\n          margin: 0 0 1.5rem 0;\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .summary-line {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          padding: 0.75rem 0;\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        .summary-line:last-of-type {\\n          border-bottom: none;\\n        }\\n\\n        .summary-line.discount {\\n          color: var(--secondary-color);\\n          font-weight: 500;\\n        }\\n\\n        .summary-line.total {\\n          font-size: 1.25rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n          border-top: 2px solid var(--border-color);\\n          margin-top: 1rem;\\n          padding-top: 1rem;\\n        }\\n\\n        .shipping-notice {\\n          background: #fffbeb;\\n          color: #92400e;\\n          padding: 0.75rem;\\n          border-radius: 0.5rem;\\n          font-size: 0.875rem;\\n          margin: 1rem 0;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .promo-section {\\n          margin: 2rem 0;\\n          padding: 1.5rem 0;\\n          border-top: 1px solid var(--border-color);\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        .promo-section h4 {\\n          margin: 0 0 1rem 0;\\n          font-size: 1rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .promo-input {\\n          display: flex;\\n          gap: 0.5rem;\\n          margin-bottom: 0.5rem;\\n        }\\n\\n        .promo-input input {\\n          flex: 1;\\n          padding: 0.75rem;\\n          border: 2px solid var(--border-color);\\n          border-radius: 0.5rem;\\n          font-size: 0.875rem;\\n        }\\n\\n        .promo-input input:focus {\\n          outline: none;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .apply-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          transition: all 0.2s;\\n        }\\n\\n        .apply-btn:hover {\\n          background: var(--primary-dark);\\n        }\\n\\n        .promo-suggestions {\\n          color: var(--text-secondary);\\n          font-size: 0.75rem;\\n        }\\n\\n        .checkout-section {\\n          margin-top: 2rem;\\n        }\\n\\n        .checkout-btn {\\n          width: 100%;\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 1rem 2rem;\\n          border-radius: 0.75rem;\\n          cursor: pointer;\\n          font-weight: 600;\\n          font-size: 1.125rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 0.75rem;\\n          transition: all 0.2s;\\n          margin-bottom: 1rem;\\n        }\\n\\n        .checkout-btn:hover:not(:disabled) {\\n          background: var(--primary-dark);\\n          transform: translateY(-1px);\\n        }\\n\\n        .checkout-btn:disabled {\\n          opacity: 0.6;\\n          cursor: not-allowed;\\n        }\\n\\n        .login-prompt {\\n          text-align: center;\\n          margin-bottom: 1rem;\\n        }\\n\\n        .login-prompt p {\\n          margin: 0 0 1rem 0;\\n          color: var(--text-secondary);\\n        }\\n\\n        .login-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          text-decoration: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          font-weight: 500;\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .login-btn:hover {\\n          background: var(--primary-dark);\\n        }\\n\\n        .continue-shopping {\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 0.5rem;\\n          color: var(--text-secondary);\\n          text-decoration: none;\\n          font-weight: 500;\\n          transition: all 0.2s;\\n        }\\n\\n        .continue-shopping:hover {\\n          color: var(--primary-color);\\n        }\\n\\n        .security-badges {\\n          margin-top: 2rem;\\n          padding-top: 1.5rem;\\n          border-top: 1px solid var(--border-color);\\n          display: flex;\\n          flex-direction: column;\\n          gap: 0.75rem;\\n        }\\n\\n        .security-badge {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .security-badge i {\\n          color: var(--secondary-color);\\n          width: 16px;\\n          text-align: center;\\n        }\\n\\n        @media (max-width: 1024px) {\\n          .cart-content {\\n            grid-template-columns: 1fr;\\n            gap: 2rem;\\n          }\\n\\n          .cart-summary {\\n            position: static;\\n          }\\n        }\\n\\n        @media (max-width: 768px) {\\n          .cart-container {\\n            padding: 1rem;\\n          }\\n\\n          .cart-header {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: flex-start;\\n          }\\n\\n          .cart-item {\\n            grid-template-columns: 80px 1fr;\\n            gap: 1rem;\\n          }\\n\\n          .item-image {\\n            width: 80px;\\n            height: 80px;\\n          }\\n\\n          .item-price,\\n          .quantity-controls,\\n          .item-total,\\n          .item-actions {\\n            grid-column: 1 / -1;\\n            justify-self: start;\\n            margin-top: 1rem;\\n          }\\n\\n          .quantity-controls {\\n            justify-self: start;\\n          }\\n\\n          .item-actions {\\n            flex-direction: row;\\n          }\\n\\n          .summary-card {\\n            padding: 1.5rem;\\n          }\\n        }\\n      \"})]});}export default CartPage;", "map": {"version": 3, "names": ["React", "useContext", "useState", "CartContext", "AuthContext", "UserStatsContext", "Link", "LoadingSpinner", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "CartPage", "cart", "removeFromCart", "clearCart", "updateQuantity", "token", "user", "addOrder", "loading", "setLoading", "promoCode", "setPromoCode", "discount", "setDiscount", "showCheckoutForm", "setShowCheckoutForm", "subtotal", "reduce", "sum", "item", "price", "quantity", "shipping", "tax", "total", "handleQuantityChange", "itemId", "newQuantity", "handlePromoCode", "validCodes", "toUpperCase", "alert", "handleCheckout", "orderData", "items", "map", "_objectSpread", "id", "_id", "shippingAddress", "post", "headers", "Authorization", "concat", "pointsEarned", "Math", "floor", "err", "size", "message", "fullScreen", "children", "className", "length", "onClick", "to", "_item$description", "_item$tags", "src", "imageUrl", "alt", "name", "description", "substring", "isEcoFriendly", "tags", "slice", "tag", "index", "disabled", "toFixed", "title", "type", "value", "onChange", "e", "target", "placeholder"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/CartPage.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { CartContext } from '../context/CartContext';\r\nimport { AuthContext } from '../context/AuthContext';\r\nimport { UserStatsContext } from '../context/UserStatsContext';\r\nimport { Link } from 'react-router-dom';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport axios from 'axios';\r\n\r\nfunction CartPage() {\r\n  const { cart, removeFromCart, clearCart, updateQuantity } = useContext(CartContext);\r\n  const { token, user } = useContext(AuthContext);\r\n  const { addOrder } = useContext(UserStatsContext);\r\n  const [loading, setLoading] = useState(false);\r\n  const [promoCode, setPromoCode] = useState('');\r\n  const [discount, setDiscount] = useState(0);\r\n  const [showCheckoutForm, setShowCheckoutForm] = useState(false);\r\n\r\n  const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);\r\n  const shipping = subtotal > 50 ? 0 : 9.99;\r\n  const tax = subtotal * 0.08; // 8% tax\r\n  const total = subtotal + shipping + tax - discount;\r\n\r\n  const handleQuantityChange = (itemId, newQuantity) => {\r\n    if (newQuantity <= 0) {\r\n      removeFromCart(itemId);\r\n    } else {\r\n      updateQuantity(itemId, newQuantity);\r\n    }\r\n  };\r\n\r\n  const handlePromoCode = () => {\r\n    const validCodes = {\r\n      'SAVE10': 0.1,\r\n      'WELCOME': 0.15,\r\n      'ECO20': 0.2\r\n    };\r\n\r\n    if (validCodes[promoCode.toUpperCase()]) {\r\n      setDiscount(subtotal * validCodes[promoCode.toUpperCase()]);\r\n    } else {\r\n      alert('Invalid promo code');\r\n    }\r\n  };\r\n\r\n  async function handleCheckout() {\r\n    if (!user) {\r\n      alert('Please login to checkout');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      // Create order object\r\n      const orderData = {\r\n        items: cart.map(item => ({\r\n          ...item,\r\n          id: item._id\r\n        })),\r\n        total: total,\r\n        shippingAddress: '123 Main St, City, State 12345' // This would come from user profile\r\n      };\r\n\r\n      // Simulate API call\r\n      await axios.post(\r\n        'http://localhost:5000/api/orders',\r\n        orderData,\r\n        { headers: { Authorization: `Bearer ${token}` } }\r\n      );\r\n\r\n      // Add order to user stats context\r\n      addOrder(orderData);\r\n\r\n      clearCart();\r\n      setDiscount(0);\r\n      setPromoCode('');\r\n\r\n      // Show success message with points earned\r\n      const pointsEarned = Math.floor(total);\r\n      alert(`Order placed successfully! You earned ${pointsEarned} loyalty points.`);\r\n    } catch (err) {\r\n      alert('Checkout failed. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner size=\"large\" message=\"Processing your order...\" fullScreen />;\r\n  }\r\n\r\n  return (\r\n    <main>\r\n      <div className=\"cart-container\">\r\n        <div className=\"cart-header\">\r\n          <h1>\r\n            <i className=\"fas fa-shopping-cart\"></i>\r\n            Shopping Cart\r\n          </h1>\r\n          {cart.length > 0 && (\r\n            <button onClick={clearCart} className=\"clear-cart-btn\">\r\n              <i className=\"fas fa-trash\"></i>\r\n              Clear Cart\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {cart.length === 0 ? (\r\n          <div className=\"empty-cart\">\r\n            <div className=\"empty-cart-icon\">\r\n              <i className=\"fas fa-shopping-cart\"></i>\r\n            </div>\r\n            <h2>Your cart is empty</h2>\r\n            <p>Looks like you haven't added any items to your cart yet</p>\r\n            <Link to=\"/\" className=\"continue-shopping-btn\">\r\n              <i className=\"fas fa-arrow-left\"></i>\r\n              Continue Shopping\r\n            </Link>\r\n          </div>\r\n        ) : (\r\n          <div className=\"cart-content\">\r\n            <div className=\"cart-items\">\r\n              <div className=\"items-header\">\r\n                <h3>Items in your cart ({cart.length})</h3>\r\n              </div>\r\n\r\n              <div className=\"items-list\">\r\n                {cart.map(item => (\r\n                  <div key={item._id} className=\"cart-item\">\r\n                    <div className=\"item-image\">\r\n                      <img src={item.imageUrl} alt={item.name} />\r\n                    </div>\r\n\r\n                    <div className=\"item-details\">\r\n                      <h4 className=\"item-name\">{item.name}</h4>\r\n                      <p className=\"item-description\">\r\n                        {item.description?.substring(0, 100)}...\r\n                      </p>\r\n\r\n                      {item.isEcoFriendly && (\r\n                        <div className=\"eco-badge\">\r\n                          <i className=\"fas fa-leaf\"></i>\r\n                          Eco-Friendly\r\n                        </div>\r\n                      )}\r\n\r\n                      <div className=\"item-tags\">\r\n                        {item.tags?.slice(0, 2).map((tag, index) => (\r\n                          <span key={index} className=\"tag\">{tag}</span>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"item-price\">\r\n                      <div className=\"price-per-unit\">${item.price}</div>\r\n                      <div className=\"price-label\">per item</div>\r\n                    </div>\r\n\r\n                    <div className=\"quantity-controls\">\r\n                      <button\r\n                        onClick={() => handleQuantityChange(item._id, item.quantity - 1)}\r\n                        className=\"quantity-btn\"\r\n                        disabled={item.quantity <= 1}\r\n                      >\r\n                        <i className=\"fas fa-minus\"></i>\r\n                      </button>\r\n                      <span className=\"quantity-display\">{item.quantity}</span>\r\n                      <button\r\n                        onClick={() => handleQuantityChange(item._id, item.quantity + 1)}\r\n                        className=\"quantity-btn\"\r\n                      >\r\n                        <i className=\"fas fa-plus\"></i>\r\n                      </button>\r\n                    </div>\r\n\r\n                    <div className=\"item-total\">\r\n                      <div className=\"total-price\">${(item.price * item.quantity).toFixed(2)}</div>\r\n                    </div>\r\n\r\n                    <div className=\"item-actions\">\r\n                      <button\r\n                        onClick={() => removeFromCart(item._id)}\r\n                        className=\"remove-btn\"\r\n                        title=\"Remove from cart\"\r\n                      >\r\n                        <i className=\"fas fa-trash\"></i>\r\n                      </button>\r\n                      <Link\r\n                        to={`/product/${item._id}`}\r\n                        className=\"view-btn\"\r\n                        title=\"View product details\"\r\n                      >\r\n                        <i className=\"fas fa-eye\"></i>\r\n                      </Link>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"cart-summary\">\r\n              <div className=\"summary-card\">\r\n                <h3>Order Summary</h3>\r\n\r\n                <div className=\"summary-line\">\r\n                  <span>Subtotal ({cart.length} items)</span>\r\n                  <span>${subtotal.toFixed(2)}</span>\r\n                </div>\r\n\r\n                <div className=\"summary-line\">\r\n                  <span>Shipping</span>\r\n                  <span>{shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}</span>\r\n                </div>\r\n\r\n                <div className=\"summary-line\">\r\n                  <span>Tax</span>\r\n                  <span>${tax.toFixed(2)}</span>\r\n                </div>\r\n\r\n                {discount > 0 && (\r\n                  <div className=\"summary-line discount\">\r\n                    <span>Discount</span>\r\n                    <span>-${discount.toFixed(2)}</span>\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"summary-line total\">\r\n                  <span>Total</span>\r\n                  <span>${total.toFixed(2)}</span>\r\n                </div>\r\n\r\n                {shipping > 0 && (\r\n                  <div className=\"shipping-notice\">\r\n                    <i className=\"fas fa-info-circle\"></i>\r\n                    Add ${(50 - subtotal).toFixed(2)} more for free shipping\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"promo-section\">\r\n                  <h4>Promo Code</h4>\r\n                  <div className=\"promo-input\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={promoCode}\r\n                      onChange={(e) => setPromoCode(e.target.value)}\r\n                      placeholder=\"Enter promo code\"\r\n                    />\r\n                    <button onClick={handlePromoCode} className=\"apply-btn\">\r\n                      Apply\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"promo-suggestions\">\r\n                    <small>Try: SAVE10, WELCOME, ECO20</small>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"checkout-section\">\r\n                  {user ? (\r\n                    <button\r\n                      onClick={handleCheckout}\r\n                      className=\"checkout-btn\"\r\n                      disabled={cart.length === 0}\r\n                    >\r\n                      <i className=\"fas fa-credit-card\"></i>\r\n                      Proceed to Checkout\r\n                    </button>\r\n                  ) : (\r\n                    <div className=\"login-prompt\">\r\n                      <p>Please login to checkout</p>\r\n                      <Link to=\"/login\" className=\"login-btn\">\r\n                        <i className=\"fas fa-sign-in-alt\"></i>\r\n                        Login\r\n                      </Link>\r\n                    </div>\r\n                  )}\r\n\r\n                  <Link to=\"/\" className=\"continue-shopping\">\r\n                    <i className=\"fas fa-arrow-left\"></i>\r\n                    Continue Shopping\r\n                  </Link>\r\n                </div>\r\n\r\n                <div className=\"security-badges\">\r\n                  <div className=\"security-badge\">\r\n                    <i className=\"fas fa-shield-alt\"></i>\r\n                    <span>Secure Checkout</span>\r\n                  </div>\r\n                  <div className=\"security-badge\">\r\n                    <i className=\"fas fa-undo\"></i>\r\n                    <span>30-Day Returns</span>\r\n                  </div>\r\n                  <div className=\"security-badge\">\r\n                    <i className=\"fas fa-truck\"></i>\r\n                    <span>Fast Shipping</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <style jsx>{`\r\n        .cart-container {\r\n          max-width: 1400px;\r\n          margin: 0 auto;\r\n          padding: 2rem;\r\n        }\r\n\r\n        .cart-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 2rem;\r\n          padding-bottom: 1rem;\r\n          border-bottom: 2px solid var(--border-color);\r\n        }\r\n\r\n        .cart-header h1 {\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n          color: var(--text-primary);\r\n          margin: 0;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n        }\r\n\r\n        .clear-cart-btn {\r\n          background: #ef4444;\r\n          color: white;\r\n          border: none;\r\n          padding: 0.75rem 1.5rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .clear-cart-btn:hover {\r\n          background: #dc2626;\r\n        }\r\n\r\n        .empty-cart {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-height: 60vh;\r\n          text-align: center;\r\n          gap: 1.5rem;\r\n        }\r\n\r\n        .empty-cart-icon {\r\n          font-size: 4rem;\r\n          color: var(--text-secondary);\r\n          opacity: 0.5;\r\n        }\r\n\r\n        .empty-cart h2 {\r\n          font-size: 1.75rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n          margin: 0;\r\n        }\r\n\r\n        .empty-cart p {\r\n          color: var(--text-secondary);\r\n          margin: 0;\r\n          font-size: 1.125rem;\r\n        }\r\n\r\n        .continue-shopping-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          text-decoration: none;\r\n          padding: 1rem 2rem;\r\n          border-radius: 0.5rem;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .continue-shopping-btn:hover {\r\n          background: var(--primary-dark);\r\n          transform: translateY(-1px);\r\n        }\r\n\r\n        .cart-content {\r\n          display: grid;\r\n          grid-template-columns: 1fr 400px;\r\n          gap: 3rem;\r\n        }\r\n\r\n        .cart-items {\r\n          background: white;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          overflow: hidden;\r\n        }\r\n\r\n        .items-header {\r\n          padding: 1.5rem 2rem;\r\n          border-bottom: 1px solid var(--border-color);\r\n          background: var(--bg-secondary);\r\n        }\r\n\r\n        .items-header h3 {\r\n          margin: 0;\r\n          font-size: 1.25rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .items-list {\r\n          padding: 1rem;\r\n        }\r\n\r\n        .cart-item {\r\n          display: grid;\r\n          grid-template-columns: 100px 1fr auto auto auto auto;\r\n          gap: 1.5rem;\r\n          align-items: center;\r\n          padding: 1.5rem;\r\n          border-bottom: 1px solid var(--border-color);\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .cart-item:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .cart-item:hover {\r\n          background: var(--bg-secondary);\r\n        }\r\n\r\n        .item-image {\r\n          width: 100px;\r\n          height: 100px;\r\n          border-radius: 0.75rem;\r\n          overflow: hidden;\r\n          box-shadow: var(--shadow-sm);\r\n        }\r\n\r\n        .item-image img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .item-details {\r\n          min-width: 0;\r\n        }\r\n\r\n        .item-name {\r\n          font-size: 1.125rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n          margin: 0 0 0.5rem 0;\r\n          line-height: 1.3;\r\n        }\r\n\r\n        .item-description {\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n          margin: 0 0 0.75rem 0;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .eco-badge {\r\n          display: inline-flex;\r\n          align-items: center;\r\n          gap: 0.25rem;\r\n          background: var(--secondary-color);\r\n          color: white;\r\n          padding: 0.25rem 0.75rem;\r\n          border-radius: 9999px;\r\n          font-size: 0.75rem;\r\n          font-weight: 500;\r\n          margin-bottom: 0.5rem;\r\n        }\r\n\r\n        .item-tags {\r\n          display: flex;\r\n          gap: 0.5rem;\r\n          flex-wrap: wrap;\r\n        }\r\n\r\n        .tag {\r\n          background: var(--bg-secondary);\r\n          color: var(--text-primary);\r\n          padding: 0.125rem 0.5rem;\r\n          border-radius: 0.375rem;\r\n          font-size: 0.75rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .item-price {\r\n          text-align: center;\r\n        }\r\n\r\n        .price-per-unit {\r\n          font-size: 1.125rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n          line-height: 1;\r\n        }\r\n\r\n        .price-label {\r\n          font-size: 0.75rem;\r\n          color: var(--text-secondary);\r\n          margin-top: 0.25rem;\r\n        }\r\n\r\n        .quantity-controls {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          background: var(--bg-secondary);\r\n          border-radius: 0.5rem;\r\n          padding: 0.25rem;\r\n        }\r\n\r\n        .quantity-btn {\r\n          background: white;\r\n          border: 1px solid var(--border-color);\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 0.375rem;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .quantity-btn:hover:not(:disabled) {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border-color: var(--primary-color);\r\n        }\r\n\r\n        .quantity-btn:disabled {\r\n          opacity: 0.5;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .quantity-display {\r\n          min-width: 40px;\r\n          text-align: center;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .item-total {\r\n          text-align: center;\r\n        }\r\n\r\n        .total-price {\r\n          font-size: 1.25rem;\r\n          font-weight: 700;\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .item-actions {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .remove-btn, .view-btn {\r\n          background: none;\r\n          border: 1px solid var(--border-color);\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 0.5rem;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          transition: all 0.2s;\r\n          text-decoration: none;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .remove-btn:hover {\r\n          background: #ef4444;\r\n          color: white;\r\n          border-color: #ef4444;\r\n        }\r\n\r\n        .view-btn:hover {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border-color: var(--primary-color);\r\n        }\r\n\r\n        .cart-summary {\r\n          position: sticky;\r\n          top: 2rem;\r\n          height: fit-content;\r\n        }\r\n\r\n        .summary-card {\r\n          background: white;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          padding: 2rem;\r\n        }\r\n\r\n        .summary-card h3 {\r\n          margin: 0 0 1.5rem 0;\r\n          font-size: 1.25rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .summary-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 0.75rem 0;\r\n          border-bottom: 1px solid var(--border-color);\r\n        }\r\n\r\n        .summary-line:last-of-type {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .summary-line.discount {\r\n          color: var(--secondary-color);\r\n          font-weight: 500;\r\n        }\r\n\r\n        .summary-line.total {\r\n          font-size: 1.25rem;\r\n          font-weight: 700;\r\n          color: var(--text-primary);\r\n          border-top: 2px solid var(--border-color);\r\n          margin-top: 1rem;\r\n          padding-top: 1rem;\r\n        }\r\n\r\n        .shipping-notice {\r\n          background: #fffbeb;\r\n          color: #92400e;\r\n          padding: 0.75rem;\r\n          border-radius: 0.5rem;\r\n          font-size: 0.875rem;\r\n          margin: 1rem 0;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .promo-section {\r\n          margin: 2rem 0;\r\n          padding: 1.5rem 0;\r\n          border-top: 1px solid var(--border-color);\r\n          border-bottom: 1px solid var(--border-color);\r\n        }\r\n\r\n        .promo-section h4 {\r\n          margin: 0 0 1rem 0;\r\n          font-size: 1rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .promo-input {\r\n          display: flex;\r\n          gap: 0.5rem;\r\n          margin-bottom: 0.5rem;\r\n        }\r\n\r\n        .promo-input input {\r\n          flex: 1;\r\n          padding: 0.75rem;\r\n          border: 2px solid var(--border-color);\r\n          border-radius: 0.5rem;\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .promo-input input:focus {\r\n          outline: none;\r\n          border-color: var(--primary-color);\r\n        }\r\n\r\n        .apply-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 0.75rem 1.5rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .apply-btn:hover {\r\n          background: var(--primary-dark);\r\n        }\r\n\r\n        .promo-suggestions {\r\n          color: var(--text-secondary);\r\n          font-size: 0.75rem;\r\n        }\r\n\r\n        .checkout-section {\r\n          margin-top: 2rem;\r\n        }\r\n\r\n        .checkout-btn {\r\n          width: 100%;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 1rem 2rem;\r\n          border-radius: 0.75rem;\r\n          cursor: pointer;\r\n          font-weight: 600;\r\n          font-size: 1.125rem;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          gap: 0.75rem;\r\n          transition: all 0.2s;\r\n          margin-bottom: 1rem;\r\n        }\r\n\r\n        .checkout-btn:hover:not(:disabled) {\r\n          background: var(--primary-dark);\r\n          transform: translateY(-1px);\r\n        }\r\n\r\n        .checkout-btn:disabled {\r\n          opacity: 0.6;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .login-prompt {\r\n          text-align: center;\r\n          margin-bottom: 1rem;\r\n        }\r\n\r\n        .login-prompt p {\r\n          margin: 0 0 1rem 0;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .login-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          text-decoration: none;\r\n          padding: 0.75rem 1.5rem;\r\n          border-radius: 0.5rem;\r\n          font-weight: 500;\r\n          display: inline-flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .login-btn:hover {\r\n          background: var(--primary-dark);\r\n        }\r\n\r\n        .continue-shopping {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          gap: 0.5rem;\r\n          color: var(--text-secondary);\r\n          text-decoration: none;\r\n          font-weight: 500;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .continue-shopping:hover {\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .security-badges {\r\n          margin-top: 2rem;\r\n          padding-top: 1.5rem;\r\n          border-top: 1px solid var(--border-color);\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 0.75rem;\r\n        }\r\n\r\n        .security-badge {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .security-badge i {\r\n          color: var(--secondary-color);\r\n          width: 16px;\r\n          text-align: center;\r\n        }\r\n\r\n        @media (max-width: 1024px) {\r\n          .cart-content {\r\n            grid-template-columns: 1fr;\r\n            gap: 2rem;\r\n          }\r\n\r\n          .cart-summary {\r\n            position: static;\r\n          }\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .cart-container {\r\n            padding: 1rem;\r\n          }\r\n\r\n          .cart-header {\r\n            flex-direction: column;\r\n            gap: 1rem;\r\n            align-items: flex-start;\r\n          }\r\n\r\n          .cart-item {\r\n            grid-template-columns: 80px 1fr;\r\n            gap: 1rem;\r\n          }\r\n\r\n          .item-image {\r\n            width: 80px;\r\n            height: 80px;\r\n          }\r\n\r\n          .item-price,\r\n          .quantity-controls,\r\n          .item-total,\r\n          .item-actions {\r\n            grid-column: 1 / -1;\r\n            justify-self: start;\r\n            margin-top: 1rem;\r\n          }\r\n\r\n          .quantity-controls {\r\n            justify-self: start;\r\n          }\r\n\r\n          .item-actions {\r\n            flex-direction: row;\r\n          }\r\n\r\n          .summary-card {\r\n            padding: 1.5rem;\r\n          }\r\n        }\r\n      `}</style>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default CartPage;"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,QAAQ,KAAQ,OAAO,CACnD,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,gBAAgB,KAAQ,6BAA6B,CAC9D,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,QAAQA,CAAA,CAAG,CAClB,KAAM,CAAEC,IAAI,CAAEC,cAAc,CAAEC,SAAS,CAAEC,cAAe,CAAC,CAAGhB,UAAU,CAACE,WAAW,CAAC,CACnF,KAAM,CAAEe,KAAK,CAAEC,IAAK,CAAC,CAAGlB,UAAU,CAACG,WAAW,CAAC,CAC/C,KAAM,CAAEgB,QAAS,CAAC,CAAGnB,UAAU,CAACI,gBAAgB,CAAC,CACjD,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqB,SAAS,CAAEC,YAAY,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACyB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAAA2B,QAAQ,CAAGf,IAAI,CAACgB,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAAGC,IAAI,CAACC,KAAK,CAAGD,IAAI,CAACE,QAAQ,CAAE,CAAC,CAAC,CAChF,KAAM,CAAAC,QAAQ,CAAGN,QAAQ,CAAG,EAAE,CAAG,CAAC,CAAG,IAAI,CACzC,KAAM,CAAAO,GAAG,CAAGP,QAAQ,CAAG,IAAI,CAAE;AAC7B,KAAM,CAAAQ,KAAK,CAAGR,QAAQ,CAAGM,QAAQ,CAAGC,GAAG,CAAGX,QAAQ,CAElD,KAAM,CAAAa,oBAAoB,CAAGA,CAACC,MAAM,CAAEC,WAAW,GAAK,CACpD,GAAIA,WAAW,EAAI,CAAC,CAAE,CACpBzB,cAAc,CAACwB,MAAM,CAAC,CACxB,CAAC,IAAM,CACLtB,cAAc,CAACsB,MAAM,CAAEC,WAAW,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,UAAU,CAAG,CACjB,QAAQ,CAAE,GAAG,CACb,SAAS,CAAE,IAAI,CACf,OAAO,CAAE,GACX,CAAC,CAED,GAAIA,UAAU,CAACnB,SAAS,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAE,CACvCjB,WAAW,CAACG,QAAQ,CAAGa,UAAU,CAACnB,SAAS,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAC,CAC7D,CAAC,IAAM,CACLC,KAAK,CAAC,oBAAoB,CAAC,CAC7B,CACF,CAAC,CAED,cAAe,CAAAC,cAAcA,CAAA,CAAG,CAC9B,GAAI,CAAC1B,IAAI,CAAE,CACTyB,KAAK,CAAC,0BAA0B,CAAC,CACjC,OACF,CAEAtB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAwB,SAAS,CAAG,CAChBC,KAAK,CAAEjC,IAAI,CAACkC,GAAG,CAAChB,IAAI,EAAAiB,aAAA,CAAAA,aAAA,IACfjB,IAAI,MACPkB,EAAE,CAAElB,IAAI,CAACmB,GAAG,EACZ,CAAC,CACHd,KAAK,CAAEA,KAAK,CACZe,eAAe,CAAE,gCAAiC;AACpD,CAAC,CAED;AACA,KAAM,CAAA5C,KAAK,CAAC6C,IAAI,CACd,kCAAkC,CAClCP,SAAS,CACT,CAAEQ,OAAO,CAAE,CAAEC,aAAa,WAAAC,MAAA,CAAYtC,KAAK,CAAG,CAAE,CAClD,CAAC,CAED;AACAE,QAAQ,CAAC0B,SAAS,CAAC,CAEnB9B,SAAS,CAAC,CAAC,CACXU,WAAW,CAAC,CAAC,CAAC,CACdF,YAAY,CAAC,EAAE,CAAC,CAEhB;AACA,KAAM,CAAAiC,YAAY,CAAGC,IAAI,CAACC,KAAK,CAACtB,KAAK,CAAC,CACtCO,KAAK,0CAAAY,MAAA,CAA0CC,YAAY,oBAAkB,CAAC,CAChF,CAAE,MAAOG,GAAG,CAAE,CACZhB,KAAK,CAAC,oCAAoC,CAAC,CAC7C,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAEA,GAAID,OAAO,CAAE,CACX,mBAAOX,IAAA,CAACH,cAAc,EAACsD,IAAI,CAAC,OAAO,CAACC,OAAO,CAAC,0BAA0B,CAACC,UAAU,MAAE,CAAC,CACtF,CAEA,mBACEnD,KAAA,SAAAoD,QAAA,eACEpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BpD,KAAA,OAAAoD,QAAA,eACEtD,IAAA,MAAGuD,SAAS,CAAC,sBAAsB,CAAI,CAAC,gBAE1C,EAAI,CAAC,CACJnD,IAAI,CAACoD,MAAM,CAAG,CAAC,eACdtD,KAAA,WAAQuD,OAAO,CAAEnD,SAAU,CAACiD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eACpDtD,IAAA,MAAGuD,SAAS,CAAC,cAAc,CAAI,CAAC,aAElC,EAAQ,CACT,EACE,CAAC,CAELnD,IAAI,CAACoD,MAAM,GAAK,CAAC,cAChBtD,KAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBtD,IAAA,QAAKuD,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BtD,IAAA,MAAGuD,SAAS,CAAC,sBAAsB,CAAI,CAAC,CACrC,CAAC,cACNvD,IAAA,OAAAsD,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BtD,IAAA,MAAAsD,QAAA,CAAG,yDAAuD,CAAG,CAAC,cAC9DpD,KAAA,CAACN,IAAI,EAAC8D,EAAE,CAAC,GAAG,CAACH,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eAC5CtD,IAAA,MAAGuD,SAAS,CAAC,mBAAmB,CAAI,CAAC,oBAEvC,EAAM,CAAC,EACJ,CAAC,cAENrD,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BpD,KAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBtD,IAAA,QAAKuD,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3BpD,KAAA,OAAAoD,QAAA,EAAI,sBAAoB,CAAClD,IAAI,CAACoD,MAAM,CAAC,GAAC,EAAI,CAAC,CACxC,CAAC,cAENxD,IAAA,QAAKuD,SAAS,CAAC,YAAY,CAAAD,QAAA,CACxBlD,IAAI,CAACkC,GAAG,CAAChB,IAAI,OAAAqC,iBAAA,CAAAC,UAAA,oBACZ1D,KAAA,QAAoBqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACvCtD,IAAA,QAAKuD,SAAS,CAAC,YAAY,CAAAD,QAAA,cACzBtD,IAAA,QAAK6D,GAAG,CAAEvC,IAAI,CAACwC,QAAS,CAACC,GAAG,CAAEzC,IAAI,CAAC0C,IAAK,CAAE,CAAC,CACxC,CAAC,cAEN9D,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,OAAIuD,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAEhC,IAAI,CAAC0C,IAAI,CAAK,CAAC,cAC1C9D,KAAA,MAAGqD,SAAS,CAAC,kBAAkB,CAAAD,QAAA,GAAAK,iBAAA,CAC5BrC,IAAI,CAAC2C,WAAW,UAAAN,iBAAA,iBAAhBA,iBAAA,CAAkBO,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAAC,KACvC,EAAG,CAAC,CAEH5C,IAAI,CAAC6C,aAAa,eACjBjE,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBtD,IAAA,MAAGuD,SAAS,CAAC,aAAa,CAAI,CAAC,eAEjC,EAAK,CACN,cAEDvD,IAAA,QAAKuD,SAAS,CAAC,WAAW,CAAAD,QAAA,EAAAM,UAAA,CACvBtC,IAAI,CAAC8C,IAAI,UAAAR,UAAA,iBAATA,UAAA,CAAWS,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC/B,GAAG,CAAC,CAACgC,GAAG,CAAEC,KAAK,gBACrCvE,IAAA,SAAkBuD,SAAS,CAAC,KAAK,CAAAD,QAAA,CAAEgB,GAAG,EAA3BC,KAAkC,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,cAENrE,KAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAC,GAAC,CAAChC,IAAI,CAACC,KAAK,EAAM,CAAC,cACnDvB,IAAA,QAAKuD,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,EACxC,CAAC,cAENpD,KAAA,QAAKqD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCtD,IAAA,WACEyD,OAAO,CAAEA,CAAA,GAAM7B,oBAAoB,CAACN,IAAI,CAACmB,GAAG,CAAEnB,IAAI,CAACE,QAAQ,CAAG,CAAC,CAAE,CACjE+B,SAAS,CAAC,cAAc,CACxBiB,QAAQ,CAAElD,IAAI,CAACE,QAAQ,EAAI,CAAE,CAAA8B,QAAA,cAE7BtD,IAAA,MAAGuD,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,cACTvD,IAAA,SAAMuD,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAAEhC,IAAI,CAACE,QAAQ,CAAO,CAAC,cACzDxB,IAAA,WACEyD,OAAO,CAAEA,CAAA,GAAM7B,oBAAoB,CAACN,IAAI,CAACmB,GAAG,CAAEnB,IAAI,CAACE,QAAQ,CAAG,CAAC,CAAE,CACjE+B,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExBtD,IAAA,MAAGuD,SAAS,CAAC,aAAa,CAAI,CAAC,CACzB,CAAC,EACN,CAAC,cAENvD,IAAA,QAAKuD,SAAS,CAAC,YAAY,CAAAD,QAAA,cACzBpD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,GAAC,CAAC,CAAChC,IAAI,CAACC,KAAK,CAAGD,IAAI,CAACE,QAAQ,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,CAC1E,CAAC,cAENvE,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,WACEyD,OAAO,CAAEA,CAAA,GAAMpD,cAAc,CAACiB,IAAI,CAACmB,GAAG,CAAE,CACxCc,SAAS,CAAC,YAAY,CACtBmB,KAAK,CAAC,kBAAkB,CAAApB,QAAA,cAExBtD,IAAA,MAAGuD,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,cACTvD,IAAA,CAACJ,IAAI,EACH8D,EAAE,aAAAZ,MAAA,CAAcxB,IAAI,CAACmB,GAAG,CAAG,CAC3Bc,SAAS,CAAC,UAAU,CACpBmB,KAAK,CAAC,sBAAsB,CAAApB,QAAA,cAE5BtD,IAAA,MAAGuD,SAAS,CAAC,YAAY,CAAI,CAAC,CAC1B,CAAC,EACJ,CAAC,GAlEEjC,IAAI,CAACmB,GAmEV,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,cAENzC,IAAA,QAAKuD,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3BpD,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,OAAAsD,QAAA,CAAI,eAAa,CAAI,CAAC,cAEtBpD,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BpD,KAAA,SAAAoD,QAAA,EAAM,YAAU,CAAClD,IAAI,CAACoD,MAAM,CAAC,SAAO,EAAM,CAAC,cAC3CtD,KAAA,SAAAoD,QAAA,EAAM,GAAC,CAACnC,QAAQ,CAACsD,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAChC,CAAC,cAENvE,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,SAAAsD,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrBtD,IAAA,SAAAsD,QAAA,CAAO7B,QAAQ,GAAK,CAAC,CAAG,MAAM,KAAAqB,MAAA,CAAOrB,QAAQ,CAACgD,OAAO,CAAC,CAAC,CAAC,CAAE,CAAO,CAAC,EAC/D,CAAC,cAENvE,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,SAAAsD,QAAA,CAAM,KAAG,CAAM,CAAC,cAChBpD,KAAA,SAAAoD,QAAA,EAAM,GAAC,CAAC5B,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAC3B,CAAC,CAEL1D,QAAQ,CAAG,CAAC,eACXb,KAAA,QAAKqD,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCtD,IAAA,SAAAsD,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrBpD,KAAA,SAAAoD,QAAA,EAAM,IAAE,CAACvC,QAAQ,CAAC0D,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EACjC,CACN,cAEDvE,KAAA,QAAKqD,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eACjCtD,IAAA,SAAAsD,QAAA,CAAM,OAAK,CAAM,CAAC,cAClBpD,KAAA,SAAAoD,QAAA,EAAM,GAAC,CAAC3B,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAC7B,CAAC,CAELhD,QAAQ,CAAG,CAAC,eACXvB,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BtD,IAAA,MAAGuD,SAAS,CAAC,oBAAoB,CAAI,CAAC,QACjC,CAAC,CAAC,EAAE,CAAGpC,QAAQ,EAAEsD,OAAO,CAAC,CAAC,CAAC,CAAC,yBACnC,EAAK,CACN,cAEDvE,KAAA,QAAKqD,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BtD,IAAA,OAAAsD,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBpD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BtD,IAAA,UACE2E,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE/D,SAAU,CACjBgE,QAAQ,CAAGC,CAAC,EAAKhE,YAAY,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9CI,WAAW,CAAC,kBAAkB,CAC/B,CAAC,cACFhF,IAAA,WAAQyD,OAAO,CAAE1B,eAAgB,CAACwB,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,OAExD,CAAQ,CAAC,EACN,CAAC,cACNtD,IAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCtD,IAAA,UAAAsD,QAAA,CAAO,6BAA2B,CAAO,CAAC,CACvC,CAAC,EACH,CAAC,cAENpD,KAAA,QAAKqD,SAAS,CAAC,kBAAkB,CAAAD,QAAA,EAC9B7C,IAAI,cACHP,KAAA,WACEuD,OAAO,CAAEtB,cAAe,CACxBoB,SAAS,CAAC,cAAc,CACxBiB,QAAQ,CAAEpE,IAAI,CAACoD,MAAM,GAAK,CAAE,CAAAF,QAAA,eAE5BtD,IAAA,MAAGuD,SAAS,CAAC,oBAAoB,CAAI,CAAC,sBAExC,EAAQ,CAAC,cAETrD,KAAA,QAAKqD,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BtD,IAAA,MAAAsD,QAAA,CAAG,0BAAwB,CAAG,CAAC,cAC/BpD,KAAA,CAACN,IAAI,EAAC8D,EAAE,CAAC,QAAQ,CAACH,SAAS,CAAC,WAAW,CAAAD,QAAA,eACrCtD,IAAA,MAAGuD,SAAS,CAAC,oBAAoB,CAAI,CAAC,QAExC,EAAM,CAAC,EACJ,CACN,cAEDrD,KAAA,CAACN,IAAI,EAAC8D,EAAE,CAAC,GAAG,CAACH,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eACxCtD,IAAA,MAAGuD,SAAS,CAAC,mBAAmB,CAAI,CAAC,oBAEvC,EAAM,CAAC,EACJ,CAAC,cAENrD,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BtD,IAAA,MAAGuD,SAAS,CAAC,mBAAmB,CAAI,CAAC,cACrCvD,IAAA,SAAAsD,QAAA,CAAM,iBAAe,CAAM,CAAC,EACzB,CAAC,cACNpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BtD,IAAA,MAAGuD,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/BvD,IAAA,SAAAsD,QAAA,CAAM,gBAAc,CAAM,CAAC,EACxB,CAAC,cACNpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BtD,IAAA,MAAGuD,SAAS,CAAC,cAAc,CAAI,CAAC,cAChCvD,IAAA,SAAAsD,QAAA,CAAM,eAAa,CAAM,CAAC,EACvB,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CACN,EACE,CAAC,cAENtD,IAAA,UAAOD,GAAG,MAAAuD,QAAA,6/aAojBD,CAAC,EACN,CAAC,CAEX,CAEA,cAAe,CAAAnD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}