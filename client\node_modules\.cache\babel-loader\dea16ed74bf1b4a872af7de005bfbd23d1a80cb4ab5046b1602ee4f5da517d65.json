{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useContext}from'react';import axios from'axios';import{AuthContext}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function AuthModal(_ref){let{isOpen,onClose,initialMode='login'}=_ref;const[mode,setMode]=useState(initialMode);const[formData,setFormData]=useState({name:'',email:'',password:'',confirmPassword:''});const[loading,setLoading]=useState(false);const[error,setError]=useState('');const{setUser,setToken}=useContext(AuthContext);const handleInputChange=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));setError('');// Clear error when user types\n};const handleSubmit=async e=>{e.preventDefault();setLoading(true);setError('');try{if(mode==='register'){if(formData.password!==formData.confirmPassword){setError('Passwords do not match');setLoading(false);return;}await axios.post('http://localhost:5000/api/auth/register',{name:formData.name,email:formData.email,password:formData.password});// Auto-login after registration\nconst loginResponse=await axios.post('http://localhost:5000/api/auth/login',{email:formData.email,password:formData.password});setUser(loginResponse.data.user);setToken(loginResponse.data.token);onClose();}else{const response=await axios.post('http://localhost:5000/api/auth/login',{email:formData.email,password:formData.password});setUser(response.data.user);setToken(response.data.token);onClose();}}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'An error occurred');}finally{setLoading(false);}};const switchMode=()=>{setMode(mode==='login'?'register':'login');setError('');setFormData({name:'',email:'',password:'',confirmPassword:''});};if(!isOpen)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"modal-overlay\",onClick:onClose,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsxs(\"h2\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(mode==='login'?'fa-sign-in-alt':'fa-user-plus')}),mode==='login'?'Welcome Back':'Create Account']}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-times\"})})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"auth-form\",children:[error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-exclamation-circle\"}),error]}),mode==='register'&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:formData.name,onChange:handleInputChange,required:true,placeholder:\"Enter your full name\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,required:true,placeholder:\"Enter your email\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,required:true,placeholder:\"Enter your password\",minLength:\"6\"})]}),mode==='register'&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"confirmPassword\",children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"confirmPassword\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleInputChange,required:true,placeholder:\"Confirm your password\",minLength:\"6\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-btn\",disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-spinner fa-spin\"}),mode==='login'?'Signing In...':'Creating Account...']}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(mode==='login'?'fa-sign-in-alt':'fa-user-plus')}),mode==='login'?'Sign In':'Create Account']})}),/*#__PURE__*/_jsx(\"div\",{className:\"form-footer\",children:/*#__PURE__*/_jsxs(\"p\",{children:[mode==='login'?\"Don't have an account?\":\"Already have an account?\",/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:switchMode,className:\"switch-mode-btn\",children:mode==='login'?'Sign Up':'Sign In'})]})})]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .modal-overlay {\\n          position: fixed;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background: rgba(0, 0, 0, 0.5);\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          z-index: 1000;\\n          padding: 1rem;\\n        }\\n\\n        .modal-content {\\n          background: white;\\n          border-radius: 1rem;\\n          width: 100%;\\n          max-width: 400px;\\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n        }\\n\\n        .modal-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          padding: 2rem 2rem 1rem 2rem;\\n          border-bottom: 1px solid #e5e7eb;\\n        }\\n\\n        .modal-header h2 {\\n          margin: 0;\\n          font-size: 1.5rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .close-btn {\\n          background: none;\\n          border: none;\\n          font-size: 1.25rem;\\n          color: #6b7280;\\n          cursor: pointer;\\n          padding: 0.5rem;\\n          border-radius: 0.375rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .close-btn:hover {\\n          background: #f3f4f6;\\n          color: #374151;\\n        }\\n\\n        .auth-form {\\n          padding: 2rem;\\n        }\\n\\n        .error-message {\\n          background: #fef2f2;\\n          color: #dc2626;\\n          padding: 0.75rem;\\n          border-radius: 0.5rem;\\n          margin-bottom: 1rem;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          font-size: 0.875rem;\\n        }\\n\\n        .form-group {\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .form-group label {\\n          display: block;\\n          margin-bottom: 0.5rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .form-group input {\\n          width: 100%;\\n          padding: 0.75rem;\\n          border: 2px solid #e5e7eb;\\n          border-radius: 0.5rem;\\n          font-size: 1rem;\\n          transition: border-color 0.2s;\\n        }\\n\\n        .form-group input:focus {\\n          outline: none;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .submit-btn {\\n          width: 100%;\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.875rem;\\n          border-radius: 0.5rem;\\n          font-size: 1rem;\\n          font-weight: 500;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .submit-btn:hover:not(:disabled) {\\n          background: var(--primary-dark);\\n        }\\n\\n        .submit-btn:disabled {\\n          opacity: 0.6;\\n          cursor: not-allowed;\\n        }\\n\\n        .form-footer {\\n          margin-top: 1.5rem;\\n          text-align: center;\\n        }\\n\\n        .form-footer p {\\n          color: var(--text-secondary);\\n          margin: 0;\\n        }\\n\\n        .switch-mode-btn {\\n          background: none;\\n          border: none;\\n          color: var(--primary-color);\\n          cursor: pointer;\\n          font-weight: 500;\\n          margin-left: 0.5rem;\\n          text-decoration: underline;\\n        }\\n\\n        .switch-mode-btn:hover {\\n          color: var(--primary-dark);\\n        }\\n      \"})]});}export default AuthModal;", "map": {"version": 3, "names": ["React", "useState", "useContext", "axios", "AuthContext", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AuthModal", "_ref", "isOpen", "onClose", "initialMode", "mode", "setMode", "formData", "setFormData", "name", "email", "password", "confirmPassword", "loading", "setLoading", "error", "setError", "setUser", "setToken", "handleInputChange", "e", "_objectSpread", "target", "value", "handleSubmit", "preventDefault", "post", "loginResponse", "data", "user", "token", "response", "err", "_err$response", "_err$response$data", "message", "switchMode", "className", "onClick", "children", "stopPropagation", "concat", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "disabled"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/AuthModal.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\nimport axios from 'axios';\nimport { AuthContext } from '../context/AuthContext';\n\nfunction AuthModal({ isOpen, onClose, initialMode = 'login' }) {\n  const [mode, setMode] = useState(initialMode);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const { setUser, setToken } = useContext(AuthContext);\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      if (mode === 'register') {\n        if (formData.password !== formData.confirmPassword) {\n          setError('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n\n        await axios.post('http://localhost:5000/api/auth/register', {\n          name: formData.name,\n          email: formData.email,\n          password: formData.password\n        });\n\n        // Auto-login after registration\n        const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n\n        setUser(loginResponse.data.user);\n        setToken(loginResponse.data.token);\n        onClose();\n      } else {\n        const response = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n\n        setUser(response.data.user);\n        setToken(response.data.token);\n        onClose();\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'register' : 'login');\n    setError('');\n    setFormData({\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>\n            <i className={`fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`}></i>\n            {mode === 'login' ? 'Welcome Back' : 'Create Account'}\n          </h2>\n          <button className=\"close-btn\" onClick={onClose}>\n            <i className=\"fas fa-times\"></i>\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          {error && (\n            <div className=\"error-message\">\n              <i className=\"fas fa-exclamation-circle\"></i>\n              {error}\n            </div>\n          )}\n\n          {mode === 'register' && (\n            <div className=\"form-group\">\n              <label htmlFor=\"name\">Full Name</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleInputChange}\n                required\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email Address</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n              placeholder=\"Enter your password\"\n              minLength=\"6\"\n            />\n          </div>\n\n          {mode === 'register' && (\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\">Confirm Password</label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required\n                placeholder=\"Confirm your password\"\n                minLength=\"6\"\n              />\n            </div>\n          )}\n\n          <button type=\"submit\" className=\"submit-btn\" disabled={loading}>\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin\"></i>\n                {mode === 'login' ? 'Signing In...' : 'Creating Account...'}\n              </>\n            ) : (\n              <>\n                <i className={`fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`}></i>\n                {mode === 'login' ? 'Sign In' : 'Create Account'}\n              </>\n            )}\n          </button>\n\n          <div className=\"form-footer\">\n            <p>\n              {mode === 'login' ? \"Don't have an account?\" : \"Already have an account?\"}\n              <button type=\"button\" onClick={switchMode} className=\"switch-mode-btn\">\n                {mode === 'login' ? 'Sign Up' : 'Sign In'}\n              </button>\n            </p>\n          </div>\n        </form>\n      </div>\n\n      <style jsx>{`\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 400px;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .auth-form {\n          padding: 2rem;\n        }\n\n        .error-message {\n          background: #fef2f2;\n          color: #dc2626;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          margin-bottom: 1rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid #e5e7eb;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .submit-btn {\n          width: 100%;\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.875rem;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .submit-btn:hover:not(:disabled) {\n          background: var(--primary-dark);\n        }\n\n        .submit-btn:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .form-footer {\n          margin-top: 1.5rem;\n          text-align: center;\n        }\n\n        .form-footer p {\n          color: var(--text-secondary);\n          margin: 0;\n        }\n\n        .switch-mode-btn {\n          background: none;\n          border: none;\n          color: var(--primary-color);\n          cursor: pointer;\n          font-weight: 500;\n          margin-left: 0.5rem;\n          text-decoration: underline;\n        }\n\n        .switch-mode-btn:hover {\n          color: var(--primary-dark);\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default AuthModal;\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,UAAU,KAAQ,OAAO,CACnD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,QAAS,CAAAC,SAASA,CAAAC,IAAA,CAA6C,IAA5C,CAAEC,MAAM,CAAEC,OAAO,CAAEC,WAAW,CAAG,OAAQ,CAAC,CAAAH,IAAA,CAC3D,KAAM,CAACI,IAAI,CAAEC,OAAO,CAAC,CAAGhB,QAAQ,CAACc,WAAW,CAAC,CAC7C,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,CACvCmB,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAE2B,OAAO,CAAEC,QAAS,CAAC,CAAG3B,UAAU,CAACE,WAAW,CAAC,CAErD,KAAM,CAAA0B,iBAAiB,CAAIC,CAAC,EAAK,CAC/BZ,WAAW,CAAAa,aAAA,CAAAA,aAAA,IACNd,QAAQ,MACX,CAACa,CAAC,CAACE,MAAM,CAACb,IAAI,EAAGW,CAAC,CAACE,MAAM,CAACC,KAAK,EAChC,CAAC,CACFP,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChB,CAAC,CAED,KAAM,CAAAQ,YAAY,CAAG,KAAO,CAAAJ,CAAC,EAAK,CAChCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBX,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,GAAIX,IAAI,GAAK,UAAU,CAAE,CACvB,GAAIE,QAAQ,CAACI,QAAQ,GAAKJ,QAAQ,CAACK,eAAe,CAAE,CAClDI,QAAQ,CAAC,wBAAwB,CAAC,CAClCF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,KAAM,CAAAtB,KAAK,CAACkC,IAAI,CAAC,yCAAyC,CAAE,CAC1DjB,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QACrB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAgB,aAAa,CAAG,KAAM,CAAAnC,KAAK,CAACkC,IAAI,CAAC,sCAAsC,CAAE,CAC7EhB,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QACrB,CAAC,CAAC,CAEFM,OAAO,CAACU,aAAa,CAACC,IAAI,CAACC,IAAI,CAAC,CAChCX,QAAQ,CAACS,aAAa,CAACC,IAAI,CAACE,KAAK,CAAC,CAClC3B,OAAO,CAAC,CAAC,CACX,CAAC,IAAM,CACL,KAAM,CAAA4B,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACkC,IAAI,CAAC,sCAAsC,CAAE,CACxEhB,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QACrB,CAAC,CAAC,CAEFM,OAAO,CAACc,QAAQ,CAACH,IAAI,CAACC,IAAI,CAAC,CAC3BX,QAAQ,CAACa,QAAQ,CAACH,IAAI,CAACE,KAAK,CAAC,CAC7B3B,OAAO,CAAC,CAAC,CACX,CACF,CAAE,MAAO6B,GAAG,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACZlB,QAAQ,CAAC,EAAAiB,aAAA,CAAAD,GAAG,CAACD,QAAQ,UAAAE,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcL,IAAI,UAAAM,kBAAA,iBAAlBA,kBAAA,CAAoBC,OAAO,GAAI,mBAAmB,CAAC,CAC9D,CAAC,OAAS,CACRrB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsB,UAAU,CAAGA,CAAA,GAAM,CACvB9B,OAAO,CAACD,IAAI,GAAK,OAAO,CAAG,UAAU,CAAG,OAAO,CAAC,CAChDW,QAAQ,CAAC,EAAE,CAAC,CACZR,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACJ,CAAC,CAED,GAAI,CAACV,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEL,KAAA,QAAKwC,SAAS,CAAC,eAAe,CAACC,OAAO,CAAEnC,OAAQ,CAAAoC,QAAA,eAC9C1C,KAAA,QAAKwC,SAAS,CAAC,eAAe,CAACC,OAAO,CAAGlB,CAAC,EAAKA,CAAC,CAACoB,eAAe,CAAC,CAAE,CAAAD,QAAA,eACjE1C,KAAA,QAAKwC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,MAAG0C,SAAS,QAAAI,MAAA,CAASpC,IAAI,GAAK,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAI,CAAC,CAChFA,IAAI,GAAK,OAAO,CAAG,cAAc,CAAG,gBAAgB,EACnD,CAAC,cACLV,IAAA,WAAQ0C,SAAS,CAAC,WAAW,CAACC,OAAO,CAAEnC,OAAQ,CAAAoC,QAAA,cAC7C5C,IAAA,MAAG0C,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,EACN,CAAC,cAENxC,KAAA,SAAM6C,QAAQ,CAAElB,YAAa,CAACa,SAAS,CAAC,WAAW,CAAAE,QAAA,EAChDxB,KAAK,eACJlB,KAAA,QAAKwC,SAAS,CAAC,eAAe,CAAAE,QAAA,eAC5B5C,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC5CtB,KAAK,EACH,CACN,CAEAV,IAAI,GAAK,UAAU,eAClBR,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB5C,IAAA,UAAOgD,OAAO,CAAC,MAAM,CAAAJ,QAAA,CAAC,WAAS,CAAO,CAAC,cACvC5C,IAAA,UACEiD,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,MAAM,CACTpC,IAAI,CAAC,MAAM,CACXc,KAAK,CAAEhB,QAAQ,CAACE,IAAK,CACrBqC,QAAQ,CAAE3B,iBAAkB,CAC5B4B,QAAQ,MACRC,WAAW,CAAC,sBAAsB,CACnC,CAAC,EACC,CACN,cAEDnD,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB5C,IAAA,UAAOgD,OAAO,CAAC,OAAO,CAAAJ,QAAA,CAAC,eAAa,CAAO,CAAC,cAC5C5C,IAAA,UACEiD,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACVpC,IAAI,CAAC,OAAO,CACZc,KAAK,CAAEhB,QAAQ,CAACG,KAAM,CACtBoC,QAAQ,CAAE3B,iBAAkB,CAC5B4B,QAAQ,MACRC,WAAW,CAAC,kBAAkB,CAC/B,CAAC,EACC,CAAC,cAENnD,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB5C,IAAA,UAAOgD,OAAO,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1C5C,IAAA,UACEiD,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACbpC,IAAI,CAAC,UAAU,CACfc,KAAK,CAAEhB,QAAQ,CAACI,QAAS,CACzBmC,QAAQ,CAAE3B,iBAAkB,CAC5B4B,QAAQ,MACRC,WAAW,CAAC,qBAAqB,CACjCC,SAAS,CAAC,GAAG,CACd,CAAC,EACC,CAAC,CAEL5C,IAAI,GAAK,UAAU,eAClBR,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB5C,IAAA,UAAOgD,OAAO,CAAC,iBAAiB,CAAAJ,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACzD5C,IAAA,UACEiD,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,iBAAiB,CACpBpC,IAAI,CAAC,iBAAiB,CACtBc,KAAK,CAAEhB,QAAQ,CAACK,eAAgB,CAChCkC,QAAQ,CAAE3B,iBAAkB,CAC5B4B,QAAQ,MACRC,WAAW,CAAC,uBAAuB,CACnCC,SAAS,CAAC,GAAG,CACd,CAAC,EACC,CACN,cAEDtD,IAAA,WAAQiD,IAAI,CAAC,QAAQ,CAACP,SAAS,CAAC,YAAY,CAACa,QAAQ,CAAErC,OAAQ,CAAA0B,QAAA,CAC5D1B,OAAO,cACNhB,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,MAAG0C,SAAS,CAAC,wBAAwB,CAAI,CAAC,CACzChC,IAAI,GAAK,OAAO,CAAG,eAAe,CAAG,qBAAqB,EAC3D,CAAC,cAEHR,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,MAAG0C,SAAS,QAAAI,MAAA,CAASpC,IAAI,GAAK,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAI,CAAC,CAChFA,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,gBAAgB,EAChD,CACH,CACK,CAAC,cAETV,IAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1B1C,KAAA,MAAA0C,QAAA,EACGlC,IAAI,GAAK,OAAO,CAAG,wBAAwB,CAAG,0BAA0B,cACzEV,IAAA,WAAQiD,IAAI,CAAC,QAAQ,CAACN,OAAO,CAAEF,UAAW,CAACC,SAAS,CAAC,iBAAiB,CAAAE,QAAA,CACnElC,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SAAS,CACnC,CAAC,EACR,CAAC,CACD,CAAC,EACF,CAAC,EACJ,CAAC,cAENV,IAAA,UAAOD,GAAG,MAAA6C,QAAA,y/GAmJD,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAvC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}