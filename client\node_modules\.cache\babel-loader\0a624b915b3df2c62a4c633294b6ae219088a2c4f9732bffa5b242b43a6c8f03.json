{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\context\\\\CartContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CartContext = /*#__PURE__*/createContext();\nexport function CartProvider({\n  children\n}) {\n  _s();\n  const [cart, setCart] = useState([]);\n  function addToCart(product) {\n    setCart(prev => prev.some(item => item._id === product._id) ? prev.map(item => item._id === product._id ? {\n      ...item,\n      quantity: item.quantity + 1\n    } : item) : [...prev, {\n      ...product,\n      quantity: 1\n    }]);\n  }\n  function removeFromCart(productId) {\n    setCart(prev => prev.filter(item => item._id !== productId));\n  }\n  function updateQuantity(productId, newQuantity) {\n    setCart(prev => prev.map(item => item._id === productId ? {\n      ...item,\n      quantity: newQuantity\n    } : item));\n  }\n  function clearCart() {\n    setCart([]);\n  }\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: {\n      cart,\n      addToCart,\n      removeFromCart,\n      clearCart\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_s(CartProvider, \"ZqFaEIYkzI5UoYUmTgmqHbYYm/0=\");\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "jsxDEV", "_jsxDEV", "CartContext", "CartProvider", "children", "_s", "cart", "setCart", "addToCart", "product", "prev", "some", "item", "_id", "map", "quantity", "removeFromCart", "productId", "filter", "updateQuantity", "newQuantity", "clearCart", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/CartContext.jsx"], "sourcesContent": ["import React, { createContext, useState } from 'react';\r\n\r\nexport const CartContext = createContext();\r\n\r\nexport function CartProvider({ children }) {\r\n  const [cart, setCart] = useState([]);\r\n\r\n  function addToCart(product) {\r\n    setCart(prev =>\r\n      prev.some(item => item._id === product._id)\r\n        ? prev.map(item =>\r\n            item._id === product._id ? { ...item, quantity: item.quantity + 1 } : item)\r\n        : [...prev, { ...product, quantity: 1 }]\r\n    );\r\n  }\r\n\r\n  function removeFromCart(productId) {\r\n    setCart(prev => prev.filter(item => item._id !== productId));\r\n  }\r\n\r\n  function updateQuantity(productId, newQuantity) {\r\n    setCart(prev => prev.map(item =>\r\n      item._id === productId\r\n        ? { ...item, quantity: newQuantity }\r\n        : item\r\n    ));\r\n  }\r\n\r\n  function clearCart() {\r\n    setCart([]);\r\n  }\r\n\r\n  return (\r\n    <CartContext.Provider value={{ cart, addToCart, removeFromCart, clearCart }}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,OAAO,MAAMC,WAAW,gBAAGJ,aAAa,CAAC,CAAC;AAE1C,OAAO,SAASK,YAAYA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACzC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAEpC,SAASS,SAASA,CAACC,OAAO,EAAE;IAC1BF,OAAO,CAACG,IAAI,IACVA,IAAI,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKJ,OAAO,CAACI,GAAG,CAAC,GACvCH,IAAI,CAACI,GAAG,CAACF,IAAI,IACXA,IAAI,CAACC,GAAG,KAAKJ,OAAO,CAACI,GAAG,GAAG;MAAE,GAAGD,IAAI;MAAEG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG;IAAE,CAAC,GAAGH,IAAI,CAAC,GAC7E,CAAC,GAAGF,IAAI,EAAE;MAAE,GAAGD,OAAO;MAAEM,QAAQ,EAAE;IAAE,CAAC,CAC3C,CAAC;EACH;EAEA,SAASC,cAAcA,CAACC,SAAS,EAAE;IACjCV,OAAO,CAACG,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKI,SAAS,CAAC,CAAC;EAC9D;EAEA,SAASE,cAAcA,CAACF,SAAS,EAAEG,WAAW,EAAE;IAC9Cb,OAAO,CAACG,IAAI,IAAIA,IAAI,CAACI,GAAG,CAACF,IAAI,IAC3BA,IAAI,CAACC,GAAG,KAAKI,SAAS,GAClB;MAAE,GAAGL,IAAI;MAAEG,QAAQ,EAAEK;IAAY,CAAC,GAClCR,IACN,CAAC,CAAC;EACJ;EAEA,SAASS,SAASA,CAAA,EAAG;IACnBd,OAAO,CAAC,EAAE,CAAC;EACb;EAEA,oBACEN,OAAA,CAACC,WAAW,CAACoB,QAAQ;IAACC,KAAK,EAAE;MAAEjB,IAAI;MAAEE,SAAS;MAAEQ,cAAc;MAAEK;IAAU,CAAE;IAAAjB,QAAA,EACzEA;EAAQ;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAACtB,EAAA,CAjCeF,YAAY;AAAAyB,EAAA,GAAZzB,YAAY;AAAA,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}