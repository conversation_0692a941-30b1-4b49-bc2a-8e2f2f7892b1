{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import Header from'./components/Header';import Footer from'./components/Footer';import Home from'./pages/Home';import ProductPage from'./pages/ProductPage';import CartPage from'./pages/CartPage';import ProfilePage from'./pages/ProfilePage';import LoginPage from'./pages/LoginPage';import WishlistPage from'./pages/WishlistPage';import AdminPage from'./pages/AdminPage';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"app\",children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"div\",{className:\"main-content\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/product/:id\",element:/*#__PURE__*/_jsx(ProductPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/cart\",element:/*#__PURE__*/_jsx(CartPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProfilePage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/wishlist\",element:/*#__PURE__*/_jsx(WishlistPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(AdminPage,{})})]})}),/*#__PURE__*/_jsx(Footer,{})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,global:true,children:\"\\n        .app {\\n          min-height: 100vh;\\n          display: flex;\\n          flex-direction: column;\\n        }\\n\\n        .main-content {\\n          flex: 1;\\n        }\\n      \"})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Header", "Footer", "Home", "ProductPage", "CartPage", "ProfilePage", "LoginPage", "WishlistPage", "AdminPage", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "global"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport Header from './components/Header';\r\nimport Footer from './components/Footer';\r\nimport Home from './pages/Home';\r\nimport ProductPage from './pages/ProductPage';\r\nimport CartPage from './pages/CartPage';\r\nimport ProfilePage from './pages/ProfilePage';\r\nimport LoginPage from './pages/LoginPage';\r\nimport WishlistPage from './pages/WishlistPage';\r\nimport AdminPage from './pages/AdminPage';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"app\">\r\n        <Header />\r\n        <div className=\"main-content\">\r\n          <Routes>\r\n            <Route path=\"/\" element={<Home />} />\r\n            <Route path=\"/product/:id\" element={<ProductPage />} />\r\n            <Route path=\"/cart\" element={<CartPage />} />\r\n            <Route path=\"/profile\" element={<ProfilePage />} />\r\n            <Route path=\"/login\" element={<LoginPage />} />\r\n            <Route path=\"/wishlist\" element={<WishlistPage />} />\r\n            <Route path=\"/admin\" element={<AdminPage />} />\r\n          </Routes>\r\n        </div>\r\n        <Footer />\r\n      </div>\r\n\r\n      <style jsx global>{`\r\n        .app {\r\n          min-height: 100vh;\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n\r\n        .main-content {\r\n          flex: 1;\r\n        }\r\n      `}</style>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,KAAA,CAACf,MAAM,EAAAiB,QAAA,eACLF,KAAA,QAAKG,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBJ,IAAA,CAACV,MAAM,GAAE,CAAC,cACVU,IAAA,QAAKK,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3BF,KAAA,CAACd,MAAM,EAAAgB,QAAA,eACLJ,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACR,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCQ,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACP,WAAW,GAAE,CAAE,CAAE,CAAC,cACvDO,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEP,IAAA,CAACN,QAAQ,GAAE,CAAE,CAAE,CAAC,cAC7CM,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDK,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACJ,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CI,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACH,YAAY,GAAE,CAAE,CAAE,CAAC,cACrDG,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACF,SAAS,GAAE,CAAE,CAAE,CAAC,EACzC,CAAC,CACN,CAAC,cACNE,IAAA,CAACT,MAAM,GAAE,CAAC,EACP,CAAC,cAENS,IAAA,UAAOD,GAAG,MAACS,MAAM,MAAAJ,QAAA,4LAUR,CAAC,EACJ,CAAC,CAEb,CAEA,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}