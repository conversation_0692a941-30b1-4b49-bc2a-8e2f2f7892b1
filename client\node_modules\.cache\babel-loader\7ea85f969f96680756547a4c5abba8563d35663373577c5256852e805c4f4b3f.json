{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport ProductPage from './pages/ProductPage';\nimport CartPage from './pages/CartPage';\nimport ProfilePage from './pages/ProfilePage';\nimport LoginPage from './pages/LoginPage';\nimport WishlistPage from './pages/WishlistPage';\nimport AdminPage from './pages/AdminPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/product/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProductPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cart\",\n            element: /*#__PURE__*/_jsxDEV(CartPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/wishlist\",\n            element: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      global: true,\n      children: `\n        .app {\n          min-height: 100vh;\n          display: flex;\n          flex-direction: column;\n        }\n\n        .main-content {\n          flex: 1;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Header", "Footer", "Home", "ProductPage", "CartPage", "ProfilePage", "LoginPage", "WishlistPage", "AdminPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "jsx", "global", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport Header from './components/Header';\r\nimport Footer from './components/Footer';\r\nimport Home from './pages/Home';\r\nimport ProductPage from './pages/ProductPage';\r\nimport CartPage from './pages/CartPage';\r\nimport ProfilePage from './pages/ProfilePage';\r\nimport LoginPage from './pages/LoginPage';\r\nimport WishlistPage from './pages/WishlistPage';\r\nimport AdminPage from './pages/AdminPage';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"app\">\r\n        <Header />\r\n        <div className=\"main-content\">\r\n          <Routes>\r\n            <Route path=\"/\" element={<Home />} />\r\n            <Route path=\"/product/:id\" element={<ProductPage />} />\r\n            <Route path=\"/cart\" element={<CartPage />} />\r\n            <Route path=\"/profile\" element={<ProfilePage />} />\r\n            <Route path=\"/login\" element={<LoginPage />} />\r\n            <Route path=\"/wishlist\" element={<WishlistPage />} />\r\n          </Routes>\r\n        </div>\r\n        <Footer />\r\n      </div>\r\n\r\n      <style jsx global>{`\r\n        .app {\r\n          min-height: 100vh;\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n\r\n        .main-content {\r\n          flex: 1;\r\n        }\r\n      `}</style>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,MAAM;IAAAe,QAAA,gBACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA,CAACV,MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3BF,OAAA,CAACZ,MAAM;UAAAc,QAAA,gBACLF,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACR,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,cAAc;YAACC,OAAO,eAAET,OAAA,CAACP,WAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAET,OAAA,CAACN,QAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACL,WAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAET,OAAA,CAACJ,SAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAET,OAAA,CAACH,YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNP,OAAA,CAACT,MAAM;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAENP,OAAA;MAAOU,GAAG;MAACC,MAAM;MAAAT,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAACK,EAAA,GA/BQX,GAAG;AAiCZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}