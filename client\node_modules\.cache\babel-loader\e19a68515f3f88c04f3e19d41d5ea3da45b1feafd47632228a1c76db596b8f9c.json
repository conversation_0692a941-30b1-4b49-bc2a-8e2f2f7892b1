{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import axios from'axios';import LoadingSpinner from'../components/LoadingSpinner';import ErrorMessage from'../components/ErrorMessage';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AdminPage(){const[products,setProducts]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[showAddForm,setShowAddForm]=useState(false);const[editingProduct,setEditingProduct]=useState(null);const[formData,setFormData]=useState({name:'',description:'',imageUrl:'',price:'',tags:'',isEcoFriendly:false});useEffect(()=>{fetchProducts();},[]);const fetchProducts=async()=>{try{setLoading(true);const response=await axios.get('http://localhost:5000/api/products');setProducts(response.data);}catch(err){setError('Failed to load products');}finally{setLoading(false);}};const handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));};const handleSubmit=async e=>{e.preventDefault();try{const productData=_objectSpread(_objectSpread({},formData),{},{price:parseFloat(formData.price),tags:formData.tags.split(',').map(tag=>tag.trim()).filter(tag=>tag)});if(editingProduct){// Update existing product (simulated)\nconst updatedProducts=products.map(p=>p._id===editingProduct._id?_objectSpread(_objectSpread({},p),productData):p);setProducts(updatedProducts);}else{// Add new product (simulated)\nconst newProduct=_objectSpread({_id:Date.now().toString()},productData);setProducts([newProduct,...products]);}resetForm();}catch(err){setError('Failed to save product');}};const handleEdit=product=>{var _product$tags;setEditingProduct(product);setFormData({name:product.name,description:product.description,imageUrl:product.imageUrl,price:product.price.toString(),tags:((_product$tags=product.tags)===null||_product$tags===void 0?void 0:_product$tags.join(', '))||'',isEcoFriendly:product.isEcoFriendly});setShowAddForm(true);};const handleDelete=productId=>{if(window.confirm('Are you sure you want to delete this product?')){setProducts(products.filter(p=>p._id!==productId));}};const resetForm=()=>{setFormData({name:'',description:'',imageUrl:'',price:'',tags:'',isEcoFriendly:false});setEditingProduct(null);setShowAddForm(false);};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{size:\"large\",message:\"Loading admin panel...\",fullScreen:true});}if(error){return/*#__PURE__*/_jsx(ErrorMessage,{title:\"Admin Panel Error\",message:error,onRetry:fetchProducts});}return/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-header\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-cog\"}),\"Product Management\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddForm(true),className:\"add-product-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus\"}),\"Add New Product\"]})]}),showAddForm&&/*#__PURE__*/_jsx(\"div\",{className:\"product-form-modal\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:editingProduct?'Edit Product':'Add New Product'}),/*#__PURE__*/_jsx(\"button\",{onClick:resetForm,className:\"close-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-times\"})})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"product-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"Product Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:formData.name,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"price\",children:\"Price *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"price\",name:\"price\",value:formData.price,onChange:handleInputChange,step:\"0.01\",min:\"0\",required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"description\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"description\",name:\"description\",value:formData.description,onChange:handleInputChange,rows:\"3\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"imageUrl\",children:\"Image URL\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",id:\"imageUrl\",name:\"imageUrl\",value:formData.imageUrl,onChange:handleInputChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"tags\",children:\"Tags (comma-separated)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"tags\",name:\"tags\",value:formData.tags,onChange:handleInputChange,placeholder:\"eco-friendly, sustainable, organic\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"isEcoFriendly\",checked:formData.isEcoFriendly,onChange:handleInputChange}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),\"Eco-Friendly Product\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:resetForm,className:\"cancel-btn\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"save-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-save\"}),editingProduct?'Update Product':'Add Product']})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"products-table\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"table-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[\"All Products (\",products.length,\")\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Image\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Price\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Eco-Friendly\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Tags\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:products.map(product=>{var _product$description,_product$tags2,_product$tags3;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"img\",{src:product.imageUrl,alt:product.name,className:\"product-thumbnail\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"product-name\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-description\",children:[(_product$description=product.description)===null||_product$description===void 0?void 0:_product$description.substring(0,50),\"...\"]})]}),/*#__PURE__*/_jsxs(\"td\",{className:\"price-cell\",children:[\"$\",product.price]}),/*#__PURE__*/_jsx(\"td\",{children:product.isEcoFriendly?/*#__PURE__*/_jsxs(\"span\",{className:\"eco-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-leaf\"}),\"Yes\"]}):/*#__PURE__*/_jsx(\"span\",{className:\"not-eco\",children:\"No\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"tags-cell\",children:[(_product$tags2=product.tags)===null||_product$tags2===void 0?void 0:_product$tags2.slice(0,2).map((tag,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"tag\",children:tag},index)),((_product$tags3=product.tags)===null||_product$tags3===void 0?void 0:_product$tags3.length)>2&&/*#__PURE__*/_jsxs(\"span\",{className:\"tag-more\",children:[\"+\",product.tags.length-2]})]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEdit(product),className:\"edit-btn\",title:\"Edit product\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-edit\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDelete(product._id),className:\"delete-btn\",title:\"Delete product\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trash\"})})]})})]},product._id);})})]})})]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .admin-container {\\n          max-width: 1400px;\\n          margin: 0 auto;\\n          padding: 2rem;\\n        }\\n\\n        .admin-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 2rem;\\n          padding-bottom: 1rem;\\n          border-bottom: 2px solid var(--border-color);\\n        }\\n\\n        .admin-header h1 {\\n          font-size: 2rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n          margin: 0;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n        }\\n\\n        .add-product-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .add-product-btn:hover {\\n          background: var(--primary-dark);\\n          transform: translateY(-1px);\\n        }\\n\\n        .product-form-modal {\\n          position: fixed;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background: rgba(0, 0, 0, 0.5);\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          z-index: 1000;\\n          padding: 1rem;\\n        }\\n\\n        .modal-content {\\n          background: white;\\n          border-radius: 1rem;\\n          width: 100%;\\n          max-width: 600px;\\n          max-height: 90vh;\\n          overflow-y: auto;\\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n        }\\n\\n        .modal-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          padding: 2rem 2rem 1rem 2rem;\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        .modal-header h2 {\\n          margin: 0;\\n          font-size: 1.5rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .close-btn {\\n          background: none;\\n          border: none;\\n          font-size: 1.25rem;\\n          color: var(--text-secondary);\\n          cursor: pointer;\\n          padding: 0.5rem;\\n          border-radius: 0.375rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .close-btn:hover {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n        }\\n\\n        .product-form {\\n          padding: 2rem;\\n        }\\n\\n        .form-row {\\n          display: grid;\\n          grid-template-columns: 1fr 1fr;\\n          gap: 1rem;\\n        }\\n\\n        .form-group {\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .form-group label {\\n          display: block;\\n          margin-bottom: 0.5rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .form-group input,\\n        .form-group textarea {\\n          width: 100%;\\n          padding: 0.75rem;\\n          border: 2px solid var(--border-color);\\n          border-radius: 0.5rem;\\n          font-size: 1rem;\\n          transition: border-color 0.2s;\\n        }\\n\\n        .form-group input:focus,\\n        .form-group textarea:focus {\\n          outline: none;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .checkbox-label {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          cursor: pointer;\\n        }\\n\\n        .form-actions {\\n          display: flex;\\n          gap: 1rem;\\n          justify-content: flex-end;\\n          margin-top: 2rem;\\n        }\\n\\n        .cancel-btn {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          border: 1px solid var(--border-color);\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n        }\\n\\n        .save-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .products-table {\\n          background: white;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n          overflow: hidden;\\n        }\\n\\n        .table-header {\\n          padding: 1.5rem 2rem;\\n          border-bottom: 1px solid var(--border-color);\\n          background: var(--bg-secondary);\\n        }\\n\\n        .table-header h3 {\\n          margin: 0;\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .table-container {\\n          overflow-x: auto;\\n        }\\n\\n        table {\\n          width: 100%;\\n          border-collapse: collapse;\\n        }\\n\\n        th, td {\\n          padding: 1rem;\\n          text-align: left;\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        th {\\n          background: var(--bg-secondary);\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .product-thumbnail {\\n          width: 60px;\\n          height: 60px;\\n          object-fit: cover;\\n          border-radius: 0.5rem;\\n        }\\n\\n        .product-name {\\n          font-weight: 500;\\n          color: var(--text-primary);\\n          margin-bottom: 0.25rem;\\n        }\\n\\n        .product-description {\\n          font-size: 0.875rem;\\n          color: var(--text-secondary);\\n        }\\n\\n        .price-cell {\\n          font-weight: 600;\\n          color: var(--primary-color);\\n          font-size: 1.125rem;\\n        }\\n\\n        .eco-badge {\\n          background: var(--secondary-color);\\n          color: white;\\n          padding: 0.25rem 0.75rem;\\n          border-radius: 9999px;\\n          font-size: 0.75rem;\\n          font-weight: 500;\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.25rem;\\n        }\\n\\n        .not-eco {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .tags-cell {\\n          display: flex;\\n          flex-wrap: wrap;\\n          gap: 0.25rem;\\n        }\\n\\n        .tag {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          padding: 0.125rem 0.5rem;\\n          border-radius: 0.375rem;\\n          font-size: 0.75rem;\\n          font-weight: 500;\\n        }\\n\\n        .tag-more {\\n          background: var(--text-secondary);\\n          color: white;\\n          padding: 0.125rem 0.5rem;\\n          border-radius: 0.375rem;\\n          font-size: 0.75rem;\\n          font-weight: 500;\\n        }\\n\\n        .action-buttons {\\n          display: flex;\\n          gap: 0.5rem;\\n        }\\n\\n        .edit-btn, .delete-btn {\\n          background: none;\\n          border: 1px solid var(--border-color);\\n          padding: 0.5rem;\\n          border-radius: 0.375rem;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n        }\\n\\n        .edit-btn {\\n          color: var(--primary-color);\\n        }\\n\\n        .edit-btn:hover {\\n          background: var(--primary-color);\\n          color: white;\\n        }\\n\\n        .delete-btn {\\n          color: #ef4444;\\n        }\\n\\n        .delete-btn:hover {\\n          background: #ef4444;\\n          color: white;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .admin-header {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: flex-start;\\n          }\\n\\n          .form-row {\\n            grid-template-columns: 1fr;\\n          }\\n\\n          .table-container {\\n            font-size: 0.875rem;\\n          }\\n\\n          th, td {\\n            padding: 0.75rem 0.5rem;\\n          }\\n        }\\n      \"})]});}export default AdminPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "LoadingSpinner", "ErrorMessage", "jsx", "_jsx", "jsxs", "_jsxs", "AdminPage", "products", "setProducts", "loading", "setLoading", "error", "setError", "showAddForm", "setShowAddForm", "editingProduct", "setEditingProduct", "formData", "setFormData", "name", "description", "imageUrl", "price", "tags", "isEcoFriendly", "fetchProducts", "response", "get", "data", "err", "handleInputChange", "e", "value", "type", "checked", "target", "prev", "_objectSpread", "handleSubmit", "preventDefault", "productData", "parseFloat", "split", "map", "tag", "trim", "filter", "updatedProducts", "p", "_id", "newProduct", "Date", "now", "toString", "resetForm", "handleEdit", "product", "_product$tags", "join", "handleDelete", "productId", "window", "confirm", "size", "message", "fullScreen", "title", "onRetry", "children", "className", "onClick", "onSubmit", "htmlFor", "id", "onChange", "required", "step", "min", "rows", "placeholder", "length", "_product$description", "_product$tags2", "_product$tags3", "src", "alt", "substring", "slice", "index"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/AdminPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nfunction AdminPage() {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    imageUrl: '',\n    price: '',\n    tags: '',\n    isEcoFriendly: false\n  });\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)\n      };\n\n      if (editingProduct) {\n        // Update existing product (simulated)\n        const updatedProducts = products.map(p => \n          p._id === editingProduct._id ? { ...p, ...productData } : p\n        );\n        setProducts(updatedProducts);\n      } else {\n        // Add new product (simulated)\n        const newProduct = {\n          _id: Date.now().toString(),\n          ...productData\n        };\n        setProducts([newProduct, ...products]);\n      }\n\n      resetForm();\n    } catch (err) {\n      setError('Failed to save product');\n    }\n  };\n\n  const handleEdit = (product) => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description,\n      imageUrl: product.imageUrl,\n      price: product.price.toString(),\n      tags: product.tags?.join(', ') || '',\n      isEcoFriendly: product.isEcoFriendly\n    });\n    setShowAddForm(true);\n  };\n\n  const handleDelete = (productId) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      setProducts(products.filter(p => p._id !== productId));\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      imageUrl: '',\n      price: '',\n      tags: '',\n      isEcoFriendly: false\n    });\n    setEditingProduct(null);\n    setShowAddForm(false);\n  };\n\n  if (loading) {\n    return <LoadingSpinner size=\"large\" message=\"Loading admin panel...\" fullScreen />;\n  }\n\n  if (error) {\n    return <ErrorMessage title=\"Admin Panel Error\" message={error} onRetry={fetchProducts} />;\n  }\n\n  return (\n    <main>\n      <div className=\"admin-container\">\n        <div className=\"admin-header\">\n          <h1>\n            <i className=\"fas fa-cog\"></i>\n            Product Management\n          </h1>\n          <button \n            onClick={() => setShowAddForm(true)}\n            className=\"add-product-btn\"\n          >\n            <i className=\"fas fa-plus\"></i>\n            Add New Product\n          </button>\n        </div>\n\n        {showAddForm && (\n          <div className=\"product-form-modal\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h2>{editingProduct ? 'Edit Product' : 'Add New Product'}</h2>\n                <button onClick={resetForm} className=\"close-btn\">\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit} className=\"product-form\">\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label htmlFor=\"name\">Product Name *</label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label htmlFor=\"price\">Price *</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"description\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    rows=\"3\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"imageUrl\">Image URL</label>\n                  <input\n                    type=\"url\"\n                    id=\"imageUrl\"\n                    name=\"imageUrl\"\n                    value={formData.imageUrl}\n                    onChange={handleInputChange}\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"tags\">Tags (comma-separated)</label>\n                  <input\n                    type=\"text\"\n                    id=\"tags\"\n                    name=\"tags\"\n                    value={formData.tags}\n                    onChange={handleInputChange}\n                    placeholder=\"eco-friendly, sustainable, organic\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"checkbox-label\">\n                    <input\n                      type=\"checkbox\"\n                      name=\"isEcoFriendly\"\n                      checked={formData.isEcoFriendly}\n                      onChange={handleInputChange}\n                    />\n                    <span className=\"checkmark\"></span>\n                    Eco-Friendly Product\n                  </label>\n                </div>\n\n                <div className=\"form-actions\">\n                  <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"save-btn\">\n                    <i className=\"fas fa-save\"></i>\n                    {editingProduct ? 'Update Product' : 'Add Product'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        <div className=\"products-table\">\n          <div className=\"table-header\">\n            <h3>All Products ({products.length})</h3>\n          </div>\n\n          <div className=\"table-container\">\n            <table>\n              <thead>\n                <tr>\n                  <th>Image</th>\n                  <th>Name</th>\n                  <th>Price</th>\n                  <th>Eco-Friendly</th>\n                  <th>Tags</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map(product => (\n                  <tr key={product._id}>\n                    <td>\n                      <img \n                        src={product.imageUrl} \n                        alt={product.name}\n                        className=\"product-thumbnail\"\n                      />\n                    </td>\n                    <td>\n                      <div className=\"product-name\">{product.name}</div>\n                      <div className=\"product-description\">\n                        {product.description?.substring(0, 50)}...\n                      </div>\n                    </td>\n                    <td className=\"price-cell\">${product.price}</td>\n                    <td>\n                      {product.isEcoFriendly ? (\n                        <span className=\"eco-badge\">\n                          <i className=\"fas fa-leaf\"></i>\n                          Yes\n                        </span>\n                      ) : (\n                        <span className=\"not-eco\">No</span>\n                      )}\n                    </td>\n                    <td>\n                      <div className=\"tags-cell\">\n                        {product.tags?.slice(0, 2).map((tag, index) => (\n                          <span key={index} className=\"tag\">{tag}</span>\n                        ))}\n                        {product.tags?.length > 2 && (\n                          <span className=\"tag-more\">+{product.tags.length - 2}</span>\n                        )}\n                      </div>\n                    </td>\n                    <td>\n                      <div className=\"action-buttons\">\n                        <button \n                          onClick={() => handleEdit(product)}\n                          className=\"edit-btn\"\n                          title=\"Edit product\"\n                        >\n                          <i className=\"fas fa-edit\"></i>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(product._id)}\n                          className=\"delete-btn\"\n                          title=\"Delete product\"\n                        >\n                          <i className=\"fas fa-trash\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .admin-container {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .admin-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 2px solid var(--border-color);\n        }\n\n        .admin-header h1 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0;\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .add-product-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .add-product-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .product-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .product-form {\n          padding: 2rem;\n        }\n\n        .form-row {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 1rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input,\n        .form-group textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus,\n        .form-group textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .checkbox-label {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          cursor: pointer;\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n          margin-top: 2rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .products-table {\n          background: white;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n        }\n\n        .table-header {\n          padding: 1.5rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .table-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .table-container {\n          overflow-x: auto;\n        }\n\n        table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        th, td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        th {\n          background: var(--bg-secondary);\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .product-thumbnail {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .product-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          margin-bottom: 0.25rem;\n        }\n\n        .product-description {\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .price-cell {\n          font-weight: 600;\n          color: var(--primary-color);\n          font-size: 1.125rem;\n        }\n\n        .eco-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          display: inline-flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .not-eco {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .tags-cell {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.25rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .tag-more {\n          background: var(--text-secondary);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .edit-btn, .delete-btn {\n          background: none;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .edit-btn {\n          color: var(--primary-color);\n        }\n\n        .edit-btn:hover {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .delete-btn {\n          color: #ef4444;\n        }\n\n        .delete-btn:hover {\n          background: #ef4444;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .admin-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .form-row {\n            grid-template-columns: 1fr;\n          }\n\n          .table-container {\n            font-size: 0.875rem;\n          }\n\n          th, td {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default AdminPage;\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,QAAS,CAAAC,SAASA,CAAA,CAAG,CACnB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGX,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACkB,cAAc,CAAEC,iBAAiB,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACoB,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAC,CACvCsB,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,aAAa,CAAE,KACjB,CAAC,CAAC,CAEF1B,SAAS,CAAC,IAAM,CACd2B,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFf,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAgB,QAAQ,CAAG,KAAM,CAAA3B,KAAK,CAAC4B,GAAG,CAAC,oCAAoC,CAAC,CACtEnB,WAAW,CAACkB,QAAQ,CAACE,IAAI,CAAC,CAC5B,CAAE,MAAOC,GAAG,CAAE,CACZjB,QAAQ,CAAC,yBAAyB,CAAC,CACrC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoB,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEZ,IAAI,CAAEa,KAAK,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGH,CAAC,CAACI,MAAM,CAC/CjB,WAAW,CAACkB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACjB,IAAI,EAAGc,IAAI,GAAK,UAAU,CAAGC,OAAO,CAAGF,KAAK,EAC7C,CAAC,CACL,CAAC,CAED,KAAM,CAAAM,YAAY,CAAG,KAAO,CAAAP,CAAC,EAAK,CAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClB,GAAI,CACF,KAAM,CAAAC,WAAW,CAAAH,aAAA,CAAAA,aAAA,IACZpB,QAAQ,MACXK,KAAK,CAAEmB,UAAU,CAACxB,QAAQ,CAACK,KAAK,CAAC,CACjCC,IAAI,CAAEN,QAAQ,CAACM,IAAI,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACF,GAAG,EAAIA,GAAG,CAAC,EACzE,CAED,GAAI7B,cAAc,CAAE,CAClB;AACA,KAAM,CAAAgC,eAAe,CAAGxC,QAAQ,CAACoC,GAAG,CAACK,CAAC,EACpCA,CAAC,CAACC,GAAG,GAAKlC,cAAc,CAACkC,GAAG,CAAAZ,aAAA,CAAAA,aAAA,IAAQW,CAAC,EAAKR,WAAW,EAAKQ,CAC5D,CAAC,CACDxC,WAAW,CAACuC,eAAe,CAAC,CAC9B,CAAC,IAAM,CACL;AACA,KAAM,CAAAG,UAAU,CAAAb,aAAA,EACdY,GAAG,CAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EACvBb,WAAW,CACf,CACDhC,WAAW,CAAC,CAAC0C,UAAU,CAAE,GAAG3C,QAAQ,CAAC,CAAC,CACxC,CAEA+C,SAAS,CAAC,CAAC,CACb,CAAE,MAAOzB,GAAG,CAAE,CACZjB,QAAQ,CAAC,wBAAwB,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAA2C,UAAU,CAAIC,OAAO,EAAK,KAAAC,aAAA,CAC9BzC,iBAAiB,CAACwC,OAAO,CAAC,CAC1BtC,WAAW,CAAC,CACVC,IAAI,CAAEqC,OAAO,CAACrC,IAAI,CAClBC,WAAW,CAAEoC,OAAO,CAACpC,WAAW,CAChCC,QAAQ,CAAEmC,OAAO,CAACnC,QAAQ,CAC1BC,KAAK,CAAEkC,OAAO,CAAClC,KAAK,CAAC+B,QAAQ,CAAC,CAAC,CAC/B9B,IAAI,CAAE,EAAAkC,aAAA,CAAAD,OAAO,CAACjC,IAAI,UAAAkC,aAAA,iBAAZA,aAAA,CAAcC,IAAI,CAAC,IAAI,CAAC,GAAI,EAAE,CACpClC,aAAa,CAAEgC,OAAO,CAAChC,aACzB,CAAC,CAAC,CACFV,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA6C,YAAY,CAAIC,SAAS,EAAK,CAClC,GAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,CAAE,CACnEtD,WAAW,CAACD,QAAQ,CAACuC,MAAM,CAACE,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKW,SAAS,CAAC,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAAN,SAAS,CAAGA,CAAA,GAAM,CACtBpC,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,aAAa,CAAE,KACjB,CAAC,CAAC,CACFR,iBAAiB,CAAC,IAAI,CAAC,CACvBF,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,GAAIL,OAAO,CAAE,CACX,mBAAON,IAAA,CAACH,cAAc,EAAC+D,IAAI,CAAC,OAAO,CAACC,OAAO,CAAC,wBAAwB,CAACC,UAAU,MAAE,CAAC,CACpF,CAEA,GAAItD,KAAK,CAAE,CACT,mBAAOR,IAAA,CAACF,YAAY,EAACiE,KAAK,CAAC,mBAAmB,CAACF,OAAO,CAAErD,KAAM,CAACwD,OAAO,CAAE1C,aAAc,CAAE,CAAC,CAC3F,CAEA,mBACEpB,KAAA,SAAA+D,QAAA,eACE/D,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/D,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B/D,KAAA,OAAA+D,QAAA,eACEjE,IAAA,MAAGkE,SAAS,CAAC,YAAY,CAAI,CAAC,qBAEhC,EAAI,CAAC,cACLhE,KAAA,WACEiE,OAAO,CAAEA,CAAA,GAAMxD,cAAc,CAAC,IAAI,CAAE,CACpCuD,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAE3BjE,IAAA,MAAGkE,SAAS,CAAC,aAAa,CAAI,CAAC,kBAEjC,EAAQ,CAAC,EACN,CAAC,CAELxD,WAAW,eACVV,IAAA,QAAKkE,SAAS,CAAC,oBAAoB,CAAAD,QAAA,cACjC/D,KAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B/D,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjE,IAAA,OAAAiE,QAAA,CAAKrD,cAAc,CAAG,cAAc,CAAG,iBAAiB,CAAK,CAAC,cAC9DZ,IAAA,WAAQmE,OAAO,CAAEhB,SAAU,CAACe,SAAS,CAAC,WAAW,CAAAD,QAAA,cAC/CjE,IAAA,MAAGkE,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,EACN,CAAC,cAENhE,KAAA,SAAMkE,QAAQ,CAAEjC,YAAa,CAAC+B,SAAS,CAAC,cAAc,CAAAD,QAAA,eACpD/D,KAAA,QAAKgE,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB/D,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBjE,IAAA,UAAOqE,OAAO,CAAC,MAAM,CAAAJ,QAAA,CAAC,gBAAc,CAAO,CAAC,cAC5CjE,IAAA,UACE8B,IAAI,CAAC,MAAM,CACXwC,EAAE,CAAC,MAAM,CACTtD,IAAI,CAAC,MAAM,CACXa,KAAK,CAAEf,QAAQ,CAACE,IAAK,CACrBuD,QAAQ,CAAE5C,iBAAkB,CAC5B6C,QAAQ,MACT,CAAC,EACC,CAAC,cACNtE,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBjE,IAAA,UAAOqE,OAAO,CAAC,OAAO,CAAAJ,QAAA,CAAC,SAAO,CAAO,CAAC,cACtCjE,IAAA,UACE8B,IAAI,CAAC,QAAQ,CACbwC,EAAE,CAAC,OAAO,CACVtD,IAAI,CAAC,OAAO,CACZa,KAAK,CAAEf,QAAQ,CAACK,KAAM,CACtBoD,QAAQ,CAAE5C,iBAAkB,CAC5B8C,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPF,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,cAENtE,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBjE,IAAA,UAAOqE,OAAO,CAAC,aAAa,CAAAJ,QAAA,CAAC,aAAW,CAAO,CAAC,cAChDjE,IAAA,aACEsE,EAAE,CAAC,aAAa,CAChBtD,IAAI,CAAC,aAAa,CAClBa,KAAK,CAAEf,QAAQ,CAACG,WAAY,CAC5BsD,QAAQ,CAAE5C,iBAAkB,CAC5BgD,IAAI,CAAC,GAAG,CACT,CAAC,EACC,CAAC,cAENzE,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBjE,IAAA,UAAOqE,OAAO,CAAC,UAAU,CAAAJ,QAAA,CAAC,WAAS,CAAO,CAAC,cAC3CjE,IAAA,UACE8B,IAAI,CAAC,KAAK,CACVwC,EAAE,CAAC,UAAU,CACbtD,IAAI,CAAC,UAAU,CACfa,KAAK,CAAEf,QAAQ,CAACI,QAAS,CACzBqD,QAAQ,CAAE5C,iBAAkB,CAC7B,CAAC,EACC,CAAC,cAENzB,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBjE,IAAA,UAAOqE,OAAO,CAAC,MAAM,CAAAJ,QAAA,CAAC,wBAAsB,CAAO,CAAC,cACpDjE,IAAA,UACE8B,IAAI,CAAC,MAAM,CACXwC,EAAE,CAAC,MAAM,CACTtD,IAAI,CAAC,MAAM,CACXa,KAAK,CAAEf,QAAQ,CAACM,IAAK,CACrBmD,QAAQ,CAAE5C,iBAAkB,CAC5BiD,WAAW,CAAC,oCAAoC,CACjD,CAAC,EACC,CAAC,cAEN5E,IAAA,QAAKkE,SAAS,CAAC,YAAY,CAAAD,QAAA,cACzB/D,KAAA,UAAOgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC/BjE,IAAA,UACE8B,IAAI,CAAC,UAAU,CACfd,IAAI,CAAC,eAAe,CACpBe,OAAO,CAAEjB,QAAQ,CAACO,aAAc,CAChCkD,QAAQ,CAAE5C,iBAAkB,CAC7B,CAAC,cACF3B,IAAA,SAAMkE,SAAS,CAAC,WAAW,CAAO,CAAC,uBAErC,EAAO,CAAC,CACL,CAAC,cAENhE,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjE,IAAA,WAAQ8B,IAAI,CAAC,QAAQ,CAACqC,OAAO,CAAEhB,SAAU,CAACe,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,QAEjE,CAAQ,CAAC,cACT/D,KAAA,WAAQ4B,IAAI,CAAC,QAAQ,CAACoC,SAAS,CAAC,UAAU,CAAAD,QAAA,eACxCjE,IAAA,MAAGkE,SAAS,CAAC,aAAa,CAAI,CAAC,CAC9BtD,cAAc,CAAG,gBAAgB,CAAG,aAAa,EAC5C,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CACN,cAEDV,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BjE,IAAA,QAAKkE,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3B/D,KAAA,OAAA+D,QAAA,EAAI,gBAAc,CAAC7D,QAAQ,CAACyE,MAAM,CAAC,GAAC,EAAI,CAAC,CACtC,CAAC,cAEN7E,IAAA,QAAKkE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B/D,KAAA,UAAA+D,QAAA,eACEjE,IAAA,UAAAiE,QAAA,cACE/D,KAAA,OAAA+D,QAAA,eACEjE,IAAA,OAAAiE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdjE,IAAA,OAAAiE,QAAA,CAAI,MAAI,CAAI,CAAC,cACbjE,IAAA,OAAAiE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdjE,IAAA,OAAAiE,QAAA,CAAI,cAAY,CAAI,CAAC,cACrBjE,IAAA,OAAAiE,QAAA,CAAI,MAAI,CAAI,CAAC,cACbjE,IAAA,OAAAiE,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACRjE,IAAA,UAAAiE,QAAA,CACG7D,QAAQ,CAACoC,GAAG,CAACa,OAAO,OAAAyB,oBAAA,CAAAC,cAAA,CAAAC,cAAA,oBACnB9E,KAAA,OAAA+D,QAAA,eACEjE,IAAA,OAAAiE,QAAA,cACEjE,IAAA,QACEiF,GAAG,CAAE5B,OAAO,CAACnC,QAAS,CACtBgE,GAAG,CAAE7B,OAAO,CAACrC,IAAK,CAClBkD,SAAS,CAAC,mBAAmB,CAC9B,CAAC,CACA,CAAC,cACLhE,KAAA,OAAA+D,QAAA,eACEjE,IAAA,QAAKkE,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAEZ,OAAO,CAACrC,IAAI,CAAM,CAAC,cAClDd,KAAA,QAAKgE,SAAS,CAAC,qBAAqB,CAAAD,QAAA,GAAAa,oBAAA,CACjCzB,OAAO,CAACpC,WAAW,UAAA6D,oBAAA,iBAAnBA,oBAAA,CAAqBK,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,KACzC,EAAK,CAAC,EACJ,CAAC,cACLjF,KAAA,OAAIgE,SAAS,CAAC,YAAY,CAAAD,QAAA,EAAC,GAAC,CAACZ,OAAO,CAAClC,KAAK,EAAK,CAAC,cAChDnB,IAAA,OAAAiE,QAAA,CACGZ,OAAO,CAAChC,aAAa,cACpBnB,KAAA,SAAMgE,SAAS,CAAC,WAAW,CAAAD,QAAA,eACzBjE,IAAA,MAAGkE,SAAS,CAAC,aAAa,CAAI,CAAC,MAEjC,EAAM,CAAC,cAEPlE,IAAA,SAAMkE,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAC,IAAE,CAAM,CACnC,CACC,CAAC,cACLjE,IAAA,OAAAiE,QAAA,cACE/D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAD,QAAA,GAAAc,cAAA,CACvB1B,OAAO,CAACjC,IAAI,UAAA2D,cAAA,iBAAZA,cAAA,CAAcK,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC5C,GAAG,CAAC,CAACC,GAAG,CAAE4C,KAAK,gBACxCrF,IAAA,SAAkBkE,SAAS,CAAC,KAAK,CAAAD,QAAA,CAAExB,GAAG,EAA3B4C,KAAkC,CAC9C,CAAC,CACD,EAAAL,cAAA,CAAA3B,OAAO,CAACjC,IAAI,UAAA4D,cAAA,iBAAZA,cAAA,CAAcH,MAAM,EAAG,CAAC,eACvB3E,KAAA,SAAMgE,SAAS,CAAC,UAAU,CAAAD,QAAA,EAAC,GAAC,CAACZ,OAAO,CAACjC,IAAI,CAACyD,MAAM,CAAG,CAAC,EAAO,CAC5D,EACE,CAAC,CACJ,CAAC,cACL7E,IAAA,OAAAiE,QAAA,cACE/D,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BjE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAACC,OAAO,CAAE,CACnCa,SAAS,CAAC,UAAU,CACpBH,KAAK,CAAC,cAAc,CAAAE,QAAA,cAEpBjE,IAAA,MAAGkE,SAAS,CAAC,aAAa,CAAI,CAAC,CACzB,CAAC,cACTlE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMX,YAAY,CAACH,OAAO,CAACP,GAAG,CAAE,CACzCoB,SAAS,CAAC,YAAY,CACtBH,KAAK,CAAC,gBAAgB,CAAAE,QAAA,cAEtBjE,IAAA,MAAGkE,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,EACN,CAAC,CACJ,CAAC,GApDEb,OAAO,CAACP,GAqDb,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,EACH,CAAC,cAEN9C,IAAA,UAAOD,GAAG,MAAAkE,QAAA,g4PA4UD,CAAC,EACN,CAAC,CAEX,CAEA,cAAe,CAAA9D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}