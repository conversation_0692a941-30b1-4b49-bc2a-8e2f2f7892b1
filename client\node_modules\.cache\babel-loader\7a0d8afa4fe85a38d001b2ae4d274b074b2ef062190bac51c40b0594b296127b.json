{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\AuthModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from 'react';\nimport axios from 'axios';\nimport { AuthContext } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AuthModal({\n  isOpen,\n  onClose,\n  initialMode = 'login'\n}) {\n  _s();\n  const [mode, setMode] = useState(initialMode);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    setUser,\n    setToken\n  } = useContext(AuthContext);\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      if (mode === 'register') {\n        if (formData.password !== formData.confirmPassword) {\n          setError('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n        await axios.post('http://localhost:5000/api/auth/register', {\n          name: formData.name,\n          email: formData.email,\n          password: formData.password\n        });\n\n        // Auto-login after registration\n        const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n        setUser(loginResponse.data.user);\n        setToken(loginResponse.data.token);\n        onClose();\n      } else {\n        const response = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n        setUser(response.data.user);\n        setToken(response.data.token);\n        onClose();\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'register' : 'login');\n    setError('');\n    setFormData({\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: onClose,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), mode === 'login' ? 'Welcome Back' : 'Create Account']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-times\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), mode === 'register' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"name\",\n            name: \"name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"Enter your full name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"Enter your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"Enter your password\",\n            minLength: \"6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), mode === 'register' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"confirmPassword\",\n            children: \"Confirm Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"confirmPassword\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"Confirm your password\",\n            minLength: \"6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"submit-btn\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-spinner fa-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), mode === 'login' ? 'Signing In...' : 'Creating Account...']\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), mode === 'login' ? 'Sign In' : 'Create Account']\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [mode === 'login' ? \"Don't have an account?\" : \"Already have an account?\", /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: switchMode,\n              className: \"switch-mode-btn\",\n              children: mode === 'login' ? 'Sign Up' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 400px;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .auth-form {\n          padding: 2rem;\n        }\n\n        .error-message {\n          background: #fef2f2;\n          color: #dc2626;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          margin-bottom: 1rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid #e5e7eb;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .submit-btn {\n          width: 100%;\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.875rem;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .submit-btn:hover:not(:disabled) {\n          background: var(--primary-dark);\n        }\n\n        .submit-btn:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .form-footer {\n          margin-top: 1.5rem;\n          text-align: center;\n        }\n\n        .form-footer p {\n          color: var(--text-secondary);\n          margin: 0;\n        }\n\n        .switch-mode-btn {\n          background: none;\n          border: none;\n          color: var(--primary-color);\n          cursor: pointer;\n          font-weight: 500;\n          margin-left: 0.5rem;\n          text-decoration: underline;\n        }\n\n        .switch-mode-btn:hover {\n          color: var(--primary-dark);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_s(AuthModal, \"WhrSbJmj/GYFgQQ53telXRpUs34=\");\n_c = AuthModal;\nexport default AuthModal;\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "axios", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthModal", "isOpen", "onClose", "initialMode", "_s", "mode", "setMode", "formData", "setFormData", "name", "email", "password", "confirmPassword", "loading", "setLoading", "error", "setError", "setUser", "setToken", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "post", "loginResponse", "data", "user", "token", "response", "err", "_err$response", "_err$response$data", "message", "switchMode", "className", "onClick", "children", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/AuthModal.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\nimport axios from 'axios';\nimport { AuthContext } from '../context/AuthContext';\n\nfunction AuthModal({ isOpen, onClose, initialMode = 'login' }) {\n  const [mode, setMode] = useState(initialMode);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const { setUser, setToken } = useContext(AuthContext);\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      if (mode === 'register') {\n        if (formData.password !== formData.confirmPassword) {\n          setError('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n\n        await axios.post('http://localhost:5000/api/auth/register', {\n          name: formData.name,\n          email: formData.email,\n          password: formData.password\n        });\n\n        // Auto-login after registration\n        const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n\n        setUser(loginResponse.data.user);\n        setToken(loginResponse.data.token);\n        onClose();\n      } else {\n        const response = await axios.post('http://localhost:5000/api/auth/login', {\n          email: formData.email,\n          password: formData.password\n        });\n\n        setUser(response.data.user);\n        setToken(response.data.token);\n        onClose();\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'register' : 'login');\n    setError('');\n    setFormData({\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>\n            <i className={`fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`}></i>\n            {mode === 'login' ? 'Welcome Back' : 'Create Account'}\n          </h2>\n          <button className=\"close-btn\" onClick={onClose}>\n            <i className=\"fas fa-times\"></i>\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          {error && (\n            <div className=\"error-message\">\n              <i className=\"fas fa-exclamation-circle\"></i>\n              {error}\n            </div>\n          )}\n\n          {mode === 'register' && (\n            <div className=\"form-group\">\n              <label htmlFor=\"name\">Full Name</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleInputChange}\n                required\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email Address</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n              placeholder=\"Enter your password\"\n              minLength=\"6\"\n            />\n          </div>\n\n          {mode === 'register' && (\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\">Confirm Password</label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required\n                placeholder=\"Confirm your password\"\n                minLength=\"6\"\n              />\n            </div>\n          )}\n\n          <button type=\"submit\" className=\"submit-btn\" disabled={loading}>\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin\"></i>\n                {mode === 'login' ? 'Signing In...' : 'Creating Account...'}\n              </>\n            ) : (\n              <>\n                <i className={`fas ${mode === 'login' ? 'fa-sign-in-alt' : 'fa-user-plus'}`}></i>\n                {mode === 'login' ? 'Sign In' : 'Create Account'}\n              </>\n            )}\n          </button>\n\n          <div className=\"form-footer\">\n            <p>\n              {mode === 'login' ? \"Don't have an account?\" : \"Already have an account?\"}\n              <button type=\"button\" onClick={switchMode} className=\"switch-mode-btn\">\n                {mode === 'login' ? 'Sign Up' : 'Sign In'}\n              </button>\n            </p>\n          </div>\n        </form>\n      </div>\n\n      <style jsx>{`\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 400px;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .auth-form {\n          padding: 2rem;\n        }\n\n        .error-message {\n          background: #fef2f2;\n          color: #dc2626;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          margin-bottom: 1rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid #e5e7eb;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .submit-btn {\n          width: 100%;\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.875rem;\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .submit-btn:hover:not(:disabled) {\n          background: var(--primary-dark);\n        }\n\n        .submit-btn:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .form-footer {\n          margin-top: 1.5rem;\n          text-align: center;\n        }\n\n        .form-footer p {\n          color: var(--text-secondary);\n          margin: 0;\n        }\n\n        .switch-mode-btn {\n          background: none;\n          border: none;\n          color: var(--primary-color);\n          cursor: pointer;\n          font-weight: 500;\n          margin-left: 0.5rem;\n          text-decoration: underline;\n        }\n\n        .switch-mode-btn:hover {\n          color: var(--primary-dark);\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default AuthModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,SAASC,SAASA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,WAAW,GAAG;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAC7D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAACW,WAAW,CAAC;EAC7C,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEyB,OAAO;IAAEC;EAAS,CAAC,GAAGzB,UAAU,CAACE,WAAW,CAAC;EAErD,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACZ,IAAI,GAAGW,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;IACFN,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAIX,IAAI,KAAK,UAAU,EAAE;QACvB,IAAIE,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;UAClDI,QAAQ,CAAC,wBAAwB,CAAC;UAClCF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMpB,KAAK,CAAC+B,IAAI,CAAC,yCAAyC,EAAE;UAC1DhB,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC,CAAC;;QAEF;QACA,MAAMe,aAAa,GAAG,MAAMhC,KAAK,CAAC+B,IAAI,CAAC,sCAAsC,EAAE;UAC7Ef,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC,CAAC;QAEFM,OAAO,CAACS,aAAa,CAACC,IAAI,CAACC,IAAI,CAAC;QAChCV,QAAQ,CAACQ,aAAa,CAACC,IAAI,CAACE,KAAK,CAAC;QAClC3B,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL,MAAM4B,QAAQ,GAAG,MAAMpC,KAAK,CAAC+B,IAAI,CAAC,sCAAsC,EAAE;UACxEf,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC,CAAC;QAEFM,OAAO,CAACa,QAAQ,CAACH,IAAI,CAACC,IAAI,CAAC;QAC3BV,QAAQ,CAACY,QAAQ,CAACH,IAAI,CAACE,KAAK,CAAC;QAC7B3B,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZjB,QAAQ,CAAC,EAAAgB,aAAA,GAAAD,GAAG,CAACD,QAAQ,cAAAE,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,mBAAmB,CAAC;IAC9D,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvB7B,OAAO,CAACD,IAAI,KAAK,OAAO,GAAG,UAAU,GAAG,OAAO,CAAC;IAChDW,QAAQ,CAAC,EAAE,CAAC;IACZR,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACX,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKuC,SAAS,EAAC,eAAe;IAACC,OAAO,EAAEnC,OAAQ;IAAAoC,QAAA,gBAC9CzC,OAAA;MAAKuC,SAAS,EAAC,eAAe;MAACC,OAAO,EAAGjB,CAAC,IAAKA,CAAC,CAACmB,eAAe,CAAC,CAAE;MAAAD,QAAA,gBACjEzC,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAE,QAAA,gBAC3BzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAGuC,SAAS,EAAE,OAAO/B,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,cAAc;UAAG;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChFtC,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,gBAAgB;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACL9C,OAAA;UAAQuC,SAAS,EAAC,WAAW;UAACC,OAAO,EAAEnC,OAAQ;UAAAoC,QAAA,eAC7CzC,OAAA;YAAGuC,SAAS,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9C,OAAA;QAAM+C,QAAQ,EAAErB,YAAa;QAACa,SAAS,EAAC,WAAW;QAAAE,QAAA,GAChDvB,KAAK,iBACJlB,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5BzC,OAAA;YAAGuC,SAAS,EAAC;UAA2B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC5C5B,KAAK;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtC,IAAI,KAAK,UAAU,iBAClBR,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACzBzC,OAAA;YAAOgD,OAAO,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvC9C,OAAA;YACEiD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,MAAM;YACTtC,IAAI,EAAC,MAAM;YACXa,KAAK,EAAEf,QAAQ,CAACE,IAAK;YACrBuC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,QAAQ;YACRC,WAAW,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED9C,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACzBzC,OAAA;YAAOgD,OAAO,EAAC,OAAO;YAAAP,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C9C,OAAA;YACEiD,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVtC,IAAI,EAAC,OAAO;YACZa,KAAK,EAAEf,QAAQ,CAACG,KAAM;YACtBsC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,QAAQ;YACRC,WAAW,EAAC;UAAkB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACzBzC,OAAA;YAAOgD,OAAO,EAAC,UAAU;YAAAP,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C9C,OAAA;YACEiD,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbtC,IAAI,EAAC,UAAU;YACfa,KAAK,EAAEf,QAAQ,CAACI,QAAS;YACzBqC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,QAAQ;YACRC,WAAW,EAAC,qBAAqB;YACjCC,SAAS,EAAC;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELtC,IAAI,KAAK,UAAU,iBAClBR,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACzBzC,OAAA;YAAOgD,OAAO,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD9C,OAAA;YACEiD,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,iBAAiB;YACpBtC,IAAI,EAAC,iBAAiB;YACtBa,KAAK,EAAEf,QAAQ,CAACK,eAAgB;YAChCoC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,QAAQ;YACRC,WAAW,EAAC,uBAAuB;YACnCC,SAAS,EAAC;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED9C,OAAA;UAAQiD,IAAI,EAAC,QAAQ;UAACV,SAAS,EAAC,YAAY;UAACgB,QAAQ,EAAEvC,OAAQ;UAAAyB,QAAA,EAC5DzB,OAAO,gBACNhB,OAAA,CAAAE,SAAA;YAAAuC,QAAA,gBACEzC,OAAA;cAAGuC,SAAS,EAAC;YAAwB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzCtC,IAAI,KAAK,OAAO,GAAG,eAAe,GAAG,qBAAqB;UAAA,eAC3D,CAAC,gBAEHR,OAAA,CAAAE,SAAA;YAAAuC,QAAA,gBACEzC,OAAA;cAAGuC,SAAS,EAAE,OAAO/B,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,cAAc;YAAG;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChFtC,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,gBAAgB;UAAA,eAChD;QACH;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAET9C,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAE,QAAA,eAC1BzC,OAAA;YAAAyC,QAAA,GACGjC,IAAI,KAAK,OAAO,GAAG,wBAAwB,GAAG,0BAA0B,eACzER,OAAA;cAAQiD,IAAI,EAAC,QAAQ;cAACT,OAAO,EAAEF,UAAW;cAACC,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EACnEjC,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;YAAS;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN9C,OAAA;MAAOwD,GAAG;MAAAf,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACvC,EAAA,CA5UQJ,SAAS;AAAAsD,EAAA,GAATtD,SAAS;AA8UlB,eAAeA,SAAS;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}