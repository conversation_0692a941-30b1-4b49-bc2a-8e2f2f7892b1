{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\ErrorMessage.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ErrorMessage({\n  title = 'Something went wrong',\n  message = 'An unexpected error occurred. Please try again.',\n  onRetry = null,\n  type = 'error' // 'error', 'warning', 'info'\n}) {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n        return 'fas fa-info-circle';\n      default:\n        return 'fas fa-exclamation-circle';\n    }\n  };\n  const getColorClass = () => {\n    switch (type) {\n      case 'warning':\n        return 'warning';\n      case 'info':\n        return 'info';\n      default:\n        return 'error';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `error-container ${getColorClass()}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: getIcon()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-text\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onRetry,\n      className: \"retry-button\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-redo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this), \"Try Again\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 2px solid;\n          background: white;\n          text-align: center;\n          max-width: 500px;\n          margin: 2rem auto;\n        }\n\n        .error-container.error {\n          border-color: #fecaca;\n          background: #fef2f2;\n        }\n\n        .error-container.warning {\n          border-color: #fed7aa;\n          background: #fffbeb;\n        }\n\n        .error-container.info {\n          border-color: #bfdbfe;\n          background: #eff6ff;\n        }\n\n        .error-content {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          margin-bottom: 1.5rem;\n        }\n\n        .error-icon {\n          font-size: 2.5rem;\n          flex-shrink: 0;\n        }\n\n        .error-container.error .error-icon {\n          color: #dc2626;\n        }\n\n        .error-container.warning .error-icon {\n          color: #d97706;\n        }\n\n        .error-container.info .error-icon {\n          color: #2563eb;\n        }\n\n        .error-text {\n          text-align: left;\n        }\n\n        .error-text h3 {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n        }\n\n        .error-container.error .error-text h3 {\n          color: #991b1b;\n        }\n\n        .error-container.warning .error-text h3 {\n          color: #92400e;\n        }\n\n        .error-container.info .error-text h3 {\n          color: #1e40af;\n        }\n\n        .error-text p {\n          margin: 0;\n          color: var(--text-secondary);\n          line-height: 1.5;\n        }\n\n        .retry-button {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .retry-button:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        @media (max-width: 768px) {\n          .error-content {\n            flex-direction: column;\n            text-align: center;\n          }\n\n          .error-text {\n            text-align: center;\n          }\n\n          .error-icon {\n            font-size: 3rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = ErrorMessage;\nexport default ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ErrorMessage", "title", "message", "onRetry", "type", "getIcon", "getColorClass", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ErrorMessage.jsx"], "sourcesContent": ["import React from 'react';\n\nfunction ErrorMessage({ \n  title = 'Something went wrong', \n  message = 'An unexpected error occurred. Please try again.', \n  onRetry = null,\n  type = 'error' // 'error', 'warning', 'info'\n}) {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n        return 'fas fa-info-circle';\n      default:\n        return 'fas fa-exclamation-circle';\n    }\n  };\n\n  const getColorClass = () => {\n    switch (type) {\n      case 'warning':\n        return 'warning';\n      case 'info':\n        return 'info';\n      default:\n        return 'error';\n    }\n  };\n\n  return (\n    <div className={`error-container ${getColorClass()}`}>\n      <div className=\"error-content\">\n        <div className=\"error-icon\">\n          <i className={getIcon()}></i>\n        </div>\n        <div className=\"error-text\">\n          <h3>{title}</h3>\n          <p>{message}</p>\n        </div>\n      </div>\n      {onRetry && (\n        <button onClick={onRetry} className=\"retry-button\">\n          <i className=\"fas fa-redo\"></i>\n          Try Again\n        </button>\n      )}\n\n      <style jsx>{`\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 2px solid;\n          background: white;\n          text-align: center;\n          max-width: 500px;\n          margin: 2rem auto;\n        }\n\n        .error-container.error {\n          border-color: #fecaca;\n          background: #fef2f2;\n        }\n\n        .error-container.warning {\n          border-color: #fed7aa;\n          background: #fffbeb;\n        }\n\n        .error-container.info {\n          border-color: #bfdbfe;\n          background: #eff6ff;\n        }\n\n        .error-content {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          margin-bottom: 1.5rem;\n        }\n\n        .error-icon {\n          font-size: 2.5rem;\n          flex-shrink: 0;\n        }\n\n        .error-container.error .error-icon {\n          color: #dc2626;\n        }\n\n        .error-container.warning .error-icon {\n          color: #d97706;\n        }\n\n        .error-container.info .error-icon {\n          color: #2563eb;\n        }\n\n        .error-text {\n          text-align: left;\n        }\n\n        .error-text h3 {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n        }\n\n        .error-container.error .error-text h3 {\n          color: #991b1b;\n        }\n\n        .error-container.warning .error-text h3 {\n          color: #92400e;\n        }\n\n        .error-container.info .error-text h3 {\n          color: #1e40af;\n        }\n\n        .error-text p {\n          margin: 0;\n          color: var(--text-secondary);\n          line-height: 1.5;\n        }\n\n        .retry-button {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .retry-button:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        @media (max-width: 768px) {\n          .error-content {\n            flex-direction: column;\n            text-align: center;\n          }\n\n          .error-text {\n            text-align: center;\n          }\n\n          .error-icon {\n            font-size: 3rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ErrorMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,YAAYA,CAAC;EACpBC,KAAK,GAAG,sBAAsB;EAC9BC,OAAO,GAAG,iDAAiD;EAC3DC,OAAO,GAAG,IAAI;EACdC,IAAI,GAAG,OAAO,CAAC;AACjB,CAAC,EAAE;EACD,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQD,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC,KAAK,MAAM;QACT,OAAO,oBAAoB;MAC7B;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQF,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAO,OAAO;IAClB;EACF,CAAC;EAED,oBACEL,OAAA;IAAKQ,SAAS,EAAE,mBAAmBD,aAAa,CAAC,CAAC,EAAG;IAAAE,QAAA,gBACnDT,OAAA;MAAKQ,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BT,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBT,OAAA;UAAGQ,SAAS,EAAEF,OAAO,CAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBT,OAAA;UAAAS,QAAA,EAAKP;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChBb,OAAA;UAAAS,QAAA,EAAIN;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLT,OAAO,iBACNJ,OAAA;MAAQc,OAAO,EAAEV,OAAQ;MAACI,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAChDT,OAAA;QAAGQ,SAAS,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,aAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT,eAEDb,OAAA;MAAOe,GAAG;MAAAN,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACG,EAAA,GApKQf,YAAY;AAsKrB,eAAeA,YAAY;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}