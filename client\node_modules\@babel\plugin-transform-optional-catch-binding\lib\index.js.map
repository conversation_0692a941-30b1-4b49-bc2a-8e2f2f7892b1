{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "CatchClause", "path", "node", "param", "uid", "scope", "generateUidIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "get", "replaceWith"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-optional-catch-binding\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"optionalCatchBinding\"),\n\n    visitor: {\n      CatchClause(path) {\n        if (!path.node.param) {\n          const uid = path.scope.generateUidIdentifier(\"unused\");\n          const paramPath = path.get(\"param\");\n          paramPath.replaceWith(uid);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,kCAAkC;IACxCC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;IAE9DC,OAAO,EAAE;MACPC,WAAWA,CAACC,IAAI,EAAE;QAChB,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,KAAK,EAAE;UACpB,MAAMC,GAAG,GAAGH,IAAI,CAACI,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;UACtD,MAAMC,SAAS,GAAGN,IAAI,CAACO,GAAG,CAAC,OAAO,CAAC;UACnCD,SAAS,CAACE,WAAW,CAACL,GAAG,CAAC;QAC5B;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}