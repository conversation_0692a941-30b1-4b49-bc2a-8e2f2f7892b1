{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\ProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductPage() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const {\n    addToCart\n  } = useContext(CartContext);\n  const {\n    wishlist,\n    addToWishlist,\n    removeFromWishlist\n  } = useContext(WishlistContext) || {\n    wishlist: [],\n    addToWishlist: () => {},\n    removeFromWishlist: () => {}\n  };\n  const isInWishlist = wishlist.some(item => item._id === (product === null || product === void 0 ? void 0 : product._id));\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    fetchProduct();\n  }, [id]);\n  const handleAddToCart = () => {\n    for (let i = 0; i < quantity; i++) {\n      addToCart(product);\n    }\n  };\n  const handleWishlistToggle = () => {\n    if (isInWishlist) {\n      removeFromWishlist(product._id);\n    } else {\n      addToWishlist(product);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"main\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-spinner fa-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading product details...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"main\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Product not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The product you're looking for doesn't exist or has been removed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-gallery\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"main-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: product.imageUrl || '/placeholder-image.jpg',\n            alt: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `wishlist-btn ${isInWishlist ? 'active' : ''}`,\n            onClick: handleWishlistToggle,\n            title: isInWishlist ? 'Remove from wishlist' : 'Add to wishlist',\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: isInWishlist ? 'fas fa-heart' : 'far fa-heart'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-rating\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stars\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-star-half-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"rating-text\",\n              children: \"(4.5) \\u2022 127 reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"price-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-price\",\n            children: [\"$\", product.price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-note\",\n            children: \"Free shipping on orders over $50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), product.isEcoFriendly && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"eco-badge\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-leaf\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Eco-Friendly Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"eco-description\",\n            children: \"This product is made with sustainable materials and eco-friendly processes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), product.tags && product.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-tags\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tags-list\",\n            children: product.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tag\",\n              children: tag\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"purchase-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quantity-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantity\",\n              children: \"Quantity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quantity-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                className: \"quantity-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-minus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"quantity\",\n                value: quantity,\n                onChange: e => setQuantity(Math.max(1, parseInt(e.target.value) || 1)),\n                min: \"1\",\n                className: \"quantity-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setQuantity(quantity + 1),\n                className: \"quantity-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-plus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddToCart,\n              className: \"add-to-cart-btn\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-shopping-cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), \"Add to Cart\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"buy-now-btn\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bolt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), \"Buy Now\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-truck\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Free Shipping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"On orders over $50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-undo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"30-Day Returns\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Easy returns & exchanges\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shield-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"2-Year Warranty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Full manufacturer warranty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductReviews, {\n      productId: product._id\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .product-page {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 4rem;\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .product-gallery {\n          position: sticky;\n          top: 2rem;\n          height: fit-content;\n        }\n\n        .main-image {\n          position: relative;\n          border-radius: 1rem;\n          overflow: hidden;\n          box-shadow: var(--shadow-lg);\n        }\n\n        .main-image img {\n          width: 100%;\n          height: 500px;\n          object-fit: cover;\n        }\n\n        .wishlist-btn {\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          background: rgba(255, 255, 255, 0.9);\n          border: none;\n          border-radius: 50%;\n          width: 50px;\n          height: 50px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          font-size: 1.25rem;\n          transition: all 0.2s;\n          backdrop-filter: blur(4px);\n        }\n\n        .wishlist-btn:hover {\n          background: rgba(255, 255, 255, 1);\n          transform: scale(1.1);\n        }\n\n        .wishlist-btn.active {\n          color: #ef4444;\n        }\n\n        .product-info {\n          display: flex;\n          flex-direction: column;\n          gap: 2rem;\n        }\n\n        .product-header h1 {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n          line-height: 1.2;\n        }\n\n        .product-rating {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .stars {\n          display: flex;\n          gap: 0.25rem;\n          color: #fbbf24;\n          font-size: 1.125rem;\n        }\n\n        .rating-text {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .price-section {\n          padding: 1.5rem 0;\n          border-top: 1px solid var(--border-color);\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .current-price {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          margin-bottom: 0.5rem;\n        }\n\n        .price-note {\n          color: var(--secondary-color);\n          font-weight: 500;\n        }\n\n        .eco-badge {\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\n          color: white;\n          padding: 1.5rem;\n          border-radius: 1rem;\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .eco-badge > span {\n          font-weight: 600;\n          font-size: 1.125rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .eco-description {\n          font-size: 0.875rem;\n          opacity: 0.9;\n        }\n\n        .product-description h3,\n        .product-tags h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .product-description p {\n          color: var(--text-secondary);\n          line-height: 1.7;\n          margin: 0;\n        }\n\n        .tags-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.75rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          font-size: 0.875rem;\n          font-weight: 500;\n          border: 1px solid var(--border-color);\n        }\n\n        .purchase-section {\n          background: var(--bg-secondary);\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .quantity-selector {\n          margin-bottom: 2rem;\n        }\n\n        .quantity-selector label {\n          display: block;\n          margin-bottom: 0.75rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .quantity-controls {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .quantity-btn {\n          background: white;\n          border: 2px solid var(--border-color);\n          width: 40px;\n          height: 40px;\n          border-radius: 0.5rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .quantity-btn:hover {\n          border-color: var(--primary-color);\n          color: var(--primary-color);\n        }\n\n        .quantity-input {\n          width: 80px;\n          height: 40px;\n          text-align: center;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n        }\n\n        .quantity-input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .add-to-cart-btn,\n        .buy-now-btn {\n          flex: 1;\n          padding: 1rem 2rem;\n          border-radius: 0.75rem;\n          font-weight: 600;\n          font-size: 1rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .add-to-cart-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n        }\n\n        .add-to-cart-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .buy-now-btn {\n          background: var(--accent-color);\n          color: white;\n          border: none;\n        }\n\n        .buy-now-btn:hover {\n          background: #d97706;\n          transform: translateY(-1px);\n        }\n\n        .product-features {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .feature {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          background: white;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 1.5rem;\n          color: var(--primary-color);\n          width: 24px;\n          text-align: center;\n        }\n\n        .feature div {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .feature strong {\n          color: var(--text-primary);\n          font-weight: 600;\n        }\n\n        .feature span {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .loading-container,\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          min-height: 60vh;\n          text-align: center;\n          gap: 1rem;\n        }\n\n        .loading-spinner,\n        .error-icon {\n          font-size: 3rem;\n          color: var(--primary-color);\n        }\n\n        .error-icon {\n          color: #ef4444;\n        }\n\n        @media (max-width: 768px) {\n          .product-page {\n            grid-template-columns: 1fr;\n            gap: 2rem;\n            padding: 1rem;\n          }\n\n          .product-header h1 {\n            font-size: 2rem;\n          }\n\n          .current-price {\n            font-size: 2rem;\n          }\n\n          .action-buttons {\n            flex-direction: column;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductPage, \"RhfkAgY+CQXW7+QJvPGPeDzSrxM=\", false, function () {\n  return [useParams];\n});\n_c = ProductPage;\nexport default ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useParams", "CartContext", "WishlistContext", "ProductReviews", "axios", "jsxDEV", "_jsxDEV", "ProductPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "quantity", "setQuantity", "addToCart", "wishlist", "addToWishlist", "removeFromWishlist", "isInWishlist", "some", "item", "_id", "fetchProduct", "response", "get", "data", "error", "console", "handleAddToCart", "i", "handleWishlistToggle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "imageUrl", "alt", "name", "onClick", "title", "price", "isEcoFriendly", "description", "tags", "length", "map", "tag", "index", "htmlFor", "Math", "max", "type", "value", "onChange", "e", "parseInt", "target", "min", "productId", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\n\nfunction ProductPage() {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const { addToCart } = useContext(CartContext);\n  const { wishlist, addToWishlist, removeFromWishlist } = useContext(WishlistContext) || { wishlist: [], addToWishlist: () => {}, removeFromWishlist: () => {} };\n\n  const isInWishlist = wishlist.some(item => item._id === product?._id);\n\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    \n    fetchProduct();\n  }, [id]);\n\n  const handleAddToCart = () => {\n    for (let i = 0; i < quantity; i++) {\n      addToCart(product);\n    }\n  };\n\n  const handleWishlistToggle = () => {\n    if (isInWishlist) {\n      removeFromWishlist(product._id);\n    } else {\n      addToWishlist(product);\n    }\n  };\n\n  if (loading) {\n    return (\n      <main>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\">\n            <i className=\"fas fa-spinner fa-spin\"></i>\n          </div>\n          <p>Loading product details...</p>\n        </div>\n      </main>\n    );\n  }\n\n  if (!product) {\n    return (\n      <main>\n        <div className=\"error-container\">\n          <div className=\"error-icon\">\n            <i className=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <h2>Product not found</h2>\n          <p>The product you're looking for doesn't exist or has been removed.</p>\n        </div>\n      </main>\n    );\n  }\n\n  return (\n    <main>\n      <div className=\"product-page\">\n        <div className=\"product-gallery\">\n          <div className=\"main-image\">\n            <img\n              src={product.imageUrl || '/placeholder-image.jpg'}\n              alt={product.name}\n            />\n            <button\n              className={`wishlist-btn ${isInWishlist ? 'active' : ''}`}\n              onClick={handleWishlistToggle}\n              title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}\n            >\n              <i className={isInWishlist ? 'fas fa-heart' : 'far fa-heart'}></i>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"product-info\">\n          <div className=\"product-header\">\n            <h1>{product.name}</h1>\n            <div className=\"product-rating\">\n              <div className=\"stars\">\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star-half-alt\"></i>\n              </div>\n              <span className=\"rating-text\">(4.5) • 127 reviews</span>\n            </div>\n          </div>\n\n          <div className=\"price-section\">\n            <div className=\"current-price\">${product.price}</div>\n            <div className=\"price-note\">Free shipping on orders over $50</div>\n          </div>\n\n          {product.isEcoFriendly && (\n            <div className=\"eco-badge\">\n              <i className=\"fas fa-leaf\"></i>\n              <span>Eco-Friendly Product</span>\n              <div className=\"eco-description\">\n                This product is made with sustainable materials and eco-friendly processes.\n              </div>\n            </div>\n          )}\n\n          <div className=\"product-description\">\n            <h3>Description</h3>\n            <p>{product.description}</p>\n          </div>\n\n          {product.tags && product.tags.length > 0 && (\n            <div className=\"product-tags\">\n              <h3>Features</h3>\n              <div className=\"tags-list\">\n                {product.tags.map((tag, index) => (\n                  <span key={index} className=\"tag\">\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"purchase-section\">\n            <div className=\"quantity-selector\">\n              <label htmlFor=\"quantity\">Quantity:</label>\n              <div className=\"quantity-controls\">\n                <button\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  className=\"quantity-btn\"\n                >\n                  <i className=\"fas fa-minus\"></i>\n                </button>\n                <input\n                  type=\"number\"\n                  id=\"quantity\"\n                  value={quantity}\n                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}\n                  min=\"1\"\n                  className=\"quantity-input\"\n                />\n                <button\n                  onClick={() => setQuantity(quantity + 1)}\n                  className=\"quantity-btn\"\n                >\n                  <i className=\"fas fa-plus\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div className=\"action-buttons\">\n              <button onClick={handleAddToCart} className=\"add-to-cart-btn\">\n                <i className=\"fas fa-shopping-cart\"></i>\n                Add to Cart\n              </button>\n              <button className=\"buy-now-btn\">\n                <i className=\"fas fa-bolt\"></i>\n                Buy Now\n              </button>\n            </div>\n          </div>\n\n          <div className=\"product-features\">\n            <div className=\"feature\">\n              <i className=\"fas fa-truck\"></i>\n              <div>\n                <strong>Free Shipping</strong>\n                <span>On orders over $50</span>\n              </div>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-undo\"></i>\n              <div>\n                <strong>30-Day Returns</strong>\n                <span>Easy returns & exchanges</span>\n              </div>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-shield-alt\"></i>\n              <div>\n                <strong>2-Year Warranty</strong>\n                <span>Full manufacturer warranty</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <ProductReviews productId={product._id} />\n\n      <style jsx>{`\n        .product-page {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 4rem;\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .product-gallery {\n          position: sticky;\n          top: 2rem;\n          height: fit-content;\n        }\n\n        .main-image {\n          position: relative;\n          border-radius: 1rem;\n          overflow: hidden;\n          box-shadow: var(--shadow-lg);\n        }\n\n        .main-image img {\n          width: 100%;\n          height: 500px;\n          object-fit: cover;\n        }\n\n        .wishlist-btn {\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          background: rgba(255, 255, 255, 0.9);\n          border: none;\n          border-radius: 50%;\n          width: 50px;\n          height: 50px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          font-size: 1.25rem;\n          transition: all 0.2s;\n          backdrop-filter: blur(4px);\n        }\n\n        .wishlist-btn:hover {\n          background: rgba(255, 255, 255, 1);\n          transform: scale(1.1);\n        }\n\n        .wishlist-btn.active {\n          color: #ef4444;\n        }\n\n        .product-info {\n          display: flex;\n          flex-direction: column;\n          gap: 2rem;\n        }\n\n        .product-header h1 {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n          line-height: 1.2;\n        }\n\n        .product-rating {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .stars {\n          display: flex;\n          gap: 0.25rem;\n          color: #fbbf24;\n          font-size: 1.125rem;\n        }\n\n        .rating-text {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .price-section {\n          padding: 1.5rem 0;\n          border-top: 1px solid var(--border-color);\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .current-price {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          margin-bottom: 0.5rem;\n        }\n\n        .price-note {\n          color: var(--secondary-color);\n          font-weight: 500;\n        }\n\n        .eco-badge {\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\n          color: white;\n          padding: 1.5rem;\n          border-radius: 1rem;\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .eco-badge > span {\n          font-weight: 600;\n          font-size: 1.125rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .eco-description {\n          font-size: 0.875rem;\n          opacity: 0.9;\n        }\n\n        .product-description h3,\n        .product-tags h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .product-description p {\n          color: var(--text-secondary);\n          line-height: 1.7;\n          margin: 0;\n        }\n\n        .tags-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.75rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          font-size: 0.875rem;\n          font-weight: 500;\n          border: 1px solid var(--border-color);\n        }\n\n        .purchase-section {\n          background: var(--bg-secondary);\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .quantity-selector {\n          margin-bottom: 2rem;\n        }\n\n        .quantity-selector label {\n          display: block;\n          margin-bottom: 0.75rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .quantity-controls {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .quantity-btn {\n          background: white;\n          border: 2px solid var(--border-color);\n          width: 40px;\n          height: 40px;\n          border-radius: 0.5rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .quantity-btn:hover {\n          border-color: var(--primary-color);\n          color: var(--primary-color);\n        }\n\n        .quantity-input {\n          width: 80px;\n          height: 40px;\n          text-align: center;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n        }\n\n        .quantity-input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .add-to-cart-btn,\n        .buy-now-btn {\n          flex: 1;\n          padding: 1rem 2rem;\n          border-radius: 0.75rem;\n          font-weight: 600;\n          font-size: 1rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .add-to-cart-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n        }\n\n        .add-to-cart-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .buy-now-btn {\n          background: var(--accent-color);\n          color: white;\n          border: none;\n        }\n\n        .buy-now-btn:hover {\n          background: #d97706;\n          transform: translateY(-1px);\n        }\n\n        .product-features {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .feature {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          background: white;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 1.5rem;\n          color: var(--primary-color);\n          width: 24px;\n          text-align: center;\n        }\n\n        .feature div {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .feature strong {\n          color: var(--text-primary);\n          font-weight: 600;\n        }\n\n        .feature span {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .loading-container,\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          min-height: 60vh;\n          text-align: center;\n          gap: 1rem;\n        }\n\n        .loading-spinner,\n        .error-icon {\n          font-size: 3rem;\n          color: var(--primary-color);\n        }\n\n        .error-icon {\n          color: #ef4444;\n        }\n\n        @media (max-width: 768px) {\n          .product-page {\n            grid-template-columns: 1fr;\n            gap: 2rem;\n            padding: 1rem;\n          }\n\n          .product-header h1 {\n            font-size: 2rem;\n          }\n\n          .current-price {\n            font-size: 2rem;\n          }\n\n          .action-buttons {\n            flex-direction: column;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default ProductPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM;IAAEmB;EAAU,CAAC,GAAGjB,UAAU,CAACE,WAAW,CAAC;EAC7C,MAAM;IAAEgB,QAAQ;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGpB,UAAU,CAACG,eAAe,CAAC,IAAI;IAAEe,QAAQ,EAAE,EAAE;IAAEC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAEC,kBAAkB,EAAEA,CAAA,KAAM,CAAC;EAAE,CAAC;EAE9J,MAAMC,YAAY,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,MAAKb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,GAAG,EAAC;EAErEzB,SAAS,CAAC,MAAM;IACd,eAAe0B,YAAYA,CAAA,EAAG;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,sCAAsCjB,EAAE,EAAE,CAAC;QAC5EE,UAAU,CAACc,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;IAEAW,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACf,EAAE,CAAC,CAAC;EAER,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,QAAQ,EAAEiB,CAAC,EAAE,EAAE;MACjCf,SAAS,CAACN,OAAO,CAAC;IACpB;EACF,CAAC;EAED,MAAMsB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIZ,YAAY,EAAE;MAChBD,kBAAkB,CAACT,OAAO,CAACa,GAAG,CAAC;IACjC,CAAC,MAAM;MACLL,aAAa,CAACR,OAAO,CAAC;IACxB;EACF,CAAC;EAED,IAAIE,OAAO,EAAE;IACX,oBACEN,OAAA;MAAA2B,QAAA,eACE3B,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChC3B,OAAA;UAAK4B,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9B3B,OAAA;YAAG4B,SAAS,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNhC,OAAA;UAAA2B,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,IAAI,CAAC5B,OAAO,EAAE;IACZ,oBACEJ,OAAA;MAAA2B,QAAA,eACE3B,OAAA;QAAK4B,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAC9B3B,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB3B,OAAA;YAAG4B,SAAS,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNhC,OAAA;UAAA2B,QAAA,EAAI;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BhC,OAAA;UAAA2B,QAAA,EAAG;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACEhC,OAAA;IAAA2B,QAAA,gBACE3B,OAAA;MAAK4B,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC3B3B,OAAA;QAAK4B,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC9B3B,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB3B,OAAA;YACEiC,GAAG,EAAE7B,OAAO,CAAC8B,QAAQ,IAAI,wBAAyB;YAClDC,GAAG,EAAE/B,OAAO,CAACgC;UAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFhC,OAAA;YACE4B,SAAS,EAAE,gBAAgBd,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC1DuB,OAAO,EAAEX,oBAAqB;YAC9BY,KAAK,EAAExB,YAAY,GAAG,sBAAsB,GAAG,iBAAkB;YAAAa,QAAA,eAEjE3B,OAAA;cAAG4B,SAAS,EAAEd,YAAY,GAAG,cAAc,GAAG;YAAe;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAK4B,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B3B,OAAA;UAAK4B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B3B,OAAA;YAAA2B,QAAA,EAAKvB,OAAO,CAACgC;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBhC,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC7B3B,OAAA;cAAK4B,SAAS,EAAC,OAAO;cAAAD,QAAA,gBACpB3B,OAAA;gBAAG4B,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhC,OAAA;gBAAG4B,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhC,OAAA;gBAAG4B,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhC,OAAA;gBAAG4B,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhC,OAAA;gBAAG4B,SAAS,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNhC,OAAA;cAAM4B,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5B3B,OAAA;YAAK4B,SAAS,EAAC,eAAe;YAAAD,QAAA,GAAC,GAAC,EAACvB,OAAO,CAACmC,KAAK;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDhC,OAAA;YAAK4B,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,EAEL5B,OAAO,CAACoC,aAAa,iBACpBxC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3B,OAAA;YAAG4B,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BhC,OAAA;YAAA2B,QAAA,EAAM;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjChC,OAAA;YAAK4B,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAEjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDhC,OAAA;UAAK4B,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAClC3B,OAAA;YAAA2B,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBhC,OAAA;YAAA2B,QAAA,EAAIvB,OAAO,CAACqC;UAAW;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAEL5B,OAAO,CAACsC,IAAI,IAAItC,OAAO,CAACsC,IAAI,CAACC,MAAM,GAAG,CAAC,iBACtC3C,OAAA;UAAK4B,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3B3B,OAAA;YAAA2B,QAAA,EAAI;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBhC,OAAA;YAAK4B,SAAS,EAAC,WAAW;YAAAD,QAAA,EACvBvB,OAAO,CAACsC,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3B9C,OAAA;cAAkB4B,SAAS,EAAC,KAAK;cAAAD,QAAA,EAC9BkB;YAAG,GADKC,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDhC,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC/B3B,OAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC3B,OAAA;cAAO+C,OAAO,EAAC,UAAU;cAAApB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3ChC,OAAA;cAAK4B,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChC3B,OAAA;gBACEqC,OAAO,EAAEA,CAAA,KAAM5B,WAAW,CAACuC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzC,QAAQ,GAAG,CAAC,CAAC,CAAE;gBACtDoB,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAExB3B,OAAA;kBAAG4B,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACThC,OAAA;gBACEkD,IAAI,EAAC,QAAQ;gBACb/C,EAAE,EAAC,UAAU;gBACbgD,KAAK,EAAE3C,QAAS;gBAChB4C,QAAQ,EAAGC,CAAC,IAAK5C,WAAW,CAACuC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEK,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAE;gBACzEK,GAAG,EAAC,GAAG;gBACP5B,SAAS,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFhC,OAAA;gBACEqC,OAAO,EAAEA,CAAA,KAAM5B,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;gBACzCoB,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAExB3B,OAAA;kBAAG4B,SAAS,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhC,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC7B3B,OAAA;cAAQqC,OAAO,EAAEb,eAAgB;cAACI,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC3D3B,OAAA;gBAAG4B,SAAS,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA;cAAQ4B,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC7B3B,OAAA;gBAAG4B,SAAS,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC/B3B,OAAA;YAAK4B,SAAS,EAAC,SAAS;YAAAD,QAAA,gBACtB3B,OAAA;cAAG4B,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChChC,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BhC,OAAA;gBAAA2B,QAAA,EAAM;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK4B,SAAS,EAAC,SAAS;YAAAD,QAAA,gBACtB3B,OAAA;cAAG4B,SAAS,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BhC,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BhC,OAAA;gBAAA2B,QAAA,EAAM;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK4B,SAAS,EAAC,SAAS;YAAAD,QAAA,gBACtB3B,OAAA;cAAG4B,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrChC,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAA2B,QAAA,EAAQ;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChChC,OAAA;gBAAA2B,QAAA,EAAM;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhC,OAAA,CAACH,cAAc;MAAC4D,SAAS,EAAErD,OAAO,CAACa;IAAI;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1ChC,OAAA;MAAO0D,GAAG;MAAA/B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX;AAAC9B,EAAA,CA3hBQD,WAAW;EAAA,QACHP,SAAS;AAAA;AAAAiE,EAAA,GADjB1D,WAAW;AA6hBpB,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}