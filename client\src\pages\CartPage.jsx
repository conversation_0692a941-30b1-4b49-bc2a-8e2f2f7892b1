import React, { useContext } from 'react';
import { CartContext } from '../context/CartContext';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';

function CartPage() {
  const { cart, removeFromCart, clearCart } = useContext(CartContext);
  const { token } = useContext(AuthContext);

  const total = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);

  async function handleCheckout() {
    try {
      await axios.post(
        'http://localhost:5000/api/orders',
        { items: cart, total },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      clearCart();
      alert('Order placed! Points awarded.');
    } catch (err) {
      alert('Checkout failed.');
    }
  }

  return (
    <div>
      <h2>Your Cart</h2>
      {cart.length === 0 ? (
        <p>Cart is empty.</p>
      ) : (
        <div>
          {cart.map(item => (
            <div key={item._id}>
              <span>{item.name} x {item.quantity}</span>
              <button onClick={() => removeFromCart(item._id)}>Remove</button>
            </div>
          ))}
          <h3>Total: ${total}</h3>
          <button onClick={handleCheckout} disabled={cart.length === 0}>Checkout</button>
          <button onClick={clearCart} style={{ marginLeft: '10px' }}>Clear Cart</button>
        </div>
      )}
    </div>
  );
}

export default CartPage;