{"version": 3, "names": ["_availablePlugins", "require", "originalPlugins", "originalPluginsBugfixes", "originalOverlappingPlugins", "keys", "Object", "plugins", "exports", "filterAvailable", "pluginsBugfixes", "overlappingPlugins", "data", "result", "plugin", "hasOwnProperty", "call", "availablePlugins"], "sources": ["../src/plugins-compat-data.ts"], "sourcesContent": ["import originalPlugins from \"@babel/compat-data/plugins\" with { type: \"json\" };\nimport originalPluginsBugfixes from \"@babel/compat-data/plugin-bugfixes\" with { type: \"json\" };\nimport originalOverlappingPlugins from \"@babel/compat-data/overlapping-plugins\" with { type: \"json\" };\nimport availablePlugins from \"./available-plugins.ts\";\n\nconst keys: <O extends object>(o: O) => (keyof O)[] = Object.keys;\n\nexport const plugins = filterAvailable(originalPlugins);\nexport const pluginsBugfixes = filterAvailable(originalPluginsBugfixes);\nexport const overlappingPlugins = filterAvailable(originalOverlappingPlugins);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error: we extend this here, since it's a syntax plugin and thus\n  // doesn't make sense to store it in a compat-data package.\n  overlappingPlugins[\"syntax-import-attributes\"] = [\"syntax-import-assertions\"];\n}\n\nfunction filterAvailable<Data extends { [name: string]: unknown }>(\n  data: Data,\n): { [Name in keyof Data & keyof typeof availablePlugins]: Data[Name] } {\n  const result = {} as any;\n  for (const plugin of keys(data)) {\n    if (Object.hasOwn(availablePlugins, plugin)) {\n      result[plugin] = data[plugin];\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,iBAAA,GAAAC,OAAA;AAAsD,MAH/CC,eAAe,GAAAD,OAAA,CAAM,4BAA4B;EACjDE,uBAAuB,GAAAF,OAAA,CAAM,oCAAoC;EACjEG,0BAA0B,GAAAH,OAAA,CAAM,wCAAwC;AAG/E,MAAMI,IAA6C,GAAGC,MAAM,CAACD,IAAI;AAE1D,MAAME,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAGE,eAAe,CAACP,eAAe,CAAC;AAChD,MAAMQ,eAAe,GAAAF,OAAA,CAAAE,eAAA,GAAGD,eAAe,CAACN,uBAAuB,CAAC;AAChE,MAAMQ,kBAAkB,GAAAH,OAAA,CAAAG,kBAAA,GAAGF,eAAe,CAACL,0BAA0B,CAAC;AAE1C;EAGjCO,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC/E;AAEA,SAASF,eAAeA,CACtBG,IAAU,EAC4D;EACtE,MAAMC,MAAM,GAAG,CAAC,CAAQ;EACxB,KAAK,MAAMC,MAAM,IAAIT,IAAI,CAACO,IAAI,CAAC,EAAE;IAC/B,IAAIG,cAAA,CAAAC,IAAA,CAAcC,yBAAgB,EAAEH,MAAM,CAAC,EAAE;MAC3CD,MAAM,CAACC,MAAM,CAAC,GAAGF,IAAI,CAACE,MAAM,CAAC;IAC/B;EACF;EACA,OAAOD,MAAM;AACf", "ignoreList": []}