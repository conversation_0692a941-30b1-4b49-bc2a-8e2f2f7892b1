{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\ProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductPage() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const {\n    addToCart\n  } = useContext(CartContext);\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    fetchProduct();\n  }, [id]);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 23\n  }, this);\n  if (!product) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Product not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 24\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '800px',\n      margin: '0 auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '30px',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          minWidth: '300px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.imageUrl || '/placeholder-image.jpg',\n          alt: product.name,\n          style: {\n            width: '100%',\n            maxWidth: '400px',\n            height: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          minWidth: '300px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '24px',\n            color: '#28a745',\n            fontWeight: 'bold'\n          },\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), product.isEcoFriendly && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#28a745',\n            marginBottom: '10px'\n          },\n          children: \"\\uD83C\\uDF31 Eco-Friendly Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), product.tags && product.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tags: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), product.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#e9ecef',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              marginRight: '5px'\n            },\n            children: tag\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => addToCart(product),\n          style: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '16px'\n          },\n          children: \"Add to Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductPage, \"nwliOH/ewzXpisA3P6mRe91fHFg=\", false, function () {\n  return [useParams];\n});\n_c = ProductPage;\nexport default ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useParams", "CartContext", "WishlistContext", "ProductReviews", "axios", "jsxDEV", "_jsxDEV", "ProductPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "addToCart", "fetchProduct", "response", "get", "data", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "display", "gap", "flexWrap", "flex", "min<PERSON><PERSON><PERSON>", "src", "imageUrl", "alt", "name", "width", "height", "fontSize", "color", "fontWeight", "price", "isEcoFriendly", "marginBottom", "description", "tags", "length", "map", "tag", "index", "backgroundColor", "borderRadius", "marginRight", "onClick", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\n\nfunction ProductPage() {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const { addToCart } = useContext(CartContext);\n\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    \n    fetchProduct();\n  }, [id]);\n\n  if (loading) return <div>Loading...</div>;\n  if (!product) return <div>Product not found</div>;\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <div style={{ display: 'flex', gap: '30px', flexWrap: 'wrap' }}>\n        <div style={{ flex: '1', minWidth: '300px' }}>\n          <img \n            src={product.imageUrl || '/placeholder-image.jpg'} \n            alt={product.name}\n            style={{ width: '100%', maxWidth: '400px', height: 'auto' }}\n          />\n        </div>\n        <div style={{ flex: '1', minWidth: '300px' }}>\n          <h1>{product.name}</h1>\n          <p style={{ fontSize: '24px', color: '#28a745', fontWeight: 'bold' }}>\n            ${product.price}\n          </p>\n          {product.isEcoFriendly && (\n            <div style={{ color: '#28a745', marginBottom: '10px' }}>\n              🌱 Eco-Friendly Product\n            </div>\n          )}\n          <p style={{ marginBottom: '20px' }}>{product.description}</p>\n          {product.tags && product.tags.length > 0 && (\n            <div style={{ marginBottom: '20px' }}>\n              <strong>Tags: </strong>\n              {product.tags.map((tag, index) => (\n                <span key={index} style={{ \n                  backgroundColor: '#e9ecef', \n                  padding: '4px 8px', \n                  borderRadius: '4px', \n                  marginRight: '5px' \n                }}>\n                  {tag}\n                </span>\n              ))}\n            </div>\n          )}\n          <button \n            onClick={() => addToCart(product)}\n            style={{\n              backgroundColor: '#007bff',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '16px'\n            }}\n          >\n            Add to Cart\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default ProductPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEiB;EAAU,CAAC,GAAGf,UAAU,CAACE,WAAW,CAAC;EAE7CH,SAAS,CAAC,MAAM;IACd,eAAeiB,YAAYA,CAAA,EAAG;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,sCAAsCR,EAAE,EAAE,CAAC;QAC5EE,UAAU,CAACK,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;IAEAE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACN,EAAE,CAAC,CAAC;EAER,IAAIG,OAAO,EAAE,oBAAON,OAAA;IAAAe,QAAA,EAAK;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzC,IAAI,CAACf,OAAO,EAAE,oBAAOJ,OAAA;IAAAe,QAAA,EAAK;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAEjD,oBACEnB,OAAA;IAAKoB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,eACnEf,OAAA;MAAKoB,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,GAAG,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAX,QAAA,gBAC7Df,OAAA;QAAKoB,KAAK,EAAE;UAAEO,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAb,QAAA,eAC3Cf,OAAA;UACE6B,GAAG,EAAEzB,OAAO,CAAC0B,QAAQ,IAAI,wBAAyB;UAClDC,GAAG,EAAE3B,OAAO,CAAC4B,IAAK;UAClBZ,KAAK,EAAE;YAAEa,KAAK,EAAE,MAAM;YAAEX,QAAQ,EAAE,OAAO;YAAEY,MAAM,EAAE;UAAO;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnB,OAAA;QAAKoB,KAAK,EAAE;UAAEO,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAb,QAAA,gBAC3Cf,OAAA;UAAAe,QAAA,EAAKX,OAAO,CAAC4B;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvBnB,OAAA;UAAGoB,KAAK,EAAE;YAAEe,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAtB,QAAA,GAAC,GACnE,EAACX,OAAO,CAACkC,KAAK;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,EACHf,OAAO,CAACmC,aAAa,iBACpBvC,OAAA;UAAKoB,KAAK,EAAE;YAAEgB,KAAK,EAAE,SAAS;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eACDnB,OAAA;UAAGoB,KAAK,EAAE;YAAEoB,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAAEX,OAAO,CAACqC;QAAW;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5Df,OAAO,CAACsC,IAAI,IAAItC,OAAO,CAACsC,IAAI,CAACC,MAAM,GAAG,CAAC,iBACtC3C,OAAA;UAAKoB,KAAK,EAAE;YAAEoB,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,gBACnCf,OAAA;YAAAe,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACtBf,OAAO,CAACsC,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3B9C,OAAA;YAAkBoB,KAAK,EAAE;cACvB2B,eAAe,EAAE,SAAS;cAC1B1B,OAAO,EAAE,SAAS;cAClB2B,YAAY,EAAE,KAAK;cACnBC,WAAW,EAAE;YACf,CAAE;YAAAlC,QAAA,EACC8B;UAAG,GANKC,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACDnB,OAAA;UACEkD,OAAO,EAAEA,CAAA,KAAM1C,SAAS,CAACJ,OAAO,CAAE;UAClCgB,KAAK,EAAE;YACL2B,eAAe,EAAE,SAAS;YAC1BX,KAAK,EAAE,OAAO;YACde,MAAM,EAAE,MAAM;YACd9B,OAAO,EAAE,WAAW;YACpB2B,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,SAAS;YACjBjB,QAAQ,EAAE;UACZ,CAAE;UAAApB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjB,EAAA,CA9EQD,WAAW;EAAA,QACHP,SAAS;AAAA;AAAA2D,EAAA,GADjBpD,WAAW;AAgFpB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}