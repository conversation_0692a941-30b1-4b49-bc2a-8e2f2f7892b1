{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport { AuthProvider } from './context/AuthContext';\nimport { CartProvider } from './context/CartContext';\nimport { WishlistProvider } from './context/WishlistContext';\nimport { UserStatsProvider } from './context/UserStatsContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(AuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(UserStatsProvider, {\n    children: /*#__PURE__*/_jsxDEV(CartProvider, {\n      children: /*#__PURE__*/_jsxDEV(WishlistProvider, {\n        children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON>th<PERSON><PERSON><PERSON>", "CartProvider", "WishlistProvider", "UserStatsProvider", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport App from './App';\r\nimport { AuthProvider } from './context/AuthContext';\r\nimport { CartProvider } from './context/CartContext';\r\nimport { WishlistProvider } from './context/WishlistContext';\r\nimport { UserStatsProvider } from './context/UserStatsContext';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n  <AuthProvider>\r\n    <UserStatsProvider>\r\n      <CartProvider>\r\n        <WishlistProvider>\r\n          <App />\r\n        </WishlistProvider>\r\n      </CartProvider>\r\n    </UserStatsProvider>\r\n  </AuthProvider>\r\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,IAAI,GAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACL,YAAY;EAAAW,QAAA,eACXN,OAAA,CAACF,iBAAiB;IAAAQ,QAAA,eAChBN,OAAA,CAACJ,YAAY;MAAAU,QAAA,eACXN,OAAA,CAACH,gBAAgB;QAAAS,QAAA,eACfN,OAAA,CAACN,GAAG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACR,CAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}