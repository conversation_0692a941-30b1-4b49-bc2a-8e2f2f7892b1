{"ast": null, "code": "import React,{useState,useEffect,useContext}from'react';import{useParams}from'react-router-dom';import{CartContext}from'../context/CartContext';import{WishlistContext}from'../context/WishlistContext';import ProductReviews from'../components/ProductReviews';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ProductPage(){const{id}=useParams();const[product,setProduct]=useState(null);const[loading,setLoading]=useState(true);const[quantity,setQuantity]=useState(1);const{addToCart}=useContext(CartContext);const{wishlist,addToWishlist,removeFromWishlist}=useContext(WishlistContext)||{wishlist:[],addToWishlist:()=>{},removeFromWishlist:()=>{}};const isInWishlist=wishlist.some(item=>item._id===(product===null||product===void 0?void 0:product._id));useEffect(()=>{async function fetchProduct(){try{const response=await axios.get(\"http://localhost:5000/api/products/\".concat(id));setProduct(response.data);}catch(error){console.error('Error fetching product:',error);}finally{setLoading(false);}}fetchProduct();},[id]);const handleAddToCart=()=>{for(let i=0;i<quantity;i++){addToCart(product);}};const handleWishlistToggle=()=>{if(isInWishlist){removeFromWishlist(product._id);}else{addToWishlist(product);}};if(loading){return/*#__PURE__*/_jsx(\"main\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-spinner fa-spin\"})}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading product details...\"})]})});}if(!product){return/*#__PURE__*/_jsx(\"main\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"error-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-exclamation-triangle\"})}),/*#__PURE__*/_jsx(\"h2\",{children:\"Product not found\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The product you're looking for doesn't exist or has been removed.\"})]})});}return/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"product-page\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"product-gallery\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"main-image\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.imageUrl||'/placeholder-image.jpg',alt:product.name}),/*#__PURE__*/_jsx(\"button\",{className:\"wishlist-btn \".concat(isInWishlist?'active':''),onClick:handleWishlistToggle,title:isInWishlist?'Remove from wishlist':'Add to wishlist',children:/*#__PURE__*/_jsx(\"i\",{className:isInWishlist?'fas fa-heart':'far fa-heart'})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"product-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-rating\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stars\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star-half-alt\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"rating-text\",children:\"(4.5) \\u2022 127 reviews\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"price-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"current-price\",children:[\"$\",product.price]}),/*#__PURE__*/_jsx(\"div\",{className:\"price-note\",children:\"Free shipping on orders over $50\"})]}),product.isEcoFriendly&&/*#__PURE__*/_jsxs(\"div\",{className:\"eco-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-leaf\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Eco-Friendly Product\"}),/*#__PURE__*/_jsx(\"div\",{className:\"eco-description\",children:\"This product is made with sustainable materials and eco-friendly processes.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-description\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"p\",{children:product.description})]}),product.tags&&product.tags.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"product-tags\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Features\"}),/*#__PURE__*/_jsx(\"div\",{className:\"tags-list\",children:product.tags.map((tag,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"tag\",children:tag},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"purchase-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"quantity-selector\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"quantity\",children:\"Quantity:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"quantity-controls\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setQuantity(Math.max(1,quantity-1)),className:\"quantity-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-minus\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"quantity\",value:quantity,onChange:e=>setQuantity(Math.max(1,parseInt(e.target.value)||1)),min:\"1\",className:\"quantity-input\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setQuantity(quantity+1),className:\"quantity-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddToCart,className:\"add-to-cart-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"}),\"Add to Cart\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"buy-now-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-bolt\"}),\"Buy Now\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Free Shipping\"}),/*#__PURE__*/_jsx(\"span\",{children:\"On orders over $50\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-undo\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"30-Day Returns\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Easy returns & exchanges\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shield-alt\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"2-Year Warranty\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Full manufacturer warranty\"})]})]})]})]})]}),/*#__PURE__*/_jsx(ProductReviews,{productId:product._id}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .product-page {\\n          display: grid;\\n          grid-template-columns: 1fr 1fr;\\n          gap: 4rem;\\n          max-width: 1200px;\\n          margin: 0 auto;\\n          padding: 2rem;\\n        }\\n\\n        .product-gallery {\\n          position: sticky;\\n          top: 2rem;\\n          height: fit-content;\\n        }\\n\\n        .main-image {\\n          position: relative;\\n          border-radius: 1rem;\\n          overflow: hidden;\\n          box-shadow: var(--shadow-lg);\\n        }\\n\\n        .main-image img {\\n          width: 100%;\\n          height: 500px;\\n          object-fit: cover;\\n        }\\n\\n        .wishlist-btn {\\n          position: absolute;\\n          top: 1rem;\\n          right: 1rem;\\n          background: rgba(255, 255, 255, 0.9);\\n          border: none;\\n          border-radius: 50%;\\n          width: 50px;\\n          height: 50px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          font-size: 1.25rem;\\n          transition: all 0.2s;\\n          backdrop-filter: blur(4px);\\n        }\\n\\n        .wishlist-btn:hover {\\n          background: rgba(255, 255, 255, 1);\\n          transform: scale(1.1);\\n        }\\n\\n        .wishlist-btn.active {\\n          color: #ef4444;\\n        }\\n\\n        .product-info {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 2rem;\\n        }\\n\\n        .product-header h1 {\\n          font-size: 2.5rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n          margin: 0 0 1rem 0;\\n          line-height: 1.2;\\n        }\\n\\n        .product-rating {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n        }\\n\\n        .stars {\\n          display: flex;\\n          gap: 0.25rem;\\n          color: #fbbf24;\\n          font-size: 1.125rem;\\n        }\\n\\n        .rating-text {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .price-section {\\n          padding: 1.5rem 0;\\n          border-top: 1px solid var(--border-color);\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        .current-price {\\n          font-size: 2.5rem;\\n          font-weight: 700;\\n          color: var(--primary-color);\\n          margin-bottom: 0.5rem;\\n        }\\n\\n        .price-note {\\n          color: var(--secondary-color);\\n          font-weight: 500;\\n        }\\n\\n        .eco-badge {\\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\\n          color: white;\\n          padding: 1.5rem;\\n          border-radius: 1rem;\\n          display: flex;\\n          flex-direction: column;\\n          gap: 0.5rem;\\n        }\\n\\n        .eco-badge > span {\\n          font-weight: 600;\\n          font-size: 1.125rem;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .eco-description {\\n          font-size: 0.875rem;\\n          opacity: 0.9;\\n        }\\n\\n        .product-description h3,\\n        .product-tags h3 {\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          margin: 0 0 1rem 0;\\n        }\\n\\n        .product-description p {\\n          color: var(--text-secondary);\\n          line-height: 1.7;\\n          margin: 0;\\n        }\\n\\n        .tags-list {\\n          display: flex;\\n          flex-wrap: wrap;\\n          gap: 0.75rem;\\n        }\\n\\n        .tag {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          padding: 0.5rem 1rem;\\n          border-radius: 9999px;\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .purchase-section {\\n          background: var(--bg-secondary);\\n          padding: 2rem;\\n          border-radius: 1rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .quantity-selector {\\n          margin-bottom: 2rem;\\n        }\\n\\n        .quantity-selector label {\\n          display: block;\\n          margin-bottom: 0.75rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .quantity-controls {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .quantity-btn {\\n          background: white;\\n          border: 2px solid var(--border-color);\\n          width: 40px;\\n          height: 40px;\\n          border-radius: 0.5rem;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n        }\\n\\n        .quantity-btn:hover {\\n          border-color: var(--primary-color);\\n          color: var(--primary-color);\\n        }\\n\\n        .quantity-input {\\n          width: 80px;\\n          height: 40px;\\n          text-align: center;\\n          border: 2px solid var(--border-color);\\n          border-radius: 0.5rem;\\n          font-size: 1rem;\\n          font-weight: 500;\\n        }\\n\\n        .quantity-input:focus {\\n          outline: none;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .action-buttons {\\n          display: flex;\\n          gap: 1rem;\\n        }\\n\\n        .add-to-cart-btn,\\n        .buy-now-btn {\\n          flex: 1;\\n          padding: 1rem 2rem;\\n          border-radius: 0.75rem;\\n          font-weight: 600;\\n          font-size: 1rem;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .add-to-cart-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n        }\\n\\n        .add-to-cart-btn:hover {\\n          background: var(--primary-dark);\\n          transform: translateY(-1px);\\n        }\\n\\n        .buy-now-btn {\\n          background: var(--accent-color);\\n          color: white;\\n          border: none;\\n        }\\n\\n        .buy-now-btn:hover {\\n          background: #d97706;\\n          transform: translateY(-1px);\\n        }\\n\\n        .product-features {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 1rem;\\n        }\\n\\n        .feature {\\n          display: flex;\\n          align-items: center;\\n          gap: 1rem;\\n          padding: 1rem;\\n          background: white;\\n          border-radius: 0.75rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .feature i {\\n          font-size: 1.5rem;\\n          color: var(--primary-color);\\n          width: 24px;\\n          text-align: center;\\n        }\\n\\n        .feature div {\\n          display: flex;\\n          flex-direction: column;\\n        }\\n\\n        .feature strong {\\n          color: var(--text-primary);\\n          font-weight: 600;\\n        }\\n\\n        .feature span {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .loading-container,\\n        .error-container {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          min-height: 60vh;\\n          text-align: center;\\n          gap: 1rem;\\n        }\\n\\n        .loading-spinner,\\n        .error-icon {\\n          font-size: 3rem;\\n          color: var(--primary-color);\\n        }\\n\\n        .error-icon {\\n          color: #ef4444;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .product-page {\\n            grid-template-columns: 1fr;\\n            gap: 2rem;\\n            padding: 1rem;\\n          }\\n\\n          .product-header h1 {\\n            font-size: 2rem;\\n          }\\n\\n          .current-price {\\n            font-size: 2rem;\\n          }\\n\\n          .action-buttons {\\n            flex-direction: column;\\n          }\\n        }\\n      \"})]});}export default ProductPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useParams", "CartContext", "WishlistContext", "ProductReviews", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "ProductPage", "id", "product", "setProduct", "loading", "setLoading", "quantity", "setQuantity", "addToCart", "wishlist", "addToWishlist", "removeFromWishlist", "isInWishlist", "some", "item", "_id", "fetchProduct", "response", "get", "concat", "data", "error", "console", "handleAddToCart", "i", "handleWishlistToggle", "children", "className", "src", "imageUrl", "alt", "name", "onClick", "title", "price", "isEcoFriendly", "description", "tags", "length", "map", "tag", "index", "htmlFor", "Math", "max", "type", "value", "onChange", "e", "parseInt", "target", "min", "productId"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\n\nfunction ProductPage() {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const { addToCart } = useContext(CartContext);\n  const { wishlist, addToWishlist, removeFromWishlist } = useContext(WishlistContext) || { wishlist: [], addToWishlist: () => {}, removeFromWishlist: () => {} };\n\n  const isInWishlist = wishlist.some(item => item._id === product?._id);\n\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    \n    fetchProduct();\n  }, [id]);\n\n  const handleAddToCart = () => {\n    for (let i = 0; i < quantity; i++) {\n      addToCart(product);\n    }\n  };\n\n  const handleWishlistToggle = () => {\n    if (isInWishlist) {\n      removeFromWishlist(product._id);\n    } else {\n      addToWishlist(product);\n    }\n  };\n\n  if (loading) {\n    return (\n      <main>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\">\n            <i className=\"fas fa-spinner fa-spin\"></i>\n          </div>\n          <p>Loading product details...</p>\n        </div>\n      </main>\n    );\n  }\n\n  if (!product) {\n    return (\n      <main>\n        <div className=\"error-container\">\n          <div className=\"error-icon\">\n            <i className=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <h2>Product not found</h2>\n          <p>The product you're looking for doesn't exist or has been removed.</p>\n        </div>\n      </main>\n    );\n  }\n\n  return (\n    <main>\n      <div className=\"product-page\">\n        <div className=\"product-gallery\">\n          <div className=\"main-image\">\n            <img\n              src={product.imageUrl || '/placeholder-image.jpg'}\n              alt={product.name}\n            />\n            <button\n              className={`wishlist-btn ${isInWishlist ? 'active' : ''}`}\n              onClick={handleWishlistToggle}\n              title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}\n            >\n              <i className={isInWishlist ? 'fas fa-heart' : 'far fa-heart'}></i>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"product-info\">\n          <div className=\"product-header\">\n            <h1>{product.name}</h1>\n            <div className=\"product-rating\">\n              <div className=\"stars\">\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star\"></i>\n                <i className=\"fas fa-star-half-alt\"></i>\n              </div>\n              <span className=\"rating-text\">(4.5) • 127 reviews</span>\n            </div>\n          </div>\n\n          <div className=\"price-section\">\n            <div className=\"current-price\">${product.price}</div>\n            <div className=\"price-note\">Free shipping on orders over $50</div>\n          </div>\n\n          {product.isEcoFriendly && (\n            <div className=\"eco-badge\">\n              <i className=\"fas fa-leaf\"></i>\n              <span>Eco-Friendly Product</span>\n              <div className=\"eco-description\">\n                This product is made with sustainable materials and eco-friendly processes.\n              </div>\n            </div>\n          )}\n\n          <div className=\"product-description\">\n            <h3>Description</h3>\n            <p>{product.description}</p>\n          </div>\n\n          {product.tags && product.tags.length > 0 && (\n            <div className=\"product-tags\">\n              <h3>Features</h3>\n              <div className=\"tags-list\">\n                {product.tags.map((tag, index) => (\n                  <span key={index} className=\"tag\">\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"purchase-section\">\n            <div className=\"quantity-selector\">\n              <label htmlFor=\"quantity\">Quantity:</label>\n              <div className=\"quantity-controls\">\n                <button\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  className=\"quantity-btn\"\n                >\n                  <i className=\"fas fa-minus\"></i>\n                </button>\n                <input\n                  type=\"number\"\n                  id=\"quantity\"\n                  value={quantity}\n                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}\n                  min=\"1\"\n                  className=\"quantity-input\"\n                />\n                <button\n                  onClick={() => setQuantity(quantity + 1)}\n                  className=\"quantity-btn\"\n                >\n                  <i className=\"fas fa-plus\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div className=\"action-buttons\">\n              <button onClick={handleAddToCart} className=\"add-to-cart-btn\">\n                <i className=\"fas fa-shopping-cart\"></i>\n                Add to Cart\n              </button>\n              <button className=\"buy-now-btn\">\n                <i className=\"fas fa-bolt\"></i>\n                Buy Now\n              </button>\n            </div>\n          </div>\n\n          <div className=\"product-features\">\n            <div className=\"feature\">\n              <i className=\"fas fa-truck\"></i>\n              <div>\n                <strong>Free Shipping</strong>\n                <span>On orders over $50</span>\n              </div>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-undo\"></i>\n              <div>\n                <strong>30-Day Returns</strong>\n                <span>Easy returns & exchanges</span>\n              </div>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-shield-alt\"></i>\n              <div>\n                <strong>2-Year Warranty</strong>\n                <span>Full manufacturer warranty</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <ProductReviews productId={product._id} />\n\n      <style jsx>{`\n        .product-page {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 4rem;\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .product-gallery {\n          position: sticky;\n          top: 2rem;\n          height: fit-content;\n        }\n\n        .main-image {\n          position: relative;\n          border-radius: 1rem;\n          overflow: hidden;\n          box-shadow: var(--shadow-lg);\n        }\n\n        .main-image img {\n          width: 100%;\n          height: 500px;\n          object-fit: cover;\n        }\n\n        .wishlist-btn {\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          background: rgba(255, 255, 255, 0.9);\n          border: none;\n          border-radius: 50%;\n          width: 50px;\n          height: 50px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          font-size: 1.25rem;\n          transition: all 0.2s;\n          backdrop-filter: blur(4px);\n        }\n\n        .wishlist-btn:hover {\n          background: rgba(255, 255, 255, 1);\n          transform: scale(1.1);\n        }\n\n        .wishlist-btn.active {\n          color: #ef4444;\n        }\n\n        .product-info {\n          display: flex;\n          flex-direction: column;\n          gap: 2rem;\n        }\n\n        .product-header h1 {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n          line-height: 1.2;\n        }\n\n        .product-rating {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .stars {\n          display: flex;\n          gap: 0.25rem;\n          color: #fbbf24;\n          font-size: 1.125rem;\n        }\n\n        .rating-text {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .price-section {\n          padding: 1.5rem 0;\n          border-top: 1px solid var(--border-color);\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .current-price {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          margin-bottom: 0.5rem;\n        }\n\n        .price-note {\n          color: var(--secondary-color);\n          font-weight: 500;\n        }\n\n        .eco-badge {\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\n          color: white;\n          padding: 1.5rem;\n          border-radius: 1rem;\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .eco-badge > span {\n          font-weight: 600;\n          font-size: 1.125rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .eco-description {\n          font-size: 0.875rem;\n          opacity: 0.9;\n        }\n\n        .product-description h3,\n        .product-tags h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .product-description p {\n          color: var(--text-secondary);\n          line-height: 1.7;\n          margin: 0;\n        }\n\n        .tags-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.75rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          font-size: 0.875rem;\n          font-weight: 500;\n          border: 1px solid var(--border-color);\n        }\n\n        .purchase-section {\n          background: var(--bg-secondary);\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .quantity-selector {\n          margin-bottom: 2rem;\n        }\n\n        .quantity-selector label {\n          display: block;\n          margin-bottom: 0.75rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .quantity-controls {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .quantity-btn {\n          background: white;\n          border: 2px solid var(--border-color);\n          width: 40px;\n          height: 40px;\n          border-radius: 0.5rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .quantity-btn:hover {\n          border-color: var(--primary-color);\n          color: var(--primary-color);\n        }\n\n        .quantity-input {\n          width: 80px;\n          height: 40px;\n          text-align: center;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          font-weight: 500;\n        }\n\n        .quantity-input:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .add-to-cart-btn,\n        .buy-now-btn {\n          flex: 1;\n          padding: 1rem 2rem;\n          border-radius: 0.75rem;\n          font-weight: 600;\n          font-size: 1rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n        }\n\n        .add-to-cart-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n        }\n\n        .add-to-cart-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .buy-now-btn {\n          background: var(--accent-color);\n          color: white;\n          border: none;\n        }\n\n        .buy-now-btn:hover {\n          background: #d97706;\n          transform: translateY(-1px);\n        }\n\n        .product-features {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .feature {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          background: white;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 1.5rem;\n          color: var(--primary-color);\n          width: 24px;\n          text-align: center;\n        }\n\n        .feature div {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .feature strong {\n          color: var(--text-primary);\n          font-weight: 600;\n        }\n\n        .feature span {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .loading-container,\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          min-height: 60vh;\n          text-align: center;\n          gap: 1rem;\n        }\n\n        .loading-spinner,\n        .error-icon {\n          font-size: 3rem;\n          color: var(--primary-color);\n        }\n\n        .error-icon {\n          color: #ef4444;\n        }\n\n        @media (max-width: 768px) {\n          .product-page {\n            grid-template-columns: 1fr;\n            gap: 2rem;\n            padding: 1rem;\n          }\n\n          .product-header h1 {\n            font-size: 2rem;\n          }\n\n          .current-price {\n            font-size: 2rem;\n          }\n\n          .action-buttons {\n            flex-direction: column;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default ProductPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CAC9D,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,eAAe,KAAQ,4BAA4B,CAC5D,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,WAAWA,CAAA,CAAG,CACrB,KAAM,CAAEC,EAAG,CAAC,CAAGV,SAAS,CAAC,CAAC,CAC1B,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAEoB,SAAU,CAAC,CAAGlB,UAAU,CAACE,WAAW,CAAC,CAC7C,KAAM,CAAEiB,QAAQ,CAAEC,aAAa,CAAEC,kBAAmB,CAAC,CAAGrB,UAAU,CAACG,eAAe,CAAC,EAAI,CAAEgB,QAAQ,CAAE,EAAE,CAAEC,aAAa,CAAEA,CAAA,GAAM,CAAC,CAAC,CAAEC,kBAAkB,CAAEA,CAAA,GAAM,CAAC,CAAE,CAAC,CAE9J,KAAM,CAAAC,YAAY,CAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,GAAG,IAAKb,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEa,GAAG,EAAC,CAErE1B,SAAS,CAAC,IAAM,CACd,cAAe,CAAA2B,YAAYA,CAAA,CAAG,CAC5B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAACuB,GAAG,uCAAAC,MAAA,CAAuClB,EAAE,CAAE,CAAC,CAC5EE,UAAU,CAACc,QAAQ,CAACG,IAAI,CAAC,CAC3B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,OAAS,CACRhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAEAW,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACf,EAAE,CAAC,CAAC,CAER,KAAM,CAAAsB,eAAe,CAAGA,CAAA,GAAM,CAC5B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGlB,QAAQ,CAAEkB,CAAC,EAAE,CAAE,CACjChB,SAAS,CAACN,OAAO,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAuB,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAIb,YAAY,CAAE,CAChBD,kBAAkB,CAACT,OAAO,CAACa,GAAG,CAAC,CACjC,CAAC,IAAM,CACLL,aAAa,CAACR,OAAO,CAAC,CACxB,CACF,CAAC,CAED,GAAIE,OAAO,CAAE,CACX,mBACEP,IAAA,SAAA6B,QAAA,cACE3B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC7B,IAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B7B,IAAA,MAAG8B,SAAS,CAAC,wBAAwB,CAAI,CAAC,CACvC,CAAC,cACN9B,IAAA,MAAA6B,QAAA,CAAG,4BAA0B,CAAG,CAAC,EAC9B,CAAC,CACF,CAAC,CAEX,CAEA,GAAI,CAACxB,OAAO,CAAE,CACZ,mBACEL,IAAA,SAAA6B,QAAA,cACE3B,KAAA,QAAK4B,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B7B,IAAA,QAAK8B,SAAS,CAAC,YAAY,CAAAD,QAAA,cACzB7B,IAAA,MAAG8B,SAAS,CAAC,6BAA6B,CAAI,CAAC,CAC5C,CAAC,cACN9B,IAAA,OAAA6B,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B7B,IAAA,MAAA6B,QAAA,CAAG,mEAAiE,CAAG,CAAC,EACrE,CAAC,CACF,CAAC,CAEX,CAEA,mBACE3B,KAAA,SAAA2B,QAAA,eACE3B,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B7B,IAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B3B,KAAA,QAAK4B,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB7B,IAAA,QACE+B,GAAG,CAAE1B,OAAO,CAAC2B,QAAQ,EAAI,wBAAyB,CAClDC,GAAG,CAAE5B,OAAO,CAAC6B,IAAK,CACnB,CAAC,cACFlC,IAAA,WACE8B,SAAS,iBAAAR,MAAA,CAAkBP,YAAY,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC1DoB,OAAO,CAAEP,oBAAqB,CAC9BQ,KAAK,CAAErB,YAAY,CAAG,sBAAsB,CAAG,iBAAkB,CAAAc,QAAA,cAEjE7B,IAAA,MAAG8B,SAAS,CAAEf,YAAY,CAAG,cAAc,CAAG,cAAe,CAAI,CAAC,CAC5D,CAAC,EACN,CAAC,CACH,CAAC,cAENb,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B3B,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B7B,IAAA,OAAA6B,QAAA,CAAKxB,OAAO,CAAC6B,IAAI,CAAK,CAAC,cACvBhC,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B3B,KAAA,QAAK4B,SAAS,CAAC,OAAO,CAAAD,QAAA,eACpB7B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B9B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B9B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B9B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B9B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAI,CAAC,EACrC,CAAC,cACN9B,IAAA,SAAM8B,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,0BAAmB,CAAM,CAAC,EACrD,CAAC,EACH,CAAC,cAEN3B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B3B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAD,QAAA,EAAC,GAAC,CAACxB,OAAO,CAACgC,KAAK,EAAM,CAAC,cACrDrC,IAAA,QAAK8B,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,kCAAgC,CAAK,CAAC,EAC/D,CAAC,CAELxB,OAAO,CAACiC,aAAa,eACpBpC,KAAA,QAAK4B,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB7B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B9B,IAAA,SAAA6B,QAAA,CAAM,sBAAoB,CAAM,CAAC,cACjC7B,IAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAAC,6EAEjC,CAAK,CAAC,EACH,CACN,cAED3B,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7B,IAAA,OAAA6B,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB7B,IAAA,MAAA6B,QAAA,CAAIxB,OAAO,CAACkC,WAAW,CAAI,CAAC,EACzB,CAAC,CAELlC,OAAO,CAACmC,IAAI,EAAInC,OAAO,CAACmC,IAAI,CAACC,MAAM,CAAG,CAAC,eACtCvC,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B7B,IAAA,OAAA6B,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjB7B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBxB,OAAO,CAACmC,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,gBAC3B5C,IAAA,SAAkB8B,SAAS,CAAC,KAAK,CAAAD,QAAA,CAC9Bc,GAAG,EADKC,KAEL,CACP,CAAC,CACC,CAAC,EACH,CACN,cAED1C,KAAA,QAAK4B,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC7B,IAAA,UAAO6C,OAAO,CAAC,UAAU,CAAAhB,QAAA,CAAC,WAAS,CAAO,CAAC,cAC3C3B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC7B,IAAA,WACEmC,OAAO,CAAEA,CAAA,GAAMzB,WAAW,CAACoC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEtC,QAAQ,CAAG,CAAC,CAAC,CAAE,CACtDqB,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExB7B,IAAA,MAAG8B,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,cACT9B,IAAA,UACEgD,IAAI,CAAC,QAAQ,CACb5C,EAAE,CAAC,UAAU,CACb6C,KAAK,CAAExC,QAAS,CAChByC,QAAQ,CAAGC,CAAC,EAAKzC,WAAW,CAACoC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEK,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,EAAI,CAAC,CAAC,CAAE,CACzEK,GAAG,CAAC,GAAG,CACPxB,SAAS,CAAC,gBAAgB,CAC3B,CAAC,cACF9B,IAAA,WACEmC,OAAO,CAAEA,CAAA,GAAMzB,WAAW,CAACD,QAAQ,CAAG,CAAC,CAAE,CACzCqB,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExB7B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,CACzB,CAAC,EACN,CAAC,EACH,CAAC,cAEN5B,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B3B,KAAA,WAAQiC,OAAO,CAAET,eAAgB,CAACI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC3D7B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAI,CAAC,cAE1C,EAAQ,CAAC,cACT5B,KAAA,WAAQ4B,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC7B7B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,UAEjC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN5B,KAAA,QAAK4B,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3B,KAAA,QAAK4B,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtB7B,IAAA,MAAG8B,SAAS,CAAC,cAAc,CAAI,CAAC,cAChC5B,KAAA,QAAA2B,QAAA,eACE7B,IAAA,WAAA6B,QAAA,CAAQ,eAAa,CAAQ,CAAC,cAC9B7B,IAAA,SAAA6B,QAAA,CAAM,oBAAkB,CAAM,CAAC,EAC5B,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK4B,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtB7B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B5B,KAAA,QAAA2B,QAAA,eACE7B,IAAA,WAAA6B,QAAA,CAAQ,gBAAc,CAAQ,CAAC,cAC/B7B,IAAA,SAAA6B,QAAA,CAAM,0BAAwB,CAAM,CAAC,EAClC,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK4B,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtB7B,IAAA,MAAG8B,SAAS,CAAC,mBAAmB,CAAI,CAAC,cACrC5B,KAAA,QAAA2B,QAAA,eACE7B,IAAA,WAAA6B,QAAA,CAAQ,iBAAe,CAAQ,CAAC,cAChC7B,IAAA,SAAA6B,QAAA,CAAM,4BAA0B,CAAM,CAAC,EACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7B,IAAA,CAACH,cAAc,EAAC0D,SAAS,CAAElD,OAAO,CAACa,GAAI,CAAE,CAAC,cAE1ClB,IAAA,UAAOD,GAAG,MAAA8B,QAAA,kyPAgVD,CAAC,EACN,CAAC,CAEX,CAEA,cAAe,CAAA1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}