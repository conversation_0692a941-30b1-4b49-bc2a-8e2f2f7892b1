{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useState}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";export const CartContext=/*#__PURE__*/createContext();export function CartProvider(_ref){let{children}=_ref;const[cart,setCart]=useState([]);function addToCart(product){setCart(prev=>prev.some(item=>item._id===product._id)?prev.map(item=>item._id===product._id?_objectSpread(_objectSpread({},item),{},{quantity:item.quantity+1}):item):[...prev,_objectSpread(_objectSpread({},product),{},{quantity:1})]);}function removeFromCart(productId){setCart(prev=>prev.filter(item=>item._id!==productId));}function updateQuantity(productId,newQuantity){setCart(prev=>prev.map(item=>item._id===productId?_objectSpread(_objectSpread({},item),{},{quantity:newQuantity}):item));}function clearCart(){setCart([]);}return/*#__PURE__*/_jsx(CartContext.Provider,{value:{cart,addToCart,removeFromCart,updateQuantity,clearCart},children:children});}", "map": {"version": 3, "names": ["React", "createContext", "useState", "jsx", "_jsx", "CartContext", "CartProvider", "_ref", "children", "cart", "setCart", "addToCart", "product", "prev", "some", "item", "_id", "map", "_objectSpread", "quantity", "removeFromCart", "productId", "filter", "updateQuantity", "newQuantity", "clearCart", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/CartContext.jsx"], "sourcesContent": ["import React, { createContext, useState } from 'react';\r\n\r\nexport const CartContext = createContext();\r\n\r\nexport function CartProvider({ children }) {\r\n  const [cart, setCart] = useState([]);\r\n\r\n  function addToCart(product) {\r\n    setCart(prev =>\r\n      prev.some(item => item._id === product._id)\r\n        ? prev.map(item =>\r\n            item._id === product._id ? { ...item, quantity: item.quantity + 1 } : item)\r\n        : [...prev, { ...product, quantity: 1 }]\r\n    );\r\n  }\r\n\r\n  function removeFromCart(productId) {\r\n    setCart(prev => prev.filter(item => item._id !== productId));\r\n  }\r\n\r\n  function updateQuantity(productId, newQuantity) {\r\n    setCart(prev => prev.map(item =>\r\n      item._id === productId\r\n        ? { ...item, quantity: newQuantity }\r\n        : item\r\n    ));\r\n  }\r\n\r\n  function clearCart() {\r\n    setCart([]);\r\n  }\r\n\r\n  return (\r\n    <CartContext.Provider value={{ cart, addToCart, removeFromCart, updateQuantity, clearCart }}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n}"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEvD,MAAO,MAAM,CAAAC,WAAW,cAAGJ,aAAa,CAAC,CAAC,CAE1C,MAAO,SAAS,CAAAK,YAAYA,CAAAC,IAAA,CAAe,IAAd,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGR,QAAQ,CAAC,EAAE,CAAC,CAEpC,QAAS,CAAAS,SAASA,CAACC,OAAO,CAAE,CAC1BF,OAAO,CAACG,IAAI,EACVA,IAAI,CAACC,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,GAAG,GAAKJ,OAAO,CAACI,GAAG,CAAC,CACvCH,IAAI,CAACI,GAAG,CAACF,IAAI,EACXA,IAAI,CAACC,GAAG,GAAKJ,OAAO,CAACI,GAAG,CAAAE,aAAA,CAAAA,aAAA,IAAQH,IAAI,MAAEI,QAAQ,CAAEJ,IAAI,CAACI,QAAQ,CAAG,CAAC,GAAKJ,IAAI,CAAC,CAC7E,CAAC,GAAGF,IAAI,CAAAK,aAAA,CAAAA,aAAA,IAAON,OAAO,MAAEO,QAAQ,CAAE,CAAC,GACzC,CAAC,CACH,CAEA,QAAS,CAAAC,cAAcA,CAACC,SAAS,CAAE,CACjCX,OAAO,CAACG,IAAI,EAAIA,IAAI,CAACS,MAAM,CAACP,IAAI,EAAIA,IAAI,CAACC,GAAG,GAAKK,SAAS,CAAC,CAAC,CAC9D,CAEA,QAAS,CAAAE,cAAcA,CAACF,SAAS,CAAEG,WAAW,CAAE,CAC9Cd,OAAO,CAACG,IAAI,EAAIA,IAAI,CAACI,GAAG,CAACF,IAAI,EAC3BA,IAAI,CAACC,GAAG,GAAKK,SAAS,CAAAH,aAAA,CAAAA,aAAA,IACbH,IAAI,MAAEI,QAAQ,CAAEK,WAAW,GAChCT,IACN,CAAC,CAAC,CACJ,CAEA,QAAS,CAAAU,SAASA,CAAA,CAAG,CACnBf,OAAO,CAAC,EAAE,CAAC,CACb,CAEA,mBACEN,IAAA,CAACC,WAAW,CAACqB,QAAQ,EAACC,KAAK,CAAE,CAAElB,IAAI,CAAEE,SAAS,CAAES,cAAc,CAAEG,cAAc,CAAEE,SAAU,CAAE,CAAAjB,QAAA,CACzFA,QAAQ,CACW,CAAC,CAE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}