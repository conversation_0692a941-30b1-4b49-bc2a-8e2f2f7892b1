"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _default;
exports.resolveFSPath = resolveFSPath;
function _default(moduleName, dirname, absoluteRuntime) {
  if (absoluteRuntime === false) return moduleName;
  resolveFSPath();
}
function resolveFSPath() {
  throw new Error("The 'absoluteRuntime' option is not supported when using @babel/standalone.");
}

//# sourceMappingURL=browser.js.map
