{"version": 3, "names": ["normalizeESLintConfig", "options", "babelOptions", "ecmaVersion", "sourceType", "requireConfigFile", "otherOptions", "_objectWithoutPropertiesLoose", "_excluded", "Object", "assign", "cwd", "process"], "sources": ["../src/configuration.cts"], "sourcesContent": ["import type { Options } from \"./types.cts\";\n\nexport = function normalizeESLintConfig(options: any) {\n  const {\n    babelOptions = {},\n    // ESLint sets ecmaVersion: undefined when ecmaVersion is not set in the config.\n    ecmaVersion = \"latest\",\n    sourceType = \"module\",\n    requireConfigFile = true,\n    ...otherOptions\n  } = options;\n\n  return {\n    babelOptions: { cwd: process.cwd(), ...babelOptions },\n    ecmaVersion: ecmaVersion === \"latest\" ? 1e8 : ecmaVersion,\n    sourceType,\n    requireConfigFile,\n    ...otherOptions,\n  } as Options;\n};\n"], "mappings": ";;;;iBAES,SAASA,qBAAqBA,CAACC,OAAY,EAAE;EACpD,MAAM;MACJC,YAAY,GAAG,CAAC,CAAC;MAEjBC,WAAW,GAAG,QAAQ;MACtBC,UAAU,GAAG,QAAQ;MACrBC,iBAAiB,GAAG;IAEtB,CAAC,GAAGJ,OAAO;IADNK,YAAY,GAAAC,6BAAA,CACbN,OAAO,EAAAO,SAAA;EAEX,OAAAC,MAAA,CAAAC,MAAA;IACER,YAAY,EAAAO,MAAA,CAAAC,MAAA;MAAIC,GAAG,EAAEC,OAAO,CAACD,GAAG,CAAC;IAAC,GAAKT,YAAY,CAAE;IACrDC,WAAW,EAAEA,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAGA,WAAW;IACzDC,UAAU;IACVC;EAAiB,GACdC,YAAY;AAEnB,CAAC", "ignoreList": []}