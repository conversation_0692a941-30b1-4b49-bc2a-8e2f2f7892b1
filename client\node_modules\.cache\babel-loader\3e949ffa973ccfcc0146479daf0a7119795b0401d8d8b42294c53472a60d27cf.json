{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useRef,useCallback}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ImageUpload(_ref){var _imageRef$current,_imageRef$current2;let{currentImage,onImageChange,onClose}=_ref;const[selectedImage,setSelectedImage]=useState(null);const[previewUrl,setPreviewUrl]=useState(currentImage||null);const[isDragging,setIsDragging]=useState(false);const[cropArea,setCropArea]=useState({x:0,y:0,width:200,height:200});const[isResizing,setIsResizing]=useState(false);const fileInputRef=useRef(null);const imageRef=useRef(null);const cropRef=useRef(null);const handleFileSelect=file=>{if(file&&file.type.startsWith('image/')){const reader=new FileReader();reader.onload=e=>{setSelectedImage(file);setPreviewUrl(e.target.result);// Reset crop area when new image is loaded\nsetCropArea({x:50,y:50,width:200,height:200});};reader.readAsDataURL(file);}};const handleFileInputChange=e=>{const file=e.target.files[0];handleFileSelect(file);};const handleDragOver=useCallback(e=>{e.preventDefault();setIsDragging(true);},[]);const handleDragLeave=useCallback(e=>{e.preventDefault();setIsDragging(false);},[]);const handleDrop=useCallback(e=>{e.preventDefault();setIsDragging(false);const file=e.dataTransfer.files[0];handleFileSelect(file);},[]);const handleCropMouseDown=e=>{e.preventDefault();setIsResizing(true);const startX=e.clientX;const startY=e.clientY;const startCrop=_objectSpread({},cropArea);const handleMouseMove=e=>{const deltaX=e.clientX-startX;const deltaY=e.clientY-startY;if(e.target.classList.contains('crop-handle')){// Resize crop area\nconst newWidth=Math.max(100,startCrop.width+deltaX);const newHeight=Math.max(100,startCrop.height+deltaY);setCropArea(prev=>_objectSpread(_objectSpread({},prev),{},{width:newWidth,height:newHeight}));}else{// Move crop area\nsetCropArea(prev=>_objectSpread(_objectSpread({},prev),{},{x:Math.max(0,startCrop.x+deltaX),y:Math.max(0,startCrop.y+deltaY)}));}};const handleMouseUp=()=>{setIsResizing(false);document.removeEventListener('mousemove',handleMouseMove);document.removeEventListener('mouseup',handleMouseUp);};document.addEventListener('mousemove',handleMouseMove);document.addEventListener('mouseup',handleMouseUp);};const cropImage=()=>{if(!previewUrl||!imageRef.current)return null;const canvas=document.createElement('canvas');const ctx=canvas.getContext('2d');const img=imageRef.current;// Set canvas size to crop area\ncanvas.width=cropArea.width;canvas.height=cropArea.height;// Calculate scale factor\nconst scaleX=img.naturalWidth/img.offsetWidth;const scaleY=img.naturalHeight/img.offsetHeight;// Draw cropped image\nctx.drawImage(img,cropArea.x*scaleX,cropArea.y*scaleY,cropArea.width*scaleX,cropArea.height*scaleY,0,0,cropArea.width,cropArea.height);return canvas.toDataURL('image/jpeg',0.9);};const handleSave=()=>{const croppedImage=cropImage();if(croppedImage){onImageChange(croppedImage);onClose();}};const handleRemoveImage=()=>{onImageChange(null);onClose();};const predefinedAvatars=['https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face','https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face','https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face','https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face','https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face','https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'];return/*#__PURE__*/_jsxs(\"div\",{className:\"image-upload-modal\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:onClose}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-camera\"}),\"Edit Profile Picture\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"close-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-times\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"upload-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"upload-tabs\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"tab active\",children:\"Upload\"}),/*#__PURE__*/_jsx(\"button\",{className:\"tab\",children:\"Choose Avatar\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"upload-area\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"drop-zone \".concat(isDragging?'dragging':''),onDragOver:handleDragOver,onDragLeave:handleDragLeave,onDrop:handleDrop,onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-cloud-upload-alt\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Drag & drop an image here or click to browse\"}),/*#__PURE__*/_jsx(\"small\",{children:\"Supports: JPG, PNG, GIF (Max 5MB)\"})]}),/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\"image/*\",onChange:handleFileInputChange,style:{display:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"predefined-avatars\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Or choose a predefined avatar:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"avatar-grid\",children:predefinedAvatars.map((avatar,index)=>/*#__PURE__*/_jsx(\"button\",{className:\"avatar-option\",onClick:()=>{onImageChange(avatar);onClose();},children:/*#__PURE__*/_jsx(\"img\",{src:avatar,alt:\"Avatar \".concat(index+1)})},index))})]})]}),previewUrl&&/*#__PURE__*/_jsxs(\"div\",{className:\"preview-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Crop your image:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"image-preview\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"crop-container\",children:[/*#__PURE__*/_jsx(\"img\",{ref:imageRef,src:previewUrl,alt:\"Preview\",className:\"preview-image\"}),/*#__PURE__*/_jsx(\"div\",{ref:cropRef,className:\"crop-overlay\",style:{left:cropArea.x,top:cropArea.y,width:cropArea.width,height:cropArea.height},onMouseDown:handleCropMouseDown,children:/*#__PURE__*/_jsx(\"div\",{className:\"crop-handle crop-handle-br\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"crop-preview\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Preview:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"avatar-preview\",children:previewUrl&&/*#__PURE__*/_jsx(\"div\",{className:\"cropped-preview\",style:{backgroundImage:\"url(\".concat(previewUrl,\")\"),backgroundPosition:\"-\".concat(cropArea.x,\"px -\").concat(cropArea.y,\"px\"),backgroundSize:\"\".concat(((_imageRef$current=imageRef.current)===null||_imageRef$current===void 0?void 0:_imageRef$current.offsetWidth)||300,\"px \").concat(((_imageRef$current2=imageRef.current)===null||_imageRef$current2===void 0?void 0:_imageRef$current2.offsetHeight)||300,\"px\")}})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-actions\",children:[currentImage&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleRemoveImage,className:\"remove-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trash\"}),\"Remove Photo\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"cancel-btn\",children:\"Cancel\"}),previewUrl&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleSave,className:\"save-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-save\"}),\"Save Photo\"]})]})]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .image-upload-modal {\\n          position: fixed;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          z-index: 10000;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          padding: 1rem;\\n        }\\n\\n        .modal-overlay {\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background: rgba(0, 0, 0, 0.7);\\n          backdrop-filter: blur(4px);\\n        }\\n\\n        .modal-content {\\n          position: relative;\\n          background: white;\\n          border-radius: 1rem;\\n          width: 100%;\\n          max-width: 800px;\\n          max-height: 90vh;\\n          overflow-y: auto;\\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n        }\\n\\n        .modal-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          padding: 2rem 2rem 1rem 2rem;\\n          border-bottom: 1px solid var(--border-color);\\n        }\\n\\n        .modal-header h3 {\\n          margin: 0;\\n          font-size: 1.5rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n        }\\n\\n        .close-btn {\\n          background: none;\\n          border: none;\\n          font-size: 1.25rem;\\n          color: var(--text-secondary);\\n          cursor: pointer;\\n          padding: 0.5rem;\\n          border-radius: 0.375rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .close-btn:hover {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n        }\\n\\n        .upload-section {\\n          padding: 2rem;\\n        }\\n\\n        .upload-tabs {\\n          display: flex;\\n          gap: 0.5rem;\\n          margin-bottom: 2rem;\\n        }\\n\\n        .tab {\\n          background: var(--bg-secondary);\\n          border: 1px solid var(--border-color);\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          transition: all 0.2s;\\n        }\\n\\n        .tab.active {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .drop-zone {\\n          border: 2px dashed var(--border-color);\\n          border-radius: 1rem;\\n          padding: 3rem 2rem;\\n          text-align: center;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          margin-bottom: 2rem;\\n        }\\n\\n        .drop-zone:hover,\\n        .drop-zone.dragging {\\n          border-color: var(--primary-color);\\n          background: var(--bg-secondary);\\n        }\\n\\n        .drop-zone i {\\n          font-size: 3rem;\\n          color: var(--primary-color);\\n          margin-bottom: 1rem;\\n        }\\n\\n        .drop-zone p {\\n          margin: 0 0 0.5rem 0;\\n          font-size: 1.125rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .drop-zone small {\\n          color: var(--text-secondary);\\n        }\\n\\n        .predefined-avatars h4 {\\n          margin: 0 0 1rem 0;\\n          font-size: 1rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .avatar-grid {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\\n          gap: 1rem;\\n        }\\n\\n        .avatar-option {\\n          background: none;\\n          border: 2px solid var(--border-color);\\n          border-radius: 50%;\\n          padding: 4px;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          width: 80px;\\n          height: 80px;\\n        }\\n\\n        .avatar-option:hover {\\n          border-color: var(--primary-color);\\n          transform: scale(1.05);\\n        }\\n\\n        .avatar-option img {\\n          width: 100%;\\n          height: 100%;\\n          border-radius: 50%;\\n          object-fit: cover;\\n        }\\n\\n        .preview-section {\\n          padding: 0 2rem 2rem 2rem;\\n          border-top: 1px solid var(--border-color);\\n        }\\n\\n        .preview-section h4 {\\n          margin: 2rem 0 1rem 0;\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .image-preview {\\n          display: grid;\\n          grid-template-columns: 1fr 200px;\\n          gap: 2rem;\\n          align-items: start;\\n        }\\n\\n        .crop-container {\\n          position: relative;\\n          display: inline-block;\\n          max-width: 100%;\\n        }\\n\\n        .preview-image {\\n          max-width: 100%;\\n          max-height: 400px;\\n          border-radius: 0.5rem;\\n          user-select: none;\\n        }\\n\\n        .crop-overlay {\\n          position: absolute;\\n          border: 2px solid var(--primary-color);\\n          background: rgba(37, 99, 235, 0.1);\\n          cursor: move;\\n          min-width: 100px;\\n          min-height: 100px;\\n        }\\n\\n        .crop-handle {\\n          position: absolute;\\n          width: 12px;\\n          height: 12px;\\n          background: var(--primary-color);\\n          border: 2px solid white;\\n          border-radius: 50%;\\n        }\\n\\n        .crop-handle-br {\\n          bottom: -6px;\\n          right: -6px;\\n          cursor: se-resize;\\n        }\\n\\n        .crop-preview h5 {\\n          margin: 0 0 1rem 0;\\n          font-size: 1rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .avatar-preview {\\n          display: flex;\\n          justify-content: center;\\n        }\\n\\n        .cropped-preview {\\n          width: 150px;\\n          height: 150px;\\n          border-radius: 50%;\\n          border: 3px solid var(--border-color);\\n          background-repeat: no-repeat;\\n          overflow: hidden;\\n        }\\n\\n        .modal-actions {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          padding: 1.5rem 2rem;\\n          border-top: 1px solid var(--border-color);\\n          background: var(--bg-secondary);\\n        }\\n\\n        .remove-btn {\\n          background: #ef4444;\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .remove-btn:hover {\\n          background: #dc2626;\\n        }\\n\\n        .action-buttons {\\n          display: flex;\\n          gap: 1rem;\\n        }\\n\\n        .cancel-btn {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          border: 1px solid var(--border-color);\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n        }\\n\\n        .save-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .save-btn:hover {\\n          background: var(--primary-dark);\\n        }\\n\\n        @media (max-width: 768px) {\\n          .modal-content {\\n            margin: 1rem;\\n            max-height: calc(100vh - 2rem);\\n          }\\n\\n          .image-preview {\\n            grid-template-columns: 1fr;\\n            gap: 1rem;\\n          }\\n\\n          .modal-actions {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: stretch;\\n          }\\n\\n          .action-buttons {\\n            justify-content: center;\\n          }\\n\\n          .avatar-grid {\\n            grid-template-columns: repeat(4, 1fr);\\n          }\\n        }\\n      \"})]});}export default ImageUpload;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "jsx", "_jsx", "jsxs", "_jsxs", "ImageUpload", "_ref", "_imageRef$current", "_imageRef$current2", "currentImage", "onImageChange", "onClose", "selectedImage", "setSelectedImage", "previewUrl", "setPreviewUrl", "isDragging", "setIsDragging", "cropArea", "setCropArea", "x", "y", "width", "height", "isResizing", "setIsResizing", "fileInputRef", "imageRef", "cropRef", "handleFileSelect", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "handleFileInputChange", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleCropMouseDown", "startX", "clientX", "startY", "clientY", "startCrop", "_objectSpread", "handleMouseMove", "deltaX", "deltaY", "classList", "contains", "newWidth", "Math", "max", "newHeight", "prev", "handleMouseUp", "document", "removeEventListener", "addEventListener", "cropImage", "current", "canvas", "createElement", "ctx", "getContext", "img", "scaleX", "naturalWidth", "offsetWidth", "scaleY", "naturalHeight", "offsetHeight", "drawImage", "toDataURL", "handleSave", "croppedImage", "handleRemoveImage", "predefinedAvatars", "className", "children", "onClick", "concat", "onDragOver", "onDragLeave", "onDrop", "_fileInputRef$current", "click", "ref", "accept", "onChange", "style", "display", "map", "avatar", "index", "src", "alt", "left", "top", "onMouseDown", "backgroundImage", "backgroundPosition", "backgroundSize"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ImageUpload.jsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\n\nfunction ImageUpload({ currentImage, onImageChange, onClose }) {\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(currentImage || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [cropArea, setCropArea] = useState({ x: 0, y: 0, width: 200, height: 200 });\n  const [isResizing, setIsResizing] = useState(false);\n  const fileInputRef = useRef(null);\n  const imageRef = useRef(null);\n  const cropRef = useRef(null);\n\n  const handleFileSelect = (file) => {\n    if (file && file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setSelectedImage(file);\n        setPreviewUrl(e.target.result);\n        // Reset crop area when new image is loaded\n        setCropArea({ x: 50, y: 50, width: 200, height: 200 });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleFileInputChange = (e) => {\n    const file = e.target.files[0];\n    handleFileSelect(file);\n  };\n\n  const handleDragOver = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const handleDrop = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(false);\n    const file = e.dataTransfer.files[0];\n    handleFileSelect(file);\n  }, []);\n\n  const handleCropMouseDown = (e) => {\n    e.preventDefault();\n    setIsResizing(true);\n    \n    const startX = e.clientX;\n    const startY = e.clientY;\n    const startCrop = { ...cropArea };\n\n    const handleMouseMove = (e) => {\n      const deltaX = e.clientX - startX;\n      const deltaY = e.clientY - startY;\n      \n      if (e.target.classList.contains('crop-handle')) {\n        // Resize crop area\n        const newWidth = Math.max(100, startCrop.width + deltaX);\n        const newHeight = Math.max(100, startCrop.height + deltaY);\n        setCropArea(prev => ({\n          ...prev,\n          width: newWidth,\n          height: newHeight\n        }));\n      } else {\n        // Move crop area\n        setCropArea(prev => ({\n          ...prev,\n          x: Math.max(0, startCrop.x + deltaX),\n          y: Math.max(0, startCrop.y + deltaY)\n        }));\n      }\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  const cropImage = () => {\n    if (!previewUrl || !imageRef.current) return null;\n\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = imageRef.current;\n    \n    // Set canvas size to crop area\n    canvas.width = cropArea.width;\n    canvas.height = cropArea.height;\n    \n    // Calculate scale factor\n    const scaleX = img.naturalWidth / img.offsetWidth;\n    const scaleY = img.naturalHeight / img.offsetHeight;\n    \n    // Draw cropped image\n    ctx.drawImage(\n      img,\n      cropArea.x * scaleX,\n      cropArea.y * scaleY,\n      cropArea.width * scaleX,\n      cropArea.height * scaleY,\n      0,\n      0,\n      cropArea.width,\n      cropArea.height\n    );\n    \n    return canvas.toDataURL('image/jpeg', 0.9);\n  };\n\n  const handleSave = () => {\n    const croppedImage = cropImage();\n    if (croppedImage) {\n      onImageChange(croppedImage);\n      onClose();\n    }\n  };\n\n  const handleRemoveImage = () => {\n    onImageChange(null);\n    onClose();\n  };\n\n  const predefinedAvatars = [\n    'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'\n  ];\n\n  return (\n    <div className=\"image-upload-modal\">\n      <div className=\"modal-overlay\" onClick={onClose}></div>\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h3>\n            <i className=\"fas fa-camera\"></i>\n            Edit Profile Picture\n          </h3>\n          <button onClick={onClose} className=\"close-btn\">\n            <i className=\"fas fa-times\"></i>\n          </button>\n        </div>\n\n        <div className=\"upload-section\">\n          <div className=\"upload-tabs\">\n            <button className=\"tab active\">Upload</button>\n            <button className=\"tab\">Choose Avatar</button>\n          </div>\n\n          <div className=\"upload-area\">\n            <div \n              className={`drop-zone ${isDragging ? 'dragging' : ''}`}\n              onDragOver={handleDragOver}\n              onDragLeave={handleDragLeave}\n              onDrop={handleDrop}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <i className=\"fas fa-cloud-upload-alt\"></i>\n              <p>Drag & drop an image here or click to browse</p>\n              <small>Supports: JPG, PNG, GIF (Max 5MB)</small>\n            </div>\n            \n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileInputChange}\n              style={{ display: 'none' }}\n            />\n          </div>\n\n          <div className=\"predefined-avatars\">\n            <h4>Or choose a predefined avatar:</h4>\n            <div className=\"avatar-grid\">\n              {predefinedAvatars.map((avatar, index) => (\n                <button\n                  key={index}\n                  className=\"avatar-option\"\n                  onClick={() => {\n                    onImageChange(avatar);\n                    onClose();\n                  }}\n                >\n                  <img src={avatar} alt={`Avatar ${index + 1}`} />\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {previewUrl && (\n          <div className=\"preview-section\">\n            <h4>Crop your image:</h4>\n            <div className=\"image-preview\">\n              <div className=\"crop-container\">\n                <img\n                  ref={imageRef}\n                  src={previewUrl}\n                  alt=\"Preview\"\n                  className=\"preview-image\"\n                />\n                <div\n                  ref={cropRef}\n                  className=\"crop-overlay\"\n                  style={{\n                    left: cropArea.x,\n                    top: cropArea.y,\n                    width: cropArea.width,\n                    height: cropArea.height\n                  }}\n                  onMouseDown={handleCropMouseDown}\n                >\n                  <div className=\"crop-handle crop-handle-br\"></div>\n                </div>\n              </div>\n              \n              <div className=\"crop-preview\">\n                <h5>Preview:</h5>\n                <div className=\"avatar-preview\">\n                  {previewUrl && (\n                    <div \n                      className=\"cropped-preview\"\n                      style={{\n                        backgroundImage: `url(${previewUrl})`,\n                        backgroundPosition: `-${cropArea.x}px -${cropArea.y}px`,\n                        backgroundSize: `${imageRef.current?.offsetWidth || 300}px ${imageRef.current?.offsetHeight || 300}px`\n                      }}\n                    ></div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"modal-actions\">\n          {currentImage && (\n            <button onClick={handleRemoveImage} className=\"remove-btn\">\n              <i className=\"fas fa-trash\"></i>\n              Remove Photo\n            </button>\n          )}\n          <div className=\"action-buttons\">\n            <button onClick={onClose} className=\"cancel-btn\">\n              Cancel\n            </button>\n            {previewUrl && (\n              <button onClick={handleSave} className=\"save-btn\">\n                <i className=\"fas fa-save\"></i>\n                Save Photo\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .image-upload-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 10000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.7);\n          backdrop-filter: blur(4px);\n        }\n\n        .modal-content {\n          position: relative;\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 800px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .upload-section {\n          padding: 2rem;\n        }\n\n        .upload-tabs {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 2rem;\n        }\n\n        .tab {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .tab.active {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .drop-zone {\n          border: 2px dashed var(--border-color);\n          border-radius: 1rem;\n          padding: 3rem 2rem;\n          text-align: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-bottom: 2rem;\n        }\n\n        .drop-zone:hover,\n        .drop-zone.dragging {\n          border-color: var(--primary-color);\n          background: var(--bg-secondary);\n        }\n\n        .drop-zone i {\n          font-size: 3rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .drop-zone p {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.125rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .drop-zone small {\n          color: var(--text-secondary);\n        }\n\n        .predefined-avatars h4 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\n          gap: 1rem;\n        }\n\n        .avatar-option {\n          background: none;\n          border: 2px solid var(--border-color);\n          border-radius: 50%;\n          padding: 4px;\n          cursor: pointer;\n          transition: all 0.2s;\n          width: 80px;\n          height: 80px;\n        }\n\n        .avatar-option:hover {\n          border-color: var(--primary-color);\n          transform: scale(1.05);\n        }\n\n        .avatar-option img {\n          width: 100%;\n          height: 100%;\n          border-radius: 50%;\n          object-fit: cover;\n        }\n\n        .preview-section {\n          padding: 0 2rem 2rem 2rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .preview-section h4 {\n          margin: 2rem 0 1rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .image-preview {\n          display: grid;\n          grid-template-columns: 1fr 200px;\n          gap: 2rem;\n          align-items: start;\n        }\n\n        .crop-container {\n          position: relative;\n          display: inline-block;\n          max-width: 100%;\n        }\n\n        .preview-image {\n          max-width: 100%;\n          max-height: 400px;\n          border-radius: 0.5rem;\n          user-select: none;\n        }\n\n        .crop-overlay {\n          position: absolute;\n          border: 2px solid var(--primary-color);\n          background: rgba(37, 99, 235, 0.1);\n          cursor: move;\n          min-width: 100px;\n          min-height: 100px;\n        }\n\n        .crop-handle {\n          position: absolute;\n          width: 12px;\n          height: 12px;\n          background: var(--primary-color);\n          border: 2px solid white;\n          border-radius: 50%;\n        }\n\n        .crop-handle-br {\n          bottom: -6px;\n          right: -6px;\n          cursor: se-resize;\n        }\n\n        .crop-preview h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-preview {\n          display: flex;\n          justify-content: center;\n        }\n\n        .cropped-preview {\n          width: 150px;\n          height: 150px;\n          border-radius: 50%;\n          border: 3px solid var(--border-color);\n          background-repeat: no-repeat;\n          overflow: hidden;\n        }\n\n        .modal-actions {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem 2rem;\n          border-top: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .remove-btn {\n          background: #ef4444;\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .remove-btn:hover {\n          background: #dc2626;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .save-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .modal-content {\n            margin: 1rem;\n            max-height: calc(100vh - 2rem);\n          }\n\n          .image-preview {\n            grid-template-columns: 1fr;\n            gap: 1rem;\n          }\n\n          .modal-actions {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: stretch;\n          }\n\n          .action-buttons {\n            justify-content: center;\n          }\n\n          .avatar-grid {\n            grid-template-columns: repeat(4, 1fr);\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ImageUpload;\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7D,QAAS,CAAAC,WAAWA,CAAAC,IAAA,CAA2C,KAAAC,iBAAA,CAAAC,kBAAA,IAA1C,CAAEC,YAAY,CAAEC,aAAa,CAAEC,OAAQ,CAAC,CAAAL,IAAA,CAC3D,KAAM,CAACM,aAAa,CAAEC,gBAAgB,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAACW,YAAY,EAAI,IAAI,CAAC,CAClE,KAAM,CAACO,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACoB,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAC,CAAEsB,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,KAAK,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CAAC,CACjF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAA4B,YAAY,CAAG3B,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAA4B,QAAQ,CAAG5B,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,CAAA6B,OAAO,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAE5B,KAAM,CAAA8B,gBAAgB,CAAIC,IAAI,EAAK,CACjC,GAAIA,IAAI,EAAIA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CAC1C,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAK,CACrBvB,gBAAgB,CAACiB,IAAI,CAAC,CACtBf,aAAa,CAACqB,CAAC,CAACC,MAAM,CAACC,MAAM,CAAC,CAC9B;AACAnB,WAAW,CAAC,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,KAAK,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CAAC,CACxD,CAAC,CACDU,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAU,qBAAqB,CAAIJ,CAAC,EAAK,CACnC,KAAM,CAAAN,IAAI,CAAGM,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAC9BZ,gBAAgB,CAACC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAY,cAAc,CAAG1C,WAAW,CAAEoC,CAAC,EAAK,CACxCA,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB1B,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA2B,eAAe,CAAG5C,WAAW,CAAEoC,CAAC,EAAK,CACzCA,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB1B,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA4B,UAAU,CAAG7C,WAAW,CAAEoC,CAAC,EAAK,CACpCA,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB1B,aAAa,CAAC,KAAK,CAAC,CACpB,KAAM,CAAAa,IAAI,CAAGM,CAAC,CAACU,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC,CACpCZ,gBAAgB,CAACC,IAAI,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAiB,mBAAmB,CAAIX,CAAC,EAAK,CACjCA,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBlB,aAAa,CAAC,IAAI,CAAC,CAEnB,KAAM,CAAAuB,MAAM,CAAGZ,CAAC,CAACa,OAAO,CACxB,KAAM,CAAAC,MAAM,CAAGd,CAAC,CAACe,OAAO,CACxB,KAAM,CAAAC,SAAS,CAAAC,aAAA,IAAQnC,QAAQ,CAAE,CAEjC,KAAM,CAAAoC,eAAe,CAAIlB,CAAC,EAAK,CAC7B,KAAM,CAAAmB,MAAM,CAAGnB,CAAC,CAACa,OAAO,CAAGD,MAAM,CACjC,KAAM,CAAAQ,MAAM,CAAGpB,CAAC,CAACe,OAAO,CAAGD,MAAM,CAEjC,GAAId,CAAC,CAACC,MAAM,CAACoB,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAE,CAC9C;AACA,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,CAAET,SAAS,CAAC9B,KAAK,CAAGiC,MAAM,CAAC,CACxD,KAAM,CAAAO,SAAS,CAAGF,IAAI,CAACC,GAAG,CAAC,GAAG,CAAET,SAAS,CAAC7B,MAAM,CAAGiC,MAAM,CAAC,CAC1DrC,WAAW,CAAC4C,IAAI,EAAAV,aAAA,CAAAA,aAAA,IACXU,IAAI,MACPzC,KAAK,CAAEqC,QAAQ,CACfpC,MAAM,CAAEuC,SAAS,EACjB,CAAC,CACL,CAAC,IAAM,CACL;AACA3C,WAAW,CAAC4C,IAAI,EAAAV,aAAA,CAAAA,aAAA,IACXU,IAAI,MACP3C,CAAC,CAAEwC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAET,SAAS,CAAChC,CAAC,CAAGmC,MAAM,CAAC,CACpClC,CAAC,CAAEuC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAET,SAAS,CAAC/B,CAAC,CAAGmC,MAAM,CAAC,EACpC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAGA,CAAA,GAAM,CAC1BvC,aAAa,CAAC,KAAK,CAAC,CACpBwC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,CAAEZ,eAAe,CAAC,CAC1DW,QAAQ,CAACC,mBAAmB,CAAC,SAAS,CAAEF,aAAa,CAAC,CACxD,CAAC,CAEDC,QAAQ,CAACE,gBAAgB,CAAC,WAAW,CAAEb,eAAe,CAAC,CACvDW,QAAQ,CAACE,gBAAgB,CAAC,SAAS,CAAEH,aAAa,CAAC,CACrD,CAAC,CAED,KAAM,CAAAI,SAAS,CAAGA,CAAA,GAAM,CACtB,GAAI,CAACtD,UAAU,EAAI,CAACa,QAAQ,CAAC0C,OAAO,CAAE,MAAO,KAAI,CAEjD,KAAM,CAAAC,MAAM,CAAGL,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC,CAC/C,KAAM,CAAAC,GAAG,CAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC,CACnC,KAAM,CAAAC,GAAG,CAAG/C,QAAQ,CAAC0C,OAAO,CAE5B;AACAC,MAAM,CAAChD,KAAK,CAAGJ,QAAQ,CAACI,KAAK,CAC7BgD,MAAM,CAAC/C,MAAM,CAAGL,QAAQ,CAACK,MAAM,CAE/B;AACA,KAAM,CAAAoD,MAAM,CAAGD,GAAG,CAACE,YAAY,CAAGF,GAAG,CAACG,WAAW,CACjD,KAAM,CAAAC,MAAM,CAAGJ,GAAG,CAACK,aAAa,CAAGL,GAAG,CAACM,YAAY,CAEnD;AACAR,GAAG,CAACS,SAAS,CACXP,GAAG,CACHxD,QAAQ,CAACE,CAAC,CAAGuD,MAAM,CACnBzD,QAAQ,CAACG,CAAC,CAAGyD,MAAM,CACnB5D,QAAQ,CAACI,KAAK,CAAGqD,MAAM,CACvBzD,QAAQ,CAACK,MAAM,CAAGuD,MAAM,CACxB,CAAC,CACD,CAAC,CACD5D,QAAQ,CAACI,KAAK,CACdJ,QAAQ,CAACK,MACX,CAAC,CAED,MAAO,CAAA+C,MAAM,CAACY,SAAS,CAAC,YAAY,CAAE,GAAG,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAC,YAAY,CAAGhB,SAAS,CAAC,CAAC,CAChC,GAAIgB,YAAY,CAAE,CAChB1E,aAAa,CAAC0E,YAAY,CAAC,CAC3BzE,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,KAAM,CAAA0E,iBAAiB,CAAGA,CAAA,GAAM,CAC9B3E,aAAa,CAAC,IAAI,CAAC,CACnBC,OAAO,CAAC,CAAC,CACX,CAAC,CAED,KAAM,CAAA2E,iBAAiB,CAAG,CACxB,6FAA6F,CAC7F,6FAA6F,CAC7F,6FAA6F,CAC7F,6FAA6F,CAC7F,6FAA6F,CAC7F,6FAA6F,CAC9F,CAED,mBACElF,KAAA,QAAKmF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCtF,IAAA,QAAKqF,SAAS,CAAC,eAAe,CAACE,OAAO,CAAE9E,OAAQ,CAAM,CAAC,cACvDP,KAAA,QAAKmF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpF,KAAA,QAAKmF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BpF,KAAA,OAAAoF,QAAA,eACEtF,IAAA,MAAGqF,SAAS,CAAC,eAAe,CAAI,CAAC,uBAEnC,EAAI,CAAC,cACLrF,IAAA,WAAQuF,OAAO,CAAE9E,OAAQ,CAAC4E,SAAS,CAAC,WAAW,CAAAC,QAAA,cAC7CtF,IAAA,MAAGqF,SAAS,CAAC,cAAc,CAAI,CAAC,CAC1B,CAAC,EACN,CAAC,cAENnF,KAAA,QAAKmF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BpF,KAAA,QAAKmF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BtF,IAAA,WAAQqF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,cAC9CtF,IAAA,WAAQqF,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,eAAa,CAAQ,CAAC,EAC3C,CAAC,cAENpF,KAAA,QAAKmF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpF,KAAA,QACEmF,SAAS,cAAAG,MAAA,CAAe1E,UAAU,CAAG,UAAU,CAAG,EAAE,CAAG,CACvD2E,UAAU,CAAEjD,cAAe,CAC3BkD,WAAW,CAAEhD,eAAgB,CAC7BiD,MAAM,CAAEhD,UAAW,CACnB4C,OAAO,CAAEA,CAAA,QAAAK,qBAAA,QAAAA,qBAAA,CAAMpE,YAAY,CAAC2C,OAAO,UAAAyB,qBAAA,iBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC,EAAC,CAAAP,QAAA,eAE7CtF,IAAA,MAAGqF,SAAS,CAAC,yBAAyB,CAAI,CAAC,cAC3CrF,IAAA,MAAAsF,QAAA,CAAG,8CAA4C,CAAG,CAAC,cACnDtF,IAAA,UAAAsF,QAAA,CAAO,mCAAiC,CAAO,CAAC,EAC7C,CAAC,cAENtF,IAAA,UACE8F,GAAG,CAAEtE,YAAa,CAClBK,IAAI,CAAC,MAAM,CACXkE,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE1D,qBAAsB,CAChC2D,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,EACC,CAAC,cAENhG,KAAA,QAAKmF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCtF,IAAA,OAAAsF,QAAA,CAAI,gCAA8B,CAAI,CAAC,cACvCtF,IAAA,QAAKqF,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBF,iBAAiB,CAACe,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBACnCrG,IAAA,WAEEqF,SAAS,CAAC,eAAe,CACzBE,OAAO,CAAEA,CAAA,GAAM,CACb/E,aAAa,CAAC4F,MAAM,CAAC,CACrB3F,OAAO,CAAC,CAAC,CACX,CAAE,CAAA6E,QAAA,cAEFtF,IAAA,QAAKsG,GAAG,CAAEF,MAAO,CAACG,GAAG,WAAAf,MAAA,CAAYa,KAAK,CAAG,CAAC,CAAG,CAAE,CAAC,EAP3CA,KAQC,CACT,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAELzF,UAAU,eACTV,KAAA,QAAKmF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtF,IAAA,OAAAsF,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBpF,KAAA,QAAKmF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpF,KAAA,QAAKmF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtF,IAAA,QACE8F,GAAG,CAAErE,QAAS,CACd6E,GAAG,CAAE1F,UAAW,CAChB2F,GAAG,CAAC,SAAS,CACblB,SAAS,CAAC,eAAe,CAC1B,CAAC,cACFrF,IAAA,QACE8F,GAAG,CAAEpE,OAAQ,CACb2D,SAAS,CAAC,cAAc,CACxBY,KAAK,CAAE,CACLO,IAAI,CAAExF,QAAQ,CAACE,CAAC,CAChBuF,GAAG,CAAEzF,QAAQ,CAACG,CAAC,CACfC,KAAK,CAAEJ,QAAQ,CAACI,KAAK,CACrBC,MAAM,CAAEL,QAAQ,CAACK,MACnB,CAAE,CACFqF,WAAW,CAAE7D,mBAAoB,CAAAyC,QAAA,cAEjCtF,IAAA,QAAKqF,SAAS,CAAC,4BAA4B,CAAM,CAAC,CAC/C,CAAC,EACH,CAAC,cAENnF,KAAA,QAAKmF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtF,IAAA,OAAAsF,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBtF,IAAA,QAAKqF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B1E,UAAU,eACTZ,IAAA,QACEqF,SAAS,CAAC,iBAAiB,CAC3BY,KAAK,CAAE,CACLU,eAAe,QAAAnB,MAAA,CAAS5E,UAAU,KAAG,CACrCgG,kBAAkB,KAAApB,MAAA,CAAMxE,QAAQ,CAACE,CAAC,SAAAsE,MAAA,CAAOxE,QAAQ,CAACG,CAAC,MAAI,CACvD0F,cAAc,IAAArB,MAAA,CAAK,EAAAnF,iBAAA,CAAAoB,QAAQ,CAAC0C,OAAO,UAAA9D,iBAAA,iBAAhBA,iBAAA,CAAkBsE,WAAW,GAAI,GAAG,QAAAa,MAAA,CAAM,EAAAlF,kBAAA,CAAAmB,QAAQ,CAAC0C,OAAO,UAAA7D,kBAAA,iBAAhBA,kBAAA,CAAkBwE,YAAY,GAAI,GAAG,MACpG,CAAE,CACE,CACP,CACE,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,cAED5E,KAAA,QAAKmF,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3B/E,YAAY,eACXL,KAAA,WAAQqF,OAAO,CAAEJ,iBAAkB,CAACE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACxDtF,IAAA,MAAGqF,SAAS,CAAC,cAAc,CAAI,CAAC,eAElC,EAAQ,CACT,cACDnF,KAAA,QAAKmF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtF,IAAA,WAAQuF,OAAO,CAAE9E,OAAQ,CAAC4E,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAEjD,CAAQ,CAAC,CACR1E,UAAU,eACTV,KAAA,WAAQqF,OAAO,CAAEN,UAAW,CAACI,SAAS,CAAC,UAAU,CAAAC,QAAA,eAC/CtF,IAAA,MAAGqF,SAAS,CAAC,aAAa,CAAI,CAAC,aAEjC,EAAQ,CACT,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAENrF,IAAA,UAAOD,GAAG,MAAAuF,QAAA,s3PAqUD,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAnF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}