import React, { useContext, useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

function SignOutPage() {
  const { logout, user } = useContext(AuthContext);
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(5);
  const [isSignedOut, setIsSignedOut] = useState(false);

  useEffect(() => {
    // Auto sign out after component mounts
    const signOutTimer = setTimeout(() => {
      logout();
      setIsSignedOut(true);
    }, 1000);

    return () => clearTimeout(signOutTimer);
  }, [logout]);

  useEffect(() => {
    if (isSignedOut) {
      // Start countdown after sign out
      const countdownTimer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownTimer);
            navigate('/');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdownTimer);
    }
  }, [isSignedOut, navigate]);

  const handleGoHome = () => {
    navigate('/');
  };

  const handleSignIn = () => {
    navigate('/login');
  };

  return (
    <div className="signout-page">
      <div className="signout-container">
        <div className="signout-card">
          {!isSignedOut ? (
            <>
              <div className="signout-icon">
                <i className="fas fa-sign-out-alt"></i>
              </div>
              <h2>Signing you out...</h2>
              <p>Please wait while we securely sign you out of your account.</p>
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
            </>
          ) : (
            <>
              <div className="success-icon">
                <i className="fas fa-check-circle"></i>
              </div>
              <h2>Successfully Signed Out</h2>
              <p>Thank you for shopping with EcoStore! You have been securely signed out.</p>
              
              <div className="user-info">
                <p>Hope to see you again soon! 👋</p>
              </div>

              <div className="redirect-info">
                <p>Redirecting to home page in <span className="countdown">{countdown}</span> seconds...</p>
              </div>

              <div className="action-buttons">
                <button onClick={handleGoHome} className="btn-primary">
                  <i className="fas fa-home"></i>
                  Go to Home
                </button>
                <button onClick={handleSignIn} className="btn-secondary">
                  <i className="fas fa-sign-in-alt"></i>
                  Sign In Again
                </button>
              </div>

              <div className="quick-links">
                <h4>Quick Links</h4>
                <div className="links-grid">
                  <Link to="/products" className="quick-link">
                    <i className="fas fa-shopping-bag"></i>
                    <span>Browse Products</span>
                  </Link>
                  <Link to="/about" className="quick-link">
                    <i className="fas fa-info-circle"></i>
                    <span>About Us</span>
                  </Link>
                  <Link to="/contact" className="quick-link">
                    <i className="fas fa-envelope"></i>
                    <span>Contact</span>
                  </Link>
                  <Link to="/help" className="quick-link">
                    <i className="fas fa-question-circle"></i>
                    <span>Help</span>
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      <style jsx>{`
        .signout-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
        }

        .signout-container {
          width: 100%;
          max-width: 500px;
        }

        .signout-card {
          background: white;
          border-radius: 2rem;
          padding: 3rem;
          text-align: center;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .signout-icon {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 2rem auto;
          color: white;
          font-size: 2rem;
        }

        .success-icon {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #00b894, #00a085);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 2rem auto;
          color: white;
          font-size: 2rem;
          animation: checkmark 0.6s ease-in-out;
        }

        @keyframes checkmark {
          0% {
            transform: scale(0);
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
          }
        }

        .signout-card h2 {
          margin: 0 0 1rem 0;
          font-size: 2rem;
          font-weight: 700;
          color: var(--text-primary);
        }

        .signout-card p {
          margin: 0 0 2rem 0;
          color: var(--text-secondary);
          font-size: 1.125rem;
          line-height: 1.6;
        }

        .loading-spinner {
          margin: 2rem 0;
        }

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid var(--primary-color);
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .user-info {
          background: var(--bg-secondary);
          padding: 1.5rem;
          border-radius: 1rem;
          margin: 2rem 0;
        }

        .user-info p {
          margin: 0;
          font-size: 1.125rem;
          color: var(--text-primary);
        }

        .redirect-info {
          background: #e3f2fd;
          padding: 1rem;
          border-radius: 0.5rem;
          margin: 2rem 0;
          border-left: 4px solid var(--primary-color);
        }

        .redirect-info p {
          margin: 0;
          font-size: 0.875rem;
          color: var(--text-secondary);
        }

        .countdown {
          font-weight: 700;
          color: var(--primary-color);
          font-size: 1.125rem;
        }

        .action-buttons {
          display: flex;
          gap: 1rem;
          margin: 2rem 0;
        }

        .btn-primary, .btn-secondary {
          flex: 1;
          padding: 1rem 1.5rem;
          border-radius: 0.75rem;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          text-decoration: none;
          border: none;
        }

        .btn-primary {
          background: var(--primary-color);
          color: white;
        }

        .btn-primary:hover {
          background: var(--primary-dark);
          transform: translateY(-2px);
        }

        .btn-secondary {
          background: var(--bg-secondary);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
          background: var(--border-color);
          transform: translateY(-2px);
        }

        .quick-links {
          margin-top: 3rem;
          padding-top: 2rem;
          border-top: 1px solid var(--border-color);
        }

        .quick-links h4 {
          margin: 0 0 1.5rem 0;
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--text-primary);
        }

        .links-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 1rem;
        }

        .quick-link {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          background: var(--bg-secondary);
          border-radius: 0.75rem;
          text-decoration: none;
          color: var(--text-secondary);
          transition: all 0.2s;
          border: 1px solid var(--border-color);
        }

        .quick-link:hover {
          background: var(--primary-color);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .quick-link i {
          font-size: 1.25rem;
        }

        .quick-link span {
          font-size: 0.875rem;
          font-weight: 500;
        }

        @media (max-width: 768px) {
          .signout-page {
            padding: 1rem;
          }

          .signout-card {
            padding: 2rem;
          }

          .action-buttons {
            flex-direction: column;
          }

          .links-grid {
            grid-template-columns: 1fr;
          }

          .signout-card h2 {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
}

export default SignOutPage;
