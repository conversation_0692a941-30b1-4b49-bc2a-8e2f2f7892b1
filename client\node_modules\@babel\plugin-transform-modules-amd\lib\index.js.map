{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperModuleTransforms", "_core", "buildWrapper", "template", "statement", "buildAnonymousWrapper", "injectWrapper", "path", "wrapper", "body", "directives", "node", "amdFactoryCall", "pushContainer", "get", "amdFactoryCallArgs", "amdFactory", "length", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "allowTopLevelThis", "strict", "strictMode", "importInterop", "noInterop", "constantReexports", "assumption", "loose", "enumerableModuleMeta", "name", "pre", "file", "set", "visitor", "types", "importExpression", "state", "has", "isCallExpression", "isImport", "requireId", "resolveId", "rejectId", "scope", "generateUidIdentifier", "result", "t", "identifier", "wrapInterop", "replaceWith", "buildDynamicImport", "specifier", "expression", "ast", "cloneNode", "Program", "exit", "isModule", "REQUIRE", "amdArgs", "importNames", "push", "stringLiteral", "moduleName", "getModuleName", "opts", "meta", "headers", "rewriteModuleStatementsAndPrepareHeader", "filename", "hasExports", "exportName", "source", "metadata", "isSideEffectImport", "interop", "header", "expressionStatement", "assignmentExpression", "loc", "buildNamespaceInitStatements", "ensureStatementsHoisted", "unshiftContainer", "MODULE_NAME", "AMD_ARGUMENTS", "arrayExpression", "IMPORT_NAMES"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport {\n  buildDynamicImport,\n  isModule,\n  rewriteModuleStatementsAndPrepareHeader,\n  type RewriteModuleStatementsAndPrepareHeaderOptions,\n  hasExports,\n  isSideEffectImport,\n  buildNamespaceInitStatements,\n  ensureStatementsHoisted,\n  wrapInterop,\n  getModuleName,\n} from \"@babel/helper-module-transforms\";\nimport { template, types as t } from \"@babel/core\";\nimport type { PluginOptions } from \"@babel/helper-module-transforms\";\nimport type { NodePath, PluginPass } from \"@babel/core\";\n\nconst buildWrapper = template.statement(`\n  define(MODULE_NAME, AMD_ARGUMENTS, function(IMPORT_NAMES) {\n  })\n`);\n\nconst buildAnonymousWrapper = template.statement(`\n  define([\"require\"], function(REQUIRE) {\n  })\n`);\n\nfunction injectWrapper(\n  path: NodePath<t.Program>,\n  wrapper: t.ExpressionStatement,\n) {\n  const { body, directives } = path.node;\n  path.node.directives = [];\n  path.node.body = [];\n  const amdFactoryCall = path\n    .pushContainer(\"body\", wrapper)[0]\n    .get(\"expression\") as NodePath<t.CallExpression>;\n  const amdFactoryCallArgs = amdFactoryCall.get(\"arguments\");\n  const amdFactory = (\n    amdFactoryCallArgs[\n      amdFactoryCallArgs.length - 1\n    ] as NodePath<t.FunctionExpression>\n  ).get(\"body\");\n  amdFactory.pushContainer(\"directives\", directives);\n  amdFactory.pushContainer(\"body\", body);\n}\n\nexport interface Options extends PluginOptions {\n  allowTopLevelThis?: boolean;\n  importInterop?: RewriteModuleStatementsAndPrepareHeaderOptions[\"importInterop\"];\n  loose?: boolean;\n  noInterop?: boolean;\n  strict?: boolean;\n  strictMode?: boolean;\n}\n\ntype State = {\n  requireId?: t.Identifier;\n  resolveId?: t.Identifier;\n  rejectId?: t.Identifier;\n};\n\nexport default declare<State>((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { allowTopLevelThis, strict, strictMode, importInterop, noInterop } =\n    options;\n\n  const constantReexports =\n    api.assumption(\"constantReexports\") ?? options.loose;\n  const enumerableModuleMeta =\n    api.assumption(\"enumerableModuleMeta\") ?? options.loose;\n\n  return {\n    name: \"transform-modules-amd\",\n\n    pre() {\n      this.file.set(\"@babel/plugin-transform-modules-*\", \"amd\");\n    },\n\n    visitor: {\n      [\"CallExpression\" +\n        (api.types.importExpression ? \"|ImportExpression\" : \"\")](\n        this: State & PluginPass,\n        path: NodePath<t.CallExpression | t.ImportExpression>,\n        state: State,\n      ) {\n        if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) return;\n        if (path.isCallExpression() && !path.get(\"callee\").isImport()) return;\n\n        let { requireId, resolveId, rejectId } = state;\n        if (!requireId) {\n          requireId = path.scope.generateUidIdentifier(\"require\");\n          state.requireId = requireId;\n        }\n        if (!resolveId || !rejectId) {\n          resolveId = path.scope.generateUidIdentifier(\"resolve\");\n          rejectId = path.scope.generateUidIdentifier(\"reject\");\n          state.resolveId = resolveId;\n          state.rejectId = rejectId;\n        }\n\n        let result: t.Node = t.identifier(\"imported\");\n        if (!noInterop) {\n          result = wrapInterop(this.file.path, result, \"namespace\");\n        }\n\n        path.replaceWith(\n          buildDynamicImport(\n            path.node,\n            false,\n            false,\n            specifier => template.expression.ast`\n              new Promise((${resolveId}, ${rejectId}) =>\n                ${requireId}(\n                  [${specifier}],\n                  imported => ${t.cloneNode(resolveId)}(${result}),\n                  ${t.cloneNode(rejectId)}\n                )\n              )\n            `,\n          ),\n        );\n      },\n      Program: {\n        exit(path, { requireId }) {\n          if (!isModule(path)) {\n            if (requireId) {\n              injectWrapper(\n                path,\n                buildAnonymousWrapper({\n                  REQUIRE: t.cloneNode(requireId),\n                }) as t.ExpressionStatement,\n              );\n            }\n            return;\n          }\n\n          const amdArgs = [];\n          const importNames = [];\n          if (requireId) {\n            amdArgs.push(t.stringLiteral(\"require\"));\n            importNames.push(t.cloneNode(requireId));\n          }\n\n          let moduleName = getModuleName(this.file.opts, options);\n          // @ts-expect-error todo(flow->ts): do not reuse variables\n          if (moduleName) moduleName = t.stringLiteral(moduleName);\n\n          const { meta, headers } = rewriteModuleStatementsAndPrepareHeader(\n            path,\n            {\n              enumerableModuleMeta,\n              constantReexports,\n              strict,\n              strictMode,\n              allowTopLevelThis,\n              importInterop,\n              noInterop,\n              filename: this.file.opts.filename,\n            },\n          );\n\n          if (hasExports(meta)) {\n            amdArgs.push(t.stringLiteral(\"exports\"));\n\n            importNames.push(t.identifier(meta.exportName));\n          }\n\n          for (const [source, metadata] of meta.source) {\n            amdArgs.push(t.stringLiteral(source));\n            importNames.push(t.identifier(metadata.name));\n\n            if (!isSideEffectImport(metadata)) {\n              const interop = wrapInterop(\n                path,\n                t.identifier(metadata.name),\n                metadata.interop,\n              );\n              if (interop) {\n                const header = t.expressionStatement(\n                  t.assignmentExpression(\n                    \"=\",\n                    t.identifier(metadata.name),\n                    interop,\n                  ),\n                );\n                header.loc = metadata.loc;\n                headers.push(header);\n              }\n            }\n\n            headers.push(\n              ...buildNamespaceInitStatements(\n                meta,\n                metadata,\n                constantReexports,\n              ),\n            );\n          }\n\n          ensureStatementsHoisted(headers);\n          path.unshiftContainer(\"body\", headers);\n\n          injectWrapper(\n            path,\n            buildWrapper({\n              MODULE_NAME: moduleName,\n\n              AMD_ARGUMENTS: t.arrayExpression(amdArgs),\n              IMPORT_NAMES: importNames,\n            }) as t.ExpressionStatement,\n          );\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAYA,IAAAE,KAAA,GAAAF,OAAA;AAIA,MAAMG,YAAY,GAAGC,cAAQ,CAACC,SAAS,CAAC;AACxC;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,qBAAqB,GAAGF,cAAQ,CAACC,SAAS,CAAC;AACjD;AACA;AACA,CAAC,CAAC;AAEF,SAASE,aAAaA,CACpBC,IAAyB,EACzBC,OAA8B,EAC9B;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGH,IAAI,CAACI,IAAI;EACtCJ,IAAI,CAACI,IAAI,CAACD,UAAU,GAAG,EAAE;EACzBH,IAAI,CAACI,IAAI,CAACF,IAAI,GAAG,EAAE;EACnB,MAAMG,cAAc,GAAGL,IAAI,CACxBM,aAAa,CAAC,MAAM,EAAEL,OAAO,CAAC,CAAC,CAAC,CAAC,CACjCM,GAAG,CAAC,YAAY,CAA+B;EAClD,MAAMC,kBAAkB,GAAGH,cAAc,CAACE,GAAG,CAAC,WAAW,CAAC;EAC1D,MAAME,UAAU,GACdD,kBAAkB,CAChBA,kBAAkB,CAACE,MAAM,GAAG,CAAC,CAC9B,CACDH,GAAG,CAAC,MAAM,CAAC;EACbE,UAAU,CAACH,aAAa,CAAC,YAAY,EAAEH,UAAU,CAAC;EAClDM,UAAU,CAACH,aAAa,CAAC,MAAM,EAAEJ,IAAI,CAAC;AACxC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAiBc,IAAAC,0BAAO,EAAQ,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACvDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAAEC,iBAAiB;IAAEC,MAAM;IAAEC,UAAU;IAAEC,aAAa;IAAEC;EAAU,CAAC,GACvER,OAAO;EAET,MAAMS,iBAAiB,IAAAR,eAAA,GACrBF,GAAG,CAACW,UAAU,CAAC,mBAAmB,CAAC,YAAAT,eAAA,GAAID,OAAO,CAACW,KAAK;EACtD,MAAMC,oBAAoB,IAAAV,gBAAA,GACxBH,GAAG,CAACW,UAAU,CAAC,sBAAsB,CAAC,YAAAR,gBAAA,GAAIF,OAAO,CAACW,KAAK;EAEzD,OAAO;IACLE,IAAI,EAAE,uBAAuB;IAE7BC,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,mCAAmC,EAAE,KAAK,CAAC;IAC3D,CAAC;IAEDC,OAAO,EAAE;MACP,CAAC,gBAAgB,IACdlB,GAAG,CAACmB,KAAK,CAACC,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,CAAC,EAEvDnC,IAAqD,EACrDoC,KAAY,EACZ;QACA,IAAI,CAAC,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,uCAAuC,CAAC,EAAE;QAC7D,IAAIrC,IAAI,CAACsC,gBAAgB,CAAC,CAAC,IAAI,CAACtC,IAAI,CAACO,GAAG,CAAC,QAAQ,CAAC,CAACgC,QAAQ,CAAC,CAAC,EAAE;QAE/D,IAAI;UAAEC,SAAS;UAAEC,SAAS;UAAEC;QAAS,CAAC,GAAGN,KAAK;QAC9C,IAAI,CAACI,SAAS,EAAE;UACdA,SAAS,GAAGxC,IAAI,CAAC2C,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;UACvDR,KAAK,CAACI,SAAS,GAAGA,SAAS;QAC7B;QACA,IAAI,CAACC,SAAS,IAAI,CAACC,QAAQ,EAAE;UAC3BD,SAAS,GAAGzC,IAAI,CAAC2C,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;UACvDF,QAAQ,GAAG1C,IAAI,CAAC2C,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;UACrDR,KAAK,CAACK,SAAS,GAAGA,SAAS;UAC3BL,KAAK,CAACM,QAAQ,GAAGA,QAAQ;QAC3B;QAEA,IAAIG,MAAc,GAAGC,WAAC,CAACC,UAAU,CAAC,UAAU,CAAC;QAC7C,IAAI,CAACvB,SAAS,EAAE;UACdqB,MAAM,GAAG,IAAAG,mCAAW,EAAC,IAAI,CAACjB,IAAI,CAAC/B,IAAI,EAAE6C,MAAM,EAAE,WAAW,CAAC;QAC3D;QAEA7C,IAAI,CAACiD,WAAW,CACd,IAAAC,0CAAkB,EAChBlD,IAAI,CAACI,IAAI,EACT,KAAK,EACL,KAAK,EACL+C,SAAS,IAAIvD,cAAQ,CAACwD,UAAU,CAACC,GAAG;AAChD,6BAA6BZ,SAAS,KAAKC,QAAQ;AACnD,kBAAkBF,SAAS;AAC3B,qBAAqBW,SAAS;AAC9B,gCAAgCL,WAAC,CAACQ,SAAS,CAACb,SAAS,CAAC,IAAII,MAAM;AAChE,oBAAoBC,WAAC,CAACQ,SAAS,CAACZ,QAAQ,CAAC;AACzC;AACA;AACA,aACU,CACF,CAAC;MACH,CAAC;MACDa,OAAO,EAAE;QACPC,IAAIA,CAACxD,IAAI,EAAE;UAAEwC;QAAU,CAAC,EAAE;UACxB,IAAI,CAAC,IAAAiB,gCAAQ,EAACzD,IAAI,CAAC,EAAE;YACnB,IAAIwC,SAAS,EAAE;cACbzC,aAAa,CACXC,IAAI,EACJF,qBAAqB,CAAC;gBACpB4D,OAAO,EAAEZ,WAAC,CAACQ,SAAS,CAACd,SAAS;cAChC,CAAC,CACH,CAAC;YACH;YACA;UACF;UAEA,MAAMmB,OAAO,GAAG,EAAE;UAClB,MAAMC,WAAW,GAAG,EAAE;UACtB,IAAIpB,SAAS,EAAE;YACbmB,OAAO,CAACE,IAAI,CAACf,WAAC,CAACgB,aAAa,CAAC,SAAS,CAAC,CAAC;YACxCF,WAAW,CAACC,IAAI,CAACf,WAAC,CAACQ,SAAS,CAACd,SAAS,CAAC,CAAC;UAC1C;UAEA,IAAIuB,UAAU,GAAG,IAAAC,qCAAa,EAAC,IAAI,CAACjC,IAAI,CAACkC,IAAI,EAAEjD,OAAO,CAAC;UAEvD,IAAI+C,UAAU,EAAEA,UAAU,GAAGjB,WAAC,CAACgB,aAAa,CAACC,UAAU,CAAC;UAExD,MAAM;YAAEG,IAAI;YAAEC;UAAQ,CAAC,GAAG,IAAAC,+DAAuC,EAC/DpE,IAAI,EACJ;YACE4B,oBAAoB;YACpBH,iBAAiB;YACjBJ,MAAM;YACNC,UAAU;YACVF,iBAAiB;YACjBG,aAAa;YACbC,SAAS;YACT6C,QAAQ,EAAE,IAAI,CAACtC,IAAI,CAACkC,IAAI,CAACI;UAC3B,CACF,CAAC;UAED,IAAI,IAAAC,kCAAU,EAACJ,IAAI,CAAC,EAAE;YACpBP,OAAO,CAACE,IAAI,CAACf,WAAC,CAACgB,aAAa,CAAC,SAAS,CAAC,CAAC;YAExCF,WAAW,CAACC,IAAI,CAACf,WAAC,CAACC,UAAU,CAACmB,IAAI,CAACK,UAAU,CAAC,CAAC;UACjD;UAEA,KAAK,MAAM,CAACC,MAAM,EAAEC,QAAQ,CAAC,IAAIP,IAAI,CAACM,MAAM,EAAE;YAC5Cb,OAAO,CAACE,IAAI,CAACf,WAAC,CAACgB,aAAa,CAACU,MAAM,CAAC,CAAC;YACrCZ,WAAW,CAACC,IAAI,CAACf,WAAC,CAACC,UAAU,CAAC0B,QAAQ,CAAC5C,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAA6C,0CAAkB,EAACD,QAAQ,CAAC,EAAE;cACjC,MAAME,OAAO,GAAG,IAAA3B,mCAAW,EACzBhD,IAAI,EACJ8C,WAAC,CAACC,UAAU,CAAC0B,QAAQ,CAAC5C,IAAI,CAAC,EAC3B4C,QAAQ,CAACE,OACX,CAAC;cACD,IAAIA,OAAO,EAAE;gBACX,MAAMC,MAAM,GAAG9B,WAAC,CAAC+B,mBAAmB,CAClC/B,WAAC,CAACgC,oBAAoB,CACpB,GAAG,EACHhC,WAAC,CAACC,UAAU,CAAC0B,QAAQ,CAAC5C,IAAI,CAAC,EAC3B8C,OACF,CACF,CAAC;gBACDC,MAAM,CAACG,GAAG,GAAGN,QAAQ,CAACM,GAAG;gBACzBZ,OAAO,CAACN,IAAI,CAACe,MAAM,CAAC;cACtB;YACF;YAEAT,OAAO,CAACN,IAAI,CACV,GAAG,IAAAmB,oDAA4B,EAC7Bd,IAAI,EACJO,QAAQ,EACRhD,iBACF,CACF,CAAC;UACH;UAEA,IAAAwD,+CAAuB,EAACd,OAAO,CAAC;UAChCnE,IAAI,CAACkF,gBAAgB,CAAC,MAAM,EAAEf,OAAO,CAAC;UAEtCpE,aAAa,CACXC,IAAI,EACJL,YAAY,CAAC;YACXwF,WAAW,EAAEpB,UAAU;YAEvBqB,aAAa,EAAEtC,WAAC,CAACuC,eAAe,CAAC1B,OAAO,CAAC;YACzC2B,YAAY,EAAE1B;UAChB,CAAC,CACH,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}