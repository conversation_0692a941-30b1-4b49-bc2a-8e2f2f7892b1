{"ast": null, "code": "import React,{createContext,useState,useEffect}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";export const WishlistContext=/*#__PURE__*/createContext();export function WishlistProvider(_ref){let{children}=_ref;const[wishlist,setWishlist]=useState(()=>{const saved=localStorage.getItem('wishlist');return saved?JSON.parse(saved):[];});useEffect(()=>{localStorage.setItem('wishlist',JSON.stringify(wishlist));},[wishlist]);function addToWishlist(product){setWishlist(prev=>{if(prev.some(item=>item._id===product._id)){return prev;// Already in wishlist\n}return[...prev,product];});}function removeFromWishlist(productId){setWishlist(prev=>prev.filter(item=>item._id!==productId));}function clearWishlist(){setWishlist([]);}function isInWishlist(productId){return wishlist.some(item=>item._id===productId);}return/*#__PURE__*/_jsx(WishlistContext.Provider,{value:{wishlist,addToWishlist,removeFromWishlist,clearWishlist,isInWishlist},children:children});}", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "jsx", "_jsx", "WishlistContext", "WishlistProvider", "_ref", "children", "wishlist", "setWishlist", "saved", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "addToWishlist", "product", "prev", "some", "item", "_id", "removeFromWishlist", "productId", "filter", "clearWishlist", "isInWishlist", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/WishlistContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect } from 'react';\n\nexport const WishlistContext = createContext();\n\nexport function WishlistProvider({ children }) {\n  const [wishlist, setWishlist] = useState(() => {\n    const saved = localStorage.getItem('wishlist');\n    return saved ? JSON.parse(saved) : [];\n  });\n\n  useEffect(() => {\n    localStorage.setItem('wishlist', JSON.stringify(wishlist));\n  }, [wishlist]);\n\n  function addToWishlist(product) {\n    setWishlist(prev => {\n      if (prev.some(item => item._id === product._id)) {\n        return prev; // Already in wishlist\n      }\n      return [...prev, product];\n    });\n  }\n\n  function removeFromWishlist(productId) {\n    setWishlist(prev => prev.filter(item => item._id !== productId));\n  }\n\n  function clearWishlist() {\n    setWishlist([]);\n  }\n\n  function isInWishlist(productId) {\n    return wishlist.some(item => item._id === productId);\n  }\n\n  return (\n    <WishlistContext.Provider value={{ \n      wishlist, \n      addToWishlist, \n      removeFromWishlist, \n      clearWishlist,\n      isInWishlist \n    }}>\n      {children}\n    </WishlistContext.Provider>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElE,MAAO,MAAM,CAAAC,eAAe,cAAGL,aAAa,CAAC,CAAC,CAE9C,MAAO,SAAS,CAAAM,gBAAgBA,CAAAC,IAAA,CAAe,IAAd,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3C,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGT,QAAQ,CAAC,IAAM,CAC7C,KAAM,CAAAU,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAC9C,MAAO,CAAAF,KAAK,CAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,CAAG,EAAE,CACvC,CAAC,CAAC,CAEFT,SAAS,CAAC,IAAM,CACdU,YAAY,CAACI,OAAO,CAAC,UAAU,CAAEF,IAAI,CAACG,SAAS,CAACR,QAAQ,CAAC,CAAC,CAC5D,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd,QAAS,CAAAS,aAAaA,CAACC,OAAO,CAAE,CAC9BT,WAAW,CAACU,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACC,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,GAAG,GAAKJ,OAAO,CAACI,GAAG,CAAC,CAAE,CAC/C,MAAO,CAAAH,IAAI,CAAE;AACf,CACA,MAAO,CAAC,GAAGA,IAAI,CAAED,OAAO,CAAC,CAC3B,CAAC,CAAC,CACJ,CAEA,QAAS,CAAAK,kBAAkBA,CAACC,SAAS,CAAE,CACrCf,WAAW,CAACU,IAAI,EAAIA,IAAI,CAACM,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAACC,GAAG,GAAKE,SAAS,CAAC,CAAC,CAClE,CAEA,QAAS,CAAAE,aAAaA,CAAA,CAAG,CACvBjB,WAAW,CAAC,EAAE,CAAC,CACjB,CAEA,QAAS,CAAAkB,YAAYA,CAACH,SAAS,CAAE,CAC/B,MAAO,CAAAhB,QAAQ,CAACY,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,GAAG,GAAKE,SAAS,CAAC,CACtD,CAEA,mBACErB,IAAA,CAACC,eAAe,CAACwB,QAAQ,EAACC,KAAK,CAAE,CAC/BrB,QAAQ,CACRS,aAAa,CACbM,kBAAkB,CAClBG,aAAa,CACbC,YACF,CAAE,CAAApB,QAAA,CACCA,QAAQ,CACe,CAAC,CAE/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}