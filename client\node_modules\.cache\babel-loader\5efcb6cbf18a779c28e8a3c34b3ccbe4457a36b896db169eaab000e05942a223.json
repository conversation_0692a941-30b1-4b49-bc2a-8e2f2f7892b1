{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function Footer(){return/*#__PURE__*/_jsxs(\"footer\",{style:{marginTop:'50px',padding:'20px',backgroundColor:'#f8f9fa',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2024 EcoCommerce. All rights reserved.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Sustainable shopping for a better tomorrow \\uD83C\\uDF31\"})]});}export default Footer;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Footer", "style", "marginTop", "padding", "backgroundColor", "textAlign", "children"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\n\nfunction Footer() {\n  return (\n    <footer style={{ marginTop: '50px', padding: '20px', backgroundColor: '#f8f9fa', textAlign: 'center' }}>\n      <p>&copy; 2024 EcoCommerce. All rights reserved.</p>\n      <p>Sustainable shopping for a better tomorrow 🌱</p>\n    </footer>\n  );\n}\n\nexport default Footer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,MAAMA,CAAA,CAAG,CAChB,mBACED,KAAA,WAAQE,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,eAAe,CAAE,SAAS,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAC,QAAA,eACrGT,IAAA,MAAAS,QAAA,CAAG,6CAA6C,CAAG,CAAC,cACpDT,IAAA,MAAAS,QAAA,CAAG,yDAA6C,CAAG,CAAC,EAC9C,CAAC,CAEb,CAEA,cAAe,CAAAN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}