{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function LoadingSpinner(_ref){let{size='medium',message='Loading...',fullScreen=false}=_ref;const sizeClasses={small:'spinner-small',medium:'spinner-medium',large:'spinner-large'};const Container=fullScreen?'div':React.Fragment;const containerProps=fullScreen?{className:'fullscreen-loader'}:{};return/*#__PURE__*/_jsxs(Container,_objectSpread(_objectSpread({},containerProps),{},{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner \".concat(sizeClasses[size]),children:/*#__PURE__*/_jsxs(\"div\",{className:\"spinner-ring\",children:[/*#__PURE__*/_jsx(\"div\",{}),/*#__PURE__*/_jsx(\"div\",{}),/*#__PURE__*/_jsx(\"div\",{}),/*#__PURE__*/_jsx(\"div\",{})]})}),message&&/*#__PURE__*/_jsx(\"p\",{className:\"loading-message\",children:message})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .fullscreen-loader {\\n          position: fixed;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background: rgba(255, 255, 255, 0.9);\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          z-index: 9999;\\n          backdrop-filter: blur(4px);\\n        }\\n\\n        .loading-container {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 1rem;\\n          padding: 2rem;\\n        }\\n\\n        .spinner {\\n          display: inline-block;\\n          position: relative;\\n        }\\n\\n        .spinner-small {\\n          width: 24px;\\n          height: 24px;\\n        }\\n\\n        .spinner-medium {\\n          width: 40px;\\n          height: 40px;\\n        }\\n\\n        .spinner-large {\\n          width: 64px;\\n          height: 64px;\\n        }\\n\\n        .spinner-ring {\\n          width: 100%;\\n          height: 100%;\\n          position: relative;\\n        }\\n\\n        .spinner-ring div {\\n          box-sizing: border-box;\\n          display: block;\\n          position: absolute;\\n          width: 100%;\\n          height: 100%;\\n          border: 3px solid var(--primary-color);\\n          border-radius: 50%;\\n          animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;\\n          border-color: var(--primary-color) transparent transparent transparent;\\n        }\\n\\n        .spinner-ring div:nth-child(1) {\\n          animation-delay: -0.45s;\\n        }\\n\\n        .spinner-ring div:nth-child(2) {\\n          animation-delay: -0.3s;\\n        }\\n\\n        .spinner-ring div:nth-child(3) {\\n          animation-delay: -0.15s;\\n        }\\n\\n        @keyframes spinner-ring {\\n          0% {\\n            transform: rotate(0deg);\\n          }\\n          100% {\\n            transform: rotate(360deg);\\n          }\\n        }\\n\\n        .loading-message {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n          margin: 0;\\n          text-align: center;\\n        }\\n\\n        /* Pulse animation for skeleton loading */\\n        @keyframes pulse {\\n          0%, 100% {\\n            opacity: 1;\\n          }\\n          50% {\\n            opacity: 0.5;\\n          }\\n        }\\n\\n        .pulse {\\n          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n        }\\n      \"})]}));}export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSpinner", "_ref", "size", "message", "fullScreen", "sizeClasses", "small", "medium", "large", "Container", "Fragment", "containerProps", "className", "_objectSpread", "children", "concat"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/LoadingSpinner.jsx"], "sourcesContent": ["import React from 'react';\n\nfunction LoadingSpinner({ size = 'medium', message = 'Loading...', fullScreen = false }) {\n  const sizeClasses = {\n    small: 'spinner-small',\n    medium: 'spinner-medium',\n    large: 'spinner-large'\n  };\n\n  const Container = fullScreen ? 'div' : React.Fragment;\n  const containerProps = fullScreen ? { className: 'fullscreen-loader' } : {};\n\n  return (\n    <Container {...containerProps}>\n      <div className=\"loading-container\">\n        <div className={`spinner ${sizeClasses[size]}`}>\n          <div className=\"spinner-ring\">\n            <div></div>\n            <div></div>\n            <div></div>\n            <div></div>\n          </div>\n        </div>\n        {message && <p className=\"loading-message\">{message}</p>}\n      </div>\n\n      <style jsx>{`\n        .fullscreen-loader {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(255, 255, 255, 0.9);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 9999;\n          backdrop-filter: blur(4px);\n        }\n\n        .loading-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n          padding: 2rem;\n        }\n\n        .spinner {\n          display: inline-block;\n          position: relative;\n        }\n\n        .spinner-small {\n          width: 24px;\n          height: 24px;\n        }\n\n        .spinner-medium {\n          width: 40px;\n          height: 40px;\n        }\n\n        .spinner-large {\n          width: 64px;\n          height: 64px;\n        }\n\n        .spinner-ring {\n          width: 100%;\n          height: 100%;\n          position: relative;\n        }\n\n        .spinner-ring div {\n          box-sizing: border-box;\n          display: block;\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          border: 3px solid var(--primary-color);\n          border-radius: 50%;\n          animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;\n          border-color: var(--primary-color) transparent transparent transparent;\n        }\n\n        .spinner-ring div:nth-child(1) {\n          animation-delay: -0.45s;\n        }\n\n        .spinner-ring div:nth-child(2) {\n          animation-delay: -0.3s;\n        }\n\n        .spinner-ring div:nth-child(3) {\n          animation-delay: -0.15s;\n        }\n\n        @keyframes spinner-ring {\n          0% {\n            transform: rotate(0deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n\n        .loading-message {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          font-weight: 500;\n          margin: 0;\n          text-align: center;\n        }\n\n        /* Pulse animation for skeleton loading */\n        @keyframes pulse {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.5;\n          }\n        }\n\n        .pulse {\n          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n        }\n      `}</style>\n    </Container>\n  );\n}\n\nexport default LoadingSpinner;\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,cAAcA,CAAAC,IAAA,CAAkE,IAAjE,CAAEC,IAAI,CAAG,QAAQ,CAAEC,OAAO,CAAG,YAAY,CAAEC,UAAU,CAAG,KAAM,CAAC,CAAAH,IAAA,CACrF,KAAM,CAAAI,WAAW,CAAG,CAClBC,KAAK,CAAE,eAAe,CACtBC,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,eACT,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGL,UAAU,CAAG,KAAK,CAAGT,KAAK,CAACe,QAAQ,CACrD,KAAM,CAAAC,cAAc,CAAGP,UAAU,CAAG,CAAEQ,SAAS,CAAE,mBAAoB,CAAC,CAAG,CAAC,CAAC,CAE3E,mBACEb,KAAA,CAACU,SAAS,CAAAI,aAAA,CAAAA,aAAA,IAAKF,cAAc,MAAAG,QAAA,eAC3Bf,KAAA,QAAKa,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCjB,IAAA,QAAKe,SAAS,YAAAG,MAAA,CAAaV,WAAW,CAACH,IAAI,CAAC,CAAG,CAAAY,QAAA,cAC7Cf,KAAA,QAAKa,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3BjB,IAAA,SAAU,CAAC,cACXA,IAAA,SAAU,CAAC,cACXA,IAAA,SAAU,CAAC,cACXA,IAAA,SAAU,CAAC,EACR,CAAC,CACH,CAAC,CACLM,OAAO,eAAIN,IAAA,MAAGe,SAAS,CAAC,iBAAiB,CAAAE,QAAA,CAAEX,OAAO,CAAI,CAAC,EACrD,CAAC,cAENN,IAAA,UAAOD,GAAG,MAAAkB,QAAA,i6EAwGD,CAAC,GACD,CAAC,CAEhB,CAEA,cAAe,CAAAd,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}