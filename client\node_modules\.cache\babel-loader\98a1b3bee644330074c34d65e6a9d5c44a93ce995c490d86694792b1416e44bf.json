{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\ProductReviews.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductReviews({\n  productId\n}) {\n  _s();\n  const {\n    user\n  } = useContext(AuthContext);\n  const [reviews, setReviews] = useState([{\n    id: 1,\n    user: '<PERSON>',\n    rating: 5,\n    comment: 'Amazing product! Really love the eco-friendly materials and the quality is outstanding.',\n    date: '2024-01-15',\n    verified: true\n  }, {\n    id: 2,\n    user: '<PERSON>',\n    rating: 4,\n    comment: 'Good value for money. Shipping was fast and packaging was minimal which I appreciate.',\n    date: '2024-01-10',\n    verified: true\n  }, {\n    id: 3,\n    user: '<PERSON>',\n    rating: 5,\n    comment: 'Exceeded my expectations! Will definitely buy again and recommend to friends.',\n    date: '2024-01-08',\n    verified: false\n  }]);\n  const [newReview, setNewReview] = useState({\n    rating: 5,\n    comment: ''\n  });\n  const [showReviewForm, setShowReviewForm] = useState(false);\n  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;\n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\n    rating,\n    count: reviews.filter(r => r.rating === rating).length,\n    percentage: reviews.filter(r => r.rating === rating).length / reviews.length * 100\n  }));\n  const handleSubmitReview = e => {\n    e.preventDefault();\n    if (!user) {\n      alert('Please login to submit a review');\n      return;\n    }\n    const review = {\n      id: Date.now(),\n      user: user.name,\n      rating: newReview.rating,\n      comment: newReview.comment,\n      date: new Date().toISOString().split('T')[0],\n      verified: true\n    };\n    setReviews([review, ...reviews]);\n    setNewReview({\n      rating: 5,\n      comment: ''\n    });\n    setShowReviewForm(false);\n  };\n  const renderStars = (rating, interactive = false, onRatingChange = null) => {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        type: interactive ? 'button' : undefined,\n        className: `star ${i <= rating ? 'filled' : 'empty'} ${interactive ? 'interactive' : ''}`,\n        onClick: interactive ? () => onRatingChange(i) : undefined,\n        disabled: !interactive,\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: i <= rating ? 'fas fa-star' : 'far fa-star'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this));\n    }\n    return stars;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-reviews\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reviews-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Customer Reviews\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"write-review-btn\",\n        onClick: () => setShowReviewForm(!showReviewForm),\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), \"Write a Review\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reviews-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"average-rating\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating-number\",\n          children: averageRating.toFixed(1)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating-stars\",\n          children: renderStars(Math.round(averageRating))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"total-reviews\",\n          children: [reviews.length, \" reviews\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rating-breakdown\",\n        children: ratingDistribution.map(({\n          rating,\n          count,\n          percentage\n        }) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating-bar\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"rating-label\",\n            children: [rating, \" star\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bar-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bar-fill\",\n              style: {\n                width: `${percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"rating-count\",\n            children: [\"(\", count, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)]\n        }, rating, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), showReviewForm && /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmitReview,\n      className: \"review-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Write Your Review\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Rating:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating-input\",\n          children: renderStars(newReview.rating, true, rating => setNewReview({\n            ...newReview,\n            rating\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"comment\",\n          children: \"Your Review:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"comment\",\n          value: newReview.comment,\n          onChange: e => setNewReview({\n            ...newReview,\n            comment: e.target.value\n          }),\n          placeholder: \"Share your experience with this product...\",\n          required: true,\n          rows: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowReviewForm(false),\n          className: \"cancel-btn\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"submit-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-paper-plane\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), \"Submit Review\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reviews-list\",\n      children: reviews.map(review => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"review-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"review-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"reviewer-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"reviewer-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reviewer-name\",\n                children: [review.user, review.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"verified-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-check-circle\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 25\n                  }, this), \"Verified Purchase\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-date\",\n                children: review.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"review-rating\",\n            children: renderStars(review.rating)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"review-comment\",\n          children: review.comment\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, review.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .product-reviews {\n          margin-top: 3rem;\n          padding-top: 2rem;\n          border-top: 2px solid var(--border-color);\n        }\n\n        .reviews-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n        }\n\n        .reviews-header h3 {\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0;\n        }\n\n        .write-review-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .write-review-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        .reviews-summary {\n          display: grid;\n          grid-template-columns: auto 1fr;\n          gap: 2rem;\n          margin-bottom: 2rem;\n          padding: 1.5rem;\n          background: var(--bg-secondary);\n          border-radius: 0.75rem;\n        }\n\n        .average-rating {\n          text-align: center;\n        }\n\n        .rating-number {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          line-height: 1;\n        }\n\n        .rating-stars {\n          display: flex;\n          justify-content: center;\n          gap: 0.25rem;\n          margin: 0.5rem 0;\n        }\n\n        .star {\n          background: none;\n          border: none;\n          color: #fbbf24;\n          font-size: 1.25rem;\n          cursor: default;\n        }\n\n        .star.interactive {\n          cursor: pointer;\n          transition: transform 0.1s;\n        }\n\n        .star.interactive:hover {\n          transform: scale(1.2);\n        }\n\n        .star.empty {\n          color: #d1d5db;\n        }\n\n        .total-reviews {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .rating-breakdown {\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .rating-bar {\n          display: grid;\n          grid-template-columns: 60px 1fr 40px;\n          align-items: center;\n          gap: 0.75rem;\n          font-size: 0.875rem;\n        }\n\n        .rating-label {\n          color: var(--text-secondary);\n        }\n\n        .bar-container {\n          height: 8px;\n          background: #e5e7eb;\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .bar-fill {\n          height: 100%;\n          background: #fbbf24;\n          transition: width 0.3s ease;\n        }\n\n        .rating-count {\n          color: var(--text-secondary);\n          text-align: right;\n        }\n\n        .review-form {\n          background: white;\n          padding: 2rem;\n          border-radius: 0.75rem;\n          border: 2px solid var(--border-color);\n          margin-bottom: 2rem;\n        }\n\n        .review-form h4 {\n          margin: 0 0 1.5rem 0;\n          color: var(--text-primary);\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .rating-input {\n          display: flex;\n          gap: 0.25rem;\n        }\n\n        .rating-input .star {\n          font-size: 1.5rem;\n        }\n\n        textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-family: inherit;\n          font-size: 1rem;\n          resize: vertical;\n        }\n\n        textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n        }\n\n        .submit-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .reviews-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1.5rem;\n        }\n\n        .review-item {\n          background: white;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .review-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 1rem;\n        }\n\n        .reviewer-info {\n          display: flex;\n          gap: 0.75rem;\n        }\n\n        .reviewer-avatar {\n          width: 40px;\n          height: 40px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .reviewer-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .verified-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .review-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .review-rating {\n          display: flex;\n          gap: 0.125rem;\n        }\n\n        .review-comment {\n          color: var(--text-primary);\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .reviews-summary {\n            grid-template-columns: 1fr;\n            text-align: center;\n          }\n\n          .reviews-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductReviews, \"qQAvAKGMPaLGCRECB9UutA6HlQo=\");\n_c = ProductReviews;\nexport default ProductReviews;\nvar _c;\n$RefreshReg$(_c, \"ProductReviews\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "AuthContext", "jsxDEV", "_jsxDEV", "ProductReviews", "productId", "_s", "user", "reviews", "setReviews", "id", "rating", "comment", "date", "verified", "newReview", "set<PERSON>ew<PERSON><PERSON>iew", "showReviewForm", "setShowReviewForm", "averageRating", "reduce", "sum", "review", "length", "ratingDistribution", "map", "count", "filter", "r", "percentage", "handleSubmitReview", "e", "preventDefault", "alert", "Date", "now", "name", "toISOString", "split", "renderStars", "interactive", "onRatingChange", "stars", "i", "push", "type", "undefined", "className", "onClick", "disabled", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "Math", "round", "style", "width", "onSubmit", "htmlFor", "value", "onChange", "target", "placeholder", "required", "rows", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ProductReviews.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\n\nfunction ProductReviews({ productId }) {\n  const { user } = useContext(AuthContext);\n  const [reviews, setReviews] = useState([\n    {\n      id: 1,\n      user: '<PERSON>',\n      rating: 5,\n      comment: 'Amazing product! Really love the eco-friendly materials and the quality is outstanding.',\n      date: '2024-01-15',\n      verified: true\n    },\n    {\n      id: 2,\n      user: '<PERSON>',\n      rating: 4,\n      comment: 'Good value for money. Shipping was fast and packaging was minimal which I appreciate.',\n      date: '2024-01-10',\n      verified: true\n    },\n    {\n      id: 3,\n      user: '<PERSON>',\n      rating: 5,\n      comment: 'Exceeded my expectations! Will definitely buy again and recommend to friends.',\n      date: '2024-01-08',\n      verified: false\n    }\n  ]);\n  \n  const [newReview, setNewReview] = useState({\n    rating: 5,\n    comment: ''\n  });\n  \n  const [showReviewForm, setShowReviewForm] = useState(false);\n\n  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;\n  \n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\n    rating,\n    count: reviews.filter(r => r.rating === rating).length,\n    percentage: (reviews.filter(r => r.rating === rating).length / reviews.length) * 100\n  }));\n\n  const handleSubmitReview = (e) => {\n    e.preventDefault();\n    if (!user) {\n      alert('Please login to submit a review');\n      return;\n    }\n    \n    const review = {\n      id: Date.now(),\n      user: user.name,\n      rating: newReview.rating,\n      comment: newReview.comment,\n      date: new Date().toISOString().split('T')[0],\n      verified: true\n    };\n    \n    setReviews([review, ...reviews]);\n    setNewReview({ rating: 5, comment: '' });\n    setShowReviewForm(false);\n  };\n\n  const renderStars = (rating, interactive = false, onRatingChange = null) => {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(\n        <button\n          key={i}\n          type={interactive ? 'button' : undefined}\n          className={`star ${i <= rating ? 'filled' : 'empty'} ${interactive ? 'interactive' : ''}`}\n          onClick={interactive ? () => onRatingChange(i) : undefined}\n          disabled={!interactive}\n        >\n          <i className={i <= rating ? 'fas fa-star' : 'far fa-star'}></i>\n        </button>\n      );\n    }\n    return stars;\n  };\n\n  return (\n    <div className=\"product-reviews\">\n      <div className=\"reviews-header\">\n        <h3>Customer Reviews</h3>\n        <button \n          className=\"write-review-btn\"\n          onClick={() => setShowReviewForm(!showReviewForm)}\n        >\n          <i className=\"fas fa-edit\"></i>\n          Write a Review\n        </button>\n      </div>\n\n      <div className=\"reviews-summary\">\n        <div className=\"average-rating\">\n          <div className=\"rating-number\">{averageRating.toFixed(1)}</div>\n          <div className=\"rating-stars\">\n            {renderStars(Math.round(averageRating))}\n          </div>\n          <div className=\"total-reviews\">{reviews.length} reviews</div>\n        </div>\n\n        <div className=\"rating-breakdown\">\n          {ratingDistribution.map(({ rating, count, percentage }) => (\n            <div key={rating} className=\"rating-bar\">\n              <span className=\"rating-label\">{rating} star</span>\n              <div className=\"bar-container\">\n                <div className=\"bar-fill\" style={{ width: `${percentage}%` }}></div>\n              </div>\n              <span className=\"rating-count\">({count})</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {showReviewForm && (\n        <form onSubmit={handleSubmitReview} className=\"review-form\">\n          <h4>Write Your Review</h4>\n          \n          <div className=\"form-group\">\n            <label>Rating:</label>\n            <div className=\"rating-input\">\n              {renderStars(newReview.rating, true, (rating) => \n                setNewReview({ ...newReview, rating })\n              )}\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"comment\">Your Review:</label>\n            <textarea\n              id=\"comment\"\n              value={newReview.comment}\n              onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}\n              placeholder=\"Share your experience with this product...\"\n              required\n              rows=\"4\"\n            />\n          </div>\n\n          <div className=\"form-actions\">\n            <button type=\"button\" onClick={() => setShowReviewForm(false)} className=\"cancel-btn\">\n              Cancel\n            </button>\n            <button type=\"submit\" className=\"submit-btn\">\n              <i className=\"fas fa-paper-plane\"></i>\n              Submit Review\n            </button>\n          </div>\n        </form>\n      )}\n\n      <div className=\"reviews-list\">\n        {reviews.map(review => (\n          <div key={review.id} className=\"review-item\">\n            <div className=\"review-header\">\n              <div className=\"reviewer-info\">\n                <div className=\"reviewer-avatar\">\n                  <i className=\"fas fa-user\"></i>\n                </div>\n                <div>\n                  <div className=\"reviewer-name\">\n                    {review.user}\n                    {review.verified && (\n                      <span className=\"verified-badge\">\n                        <i className=\"fas fa-check-circle\"></i>\n                        Verified Purchase\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"review-date\">{review.date}</div>\n                </div>\n              </div>\n              <div className=\"review-rating\">\n                {renderStars(review.rating)}\n              </div>\n            </div>\n            <div className=\"review-comment\">\n              {review.comment}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <style jsx>{`\n        .product-reviews {\n          margin-top: 3rem;\n          padding-top: 2rem;\n          border-top: 2px solid var(--border-color);\n        }\n\n        .reviews-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n        }\n\n        .reviews-header h3 {\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0;\n        }\n\n        .write-review-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .write-review-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        .reviews-summary {\n          display: grid;\n          grid-template-columns: auto 1fr;\n          gap: 2rem;\n          margin-bottom: 2rem;\n          padding: 1.5rem;\n          background: var(--bg-secondary);\n          border-radius: 0.75rem;\n        }\n\n        .average-rating {\n          text-align: center;\n        }\n\n        .rating-number {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          line-height: 1;\n        }\n\n        .rating-stars {\n          display: flex;\n          justify-content: center;\n          gap: 0.25rem;\n          margin: 0.5rem 0;\n        }\n\n        .star {\n          background: none;\n          border: none;\n          color: #fbbf24;\n          font-size: 1.25rem;\n          cursor: default;\n        }\n\n        .star.interactive {\n          cursor: pointer;\n          transition: transform 0.1s;\n        }\n\n        .star.interactive:hover {\n          transform: scale(1.2);\n        }\n\n        .star.empty {\n          color: #d1d5db;\n        }\n\n        .total-reviews {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .rating-breakdown {\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .rating-bar {\n          display: grid;\n          grid-template-columns: 60px 1fr 40px;\n          align-items: center;\n          gap: 0.75rem;\n          font-size: 0.875rem;\n        }\n\n        .rating-label {\n          color: var(--text-secondary);\n        }\n\n        .bar-container {\n          height: 8px;\n          background: #e5e7eb;\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .bar-fill {\n          height: 100%;\n          background: #fbbf24;\n          transition: width 0.3s ease;\n        }\n\n        .rating-count {\n          color: var(--text-secondary);\n          text-align: right;\n        }\n\n        .review-form {\n          background: white;\n          padding: 2rem;\n          border-radius: 0.75rem;\n          border: 2px solid var(--border-color);\n          margin-bottom: 2rem;\n        }\n\n        .review-form h4 {\n          margin: 0 0 1.5rem 0;\n          color: var(--text-primary);\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .rating-input {\n          display: flex;\n          gap: 0.25rem;\n        }\n\n        .rating-input .star {\n          font-size: 1.5rem;\n        }\n\n        textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-family: inherit;\n          font-size: 1rem;\n          resize: vertical;\n        }\n\n        textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n        }\n\n        .submit-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .reviews-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1.5rem;\n        }\n\n        .review-item {\n          background: white;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .review-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 1rem;\n        }\n\n        .reviewer-info {\n          display: flex;\n          gap: 0.75rem;\n        }\n\n        .reviewer-avatar {\n          width: 40px;\n          height: 40px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .reviewer-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .verified-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .review-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .review-rating {\n          display: flex;\n          gap: 0.125rem;\n        }\n\n        .review-comment {\n          color: var(--text-primary);\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .reviews-summary {\n            grid-template-columns: 1fr;\n            text-align: center;\n          }\n\n          .reviews-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ProductReviews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,cAAcA,CAAC;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGP,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,CACrC;IACEW,EAAE,EAAE,CAAC;IACLH,IAAI,EAAE,eAAe;IACrBI,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,yFAAyF;IAClGC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLH,IAAI,EAAE,WAAW;IACjBI,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,uFAAuF;IAChGC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLH,IAAI,EAAE,aAAa;IACnBI,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,+EAA+E;IACxFC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC;IACzCY,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMoB,aAAa,GAAGX,OAAO,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACX,MAAM,EAAE,CAAC,CAAC,GAAGH,OAAO,CAACe,MAAM;EAE9F,MAAMC,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACd,MAAM,KAAK;IACxDA,MAAM;IACNe,KAAK,EAAElB,OAAO,CAACmB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACY,MAAM;IACtDM,UAAU,EAAGrB,OAAO,CAACmB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACY,MAAM,GAAGf,OAAO,CAACe,MAAM,GAAI;EACnF,CAAC,CAAC,CAAC;EAEH,MAAMO,kBAAkB,GAAIC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACzB,IAAI,EAAE;MACT0B,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACF;IAEA,MAAMX,MAAM,GAAG;MACbZ,EAAE,EAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC;MACd5B,IAAI,EAAEA,IAAI,CAAC6B,IAAI;MACfzB,MAAM,EAAEI,SAAS,CAACJ,MAAM;MACxBC,OAAO,EAAEG,SAAS,CAACH,OAAO;MAC1BC,IAAI,EAAE,IAAIqB,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CxB,QAAQ,EAAE;IACZ,CAAC;IAEDL,UAAU,CAAC,CAACa,MAAM,EAAE,GAAGd,OAAO,CAAC,CAAC;IAChCQ,YAAY,CAAC;MAAEL,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;IACxCM,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMqB,WAAW,GAAGA,CAAC5B,MAAM,EAAE6B,WAAW,GAAG,KAAK,EAAEC,cAAc,GAAG,IAAI,KAAK;IAC1E,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3BD,KAAK,CAACE,IAAI,cACRzC,OAAA;QAEE0C,IAAI,EAAEL,WAAW,GAAG,QAAQ,GAAGM,SAAU;QACzCC,SAAS,EAAE,QAAQJ,CAAC,IAAIhC,MAAM,GAAG,QAAQ,GAAG,OAAO,IAAI6B,WAAW,GAAG,aAAa,GAAG,EAAE,EAAG;QAC1FQ,OAAO,EAAER,WAAW,GAAG,MAAMC,cAAc,CAACE,CAAC,CAAC,GAAGG,SAAU;QAC3DG,QAAQ,EAAE,CAACT,WAAY;QAAAU,QAAA,eAEvB/C,OAAA;UAAG4C,SAAS,EAAEJ,CAAC,IAAIhC,MAAM,GAAG,aAAa,GAAG;QAAc;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC,GAN1DX,CAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOA,CACV,CAAC;IACH;IACA,OAAOZ,KAAK;EACd,CAAC;EAED,oBACEvC,OAAA;IAAK4C,SAAS,EAAC,iBAAiB;IAAAG,QAAA,gBAC9B/C,OAAA;MAAK4C,SAAS,EAAC,gBAAgB;MAAAG,QAAA,gBAC7B/C,OAAA;QAAA+C,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBnD,OAAA;QACE4C,SAAS,EAAC,kBAAkB;QAC5BC,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,CAACD,cAAc,CAAE;QAAAiC,QAAA,gBAElD/C,OAAA;UAAG4C,SAAS,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,kBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENnD,OAAA;MAAK4C,SAAS,EAAC,iBAAiB;MAAAG,QAAA,gBAC9B/C,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7B/C,OAAA;UAAK4C,SAAS,EAAC,eAAe;UAAAG,QAAA,EAAE/B,aAAa,CAACoC,OAAO,CAAC,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/DnD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAG,QAAA,EAC1BX,WAAW,CAACiB,IAAI,CAACC,KAAK,CAACtC,aAAa,CAAC;QAAC;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNnD,OAAA;UAAK4C,SAAS,EAAC,eAAe;UAAAG,QAAA,GAAE1C,OAAO,CAACe,MAAM,EAAC,UAAQ;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAENnD,OAAA;QAAK4C,SAAS,EAAC,kBAAkB;QAAAG,QAAA,EAC9B1B,kBAAkB,CAACC,GAAG,CAAC,CAAC;UAAEd,MAAM;UAAEe,KAAK;UAAEG;QAAW,CAAC,kBACpD1B,OAAA;UAAkB4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACtC/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAEvC,MAAM,EAAC,OAAK;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDnD,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAG,QAAA,eAC5B/C,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAACW,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAG9B,UAAU;cAAI;YAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNnD,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAC,GAAC,EAACxB,KAAK,EAAC,GAAC;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GALvC3C,MAAM;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELrC,cAAc,iBACbd,OAAA;MAAMyD,QAAQ,EAAE9B,kBAAmB;MAACiB,SAAS,EAAC,aAAa;MAAAG,QAAA,gBACzD/C,OAAA;QAAA+C,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE1BnD,OAAA;QAAK4C,SAAS,EAAC,YAAY;QAAAG,QAAA,gBACzB/C,OAAA;UAAA+C,QAAA,EAAO;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtBnD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAG,QAAA,EAC1BX,WAAW,CAACxB,SAAS,CAACJ,MAAM,EAAE,IAAI,EAAGA,MAAM,IAC1CK,YAAY,CAAC;YAAE,GAAGD,SAAS;YAAEJ;UAAO,CAAC,CACvC;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK4C,SAAS,EAAC,YAAY;QAAAG,QAAA,gBACzB/C,OAAA;UAAO0D,OAAO,EAAC,SAAS;UAAAX,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7CnD,OAAA;UACEO,EAAE,EAAC,SAAS;UACZoD,KAAK,EAAE/C,SAAS,CAACH,OAAQ;UACzBmD,QAAQ,EAAGhC,CAAC,IAAKf,YAAY,CAAC;YAAE,GAAGD,SAAS;YAAEH,OAAO,EAAEmB,CAAC,CAACiC,MAAM,CAACF;UAAM,CAAC,CAAE;UACzEG,WAAW,EAAC,4CAA4C;UACxDC,QAAQ;UACRC,IAAI,EAAC;QAAG;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnD,OAAA;QAAK4C,SAAS,EAAC,cAAc;QAAAG,QAAA,gBAC3B/C,OAAA;UAAQ0C,IAAI,EAAC,QAAQ;UAACG,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;UAAC6B,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnD,OAAA;UAAQ0C,IAAI,EAAC,QAAQ;UAACE,SAAS,EAAC,YAAY;UAAAG,QAAA,gBAC1C/C,OAAA;YAAG4C,SAAS,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAEDnD,OAAA;MAAK4C,SAAS,EAAC,cAAc;MAAAG,QAAA,EAC1B1C,OAAO,CAACiB,GAAG,CAACH,MAAM,iBACjBnB,OAAA;QAAqB4C,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1C/C,OAAA;UAAK4C,SAAS,EAAC,eAAe;UAAAG,QAAA,gBAC5B/C,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAG,QAAA,gBAC5B/C,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAG,QAAA,eAC9B/C,OAAA;gBAAG4C,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNnD,OAAA;cAAA+C,QAAA,gBACE/C,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAG,QAAA,GAC3B5B,MAAM,CAACf,IAAI,EACXe,MAAM,CAACR,QAAQ,iBACdX,OAAA;kBAAM4C,SAAS,EAAC,gBAAgB;kBAAAG,QAAA,gBAC9B/C,OAAA;oBAAG4C,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,qBAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnD,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAG,QAAA,EAAE5B,MAAM,CAACT;cAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnD,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAG,QAAA,EAC3BX,WAAW,CAACjB,MAAM,CAACX,MAAM;UAAC;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnD,OAAA;UAAK4C,SAAS,EAAC,gBAAgB;UAAAG,QAAA,EAC5B5B,MAAM,CAACV;QAAO;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA,GAzBEhC,MAAM,CAACZ,EAAE;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0Bd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnD,OAAA;MAAOiE,GAAG;MAAAlB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAChD,EAAA,CA5dQF,cAAc;AAAAiE,EAAA,GAAdjE,cAAc;AA8dvB,eAAeA,cAAc;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}