{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { AuthContext } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  const {\n    cart\n  } = useContext(CartContext);\n  const {\n    user,\n    logout\n  } = useContext(AuthContext);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const cartItemCount = cart.reduce((total, item) => total + item.quantity, 0);\n\n  // Close menus when clicking outside\n  React.useEffect(() => {\n    const handleClickOutside = event => {\n      if (!event.target.closest('.user-menu') && !event.target.closest('.mobile-menu')) {\n        setShowUserMenu(false);\n        setShowMobileMenu(false);\n      }\n    };\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-leaf\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), \"EcoCommerce\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"desktop-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: \"nav-link\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart\",\n          className: \"nav-link cart-link\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-shopping-cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), cartItemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"cart-badge\",\n            children: cartItemCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin\",\n          className: \"nav-link admin-link\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-cog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"user-button\",\n            onClick: () => setShowUserMenu(!showUserMenu),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-down\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              onClick: () => setShowUserMenu(false),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 21\n              }, this), \"Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/wishlist\",\n              onClick: () => setShowUserMenu(false),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-heart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 21\n              }, this), \"Wishlist\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/orders\",\n              onClick: () => setShowUserMenu(false),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-box\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 21\n              }, this), \"Orders\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                logout();\n                setShowUserMenu(false);\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-out-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 21\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"nav-link\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sign-in-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"mobile-menu-btn\",\n        onClick: () => setShowMobileMenu(!showMobileMenu),\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: `fas ${showMobileMenu ? 'fa-times' : 'fa-bars'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), showMobileMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-menu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-menu-content\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: \"mobile-nav-link\",\n          onClick: () => setShowMobileMenu(false),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), \"Search\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart\",\n          className: \"mobile-nav-link\",\n          onClick: () => setShowMobileMenu(false),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-shopping-cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), \"Cart\", cartItemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"cart-badge\",\n            children: cartItemCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/profile\",\n            className: \"mobile-nav-link\",\n            onClick: () => setShowMobileMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/wishlist\",\n            className: \"mobile-nav-link\",\n            onClick: () => setShowMobileMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-heart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this), \"Wishlist\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/orders\",\n            className: \"mobile-nav-link\",\n            onClick: () => setShowMobileMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-box\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), \"Orders\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"mobile-nav-link logout-btn\",\n            onClick: () => {\n              logout();\n              setShowMobileMenu(false);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-sign-out-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"mobile-nav-link\",\n          onClick: () => setShowMobileMenu(false),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sign-in-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 17\n          }, this), \"Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        header {\n          position: relative;\n        }\n\n        .header-content {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 0 1rem;\n        }\n\n        .desktop-nav {\n          display: flex;\n          gap: 2rem;\n          align-items: center;\n        }\n\n        .nav-link {\n          position: relative;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .nav-link span {\n          display: block;\n        }\n\n        .cart-link {\n          position: relative;\n        }\n\n        .cart-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          background: #ef4444;\n          color: white;\n          border-radius: 50%;\n          width: 20px;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n          z-index: 10;\n        }\n\n        .user-menu {\n          position: relative;\n        }\n\n        .user-button {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          color: white;\n          padding: 0.5rem 1rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .user-button:hover {\n          background: rgba(255, 255, 255, 0.2);\n        }\n\n        .user-dropdown {\n          position: absolute;\n          top: 100%;\n          right: 0;\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);\n          padding: 0.75rem;\n          min-width: 220px;\n          z-index: 1000;\n          margin-top: 0.5rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .user-dropdown a,\n        .user-dropdown button {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 0.75rem 1rem;\n          color: #374151;\n          text-decoration: none;\n          border-radius: 0.5rem;\n          transition: all 0.2s;\n          width: 100%;\n          border: none;\n          background: none;\n          cursor: pointer;\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        .user-dropdown a:hover,\n        .user-dropdown button:hover {\n          background: var(--bg-secondary);\n          color: var(--primary-color);\n        }\n\n        .mobile-menu-btn {\n          display: none;\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          color: white;\n          padding: 0.75rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-size: 1.25rem;\n          transition: all 0.2s;\n        }\n\n        .mobile-menu-btn:hover {\n          background: rgba(255, 255, 255, 0.2);\n        }\n\n        .mobile-menu {\n          position: absolute;\n          top: 100%;\n          left: 0;\n          right: 0;\n          background: white;\n          border-top: 1px solid var(--border-color);\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\n          z-index: 1000;\n        }\n\n        .mobile-menu-content {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 1rem;\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .mobile-nav-link {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          color: var(--text-primary);\n          text-decoration: none;\n          border-radius: 0.5rem;\n          transition: all 0.2s;\n          font-weight: 500;\n          position: relative;\n        }\n\n        .mobile-nav-link:hover {\n          background: var(--bg-secondary);\n          color: var(--primary-color);\n        }\n\n        .mobile-nav-link.logout-btn {\n          background: none;\n          border: none;\n          width: 100%;\n          text-align: left;\n          cursor: pointer;\n          font-size: 1rem;\n        }\n\n        .mobile-nav-link i {\n          width: 20px;\n          text-align: center;\n        }\n\n        @media (max-width: 768px) {\n          .desktop-nav {\n            display: none;\n          }\n\n          .mobile-menu-btn {\n            display: block;\n          }\n\n          .nav-link span {\n            display: none;\n          }\n\n          .header-content {\n            padding: 0 1rem;\n          }\n\n          header h1 {\n            font-size: 1.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .nav-link span {\n            display: none;\n          }\n\n          .user-button span {\n            display: none;\n          }\n\n          .desktop-nav {\n            gap: 1rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"ml0VBIGIfkaqsomPmYWe4jt02Vo=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "Link", "CartContext", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "_s", "cart", "user", "logout", "showUserMenu", "setShowUserMenu", "showMobileMenu", "setShowMobileMenu", "cartItemCount", "reduce", "total", "item", "quantity", "useEffect", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "name", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/Header.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { CartContext } from '../context/CartContext';\r\nimport { AuthContext } from '../context/AuthContext';\r\n\r\nfunction Header() {\r\n  const { cart } = useContext(CartContext);\r\n  const { user, logout } = useContext(AuthContext);\r\n  const [showUserMenu, setShowUserMenu] = useState(false);\r\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\r\n\r\n  const cartItemCount = cart.reduce((total, item) => total + item.quantity, 0);\r\n\r\n  // Close menus when clicking outside\r\n  React.useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (!event.target.closest('.user-menu') && !event.target.closest('.mobile-menu')) {\r\n        setShowUserMenu(false);\r\n        setShowMobileMenu(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('click', handleClickOutside);\r\n    return () => document.removeEventListener('click', handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <header>\r\n      <div className=\"header-content\">\r\n        <h1>\r\n          <Link to=\"/\">\r\n            <i className=\"fas fa-leaf\"></i>\r\n            EcoCommerce\r\n          </Link>\r\n        </h1>\r\n\r\n        {/* Desktop Navigation */}\r\n        <nav className=\"desktop-nav\">\r\n          <Link to=\"/search\" className=\"nav-link\">\r\n            <i className=\"fas fa-search\"></i>\r\n            <span>Search</span>\r\n          </Link>\r\n          <Link to=\"/cart\" className=\"nav-link cart-link\">\r\n            <i className=\"fas fa-shopping-cart\"></i>\r\n            <span>Cart</span>\r\n            {cartItemCount > 0 && (\r\n              <span className=\"cart-badge\">{cartItemCount}</span>\r\n            )}\r\n          </Link>\r\n          <Link to=\"/admin\" className=\"nav-link admin-link\">\r\n            <i className=\"fas fa-cog\"></i>\r\n            <span>Admin</span>\r\n          </Link>\r\n          {user ? (\r\n            <div className=\"user-menu\">\r\n              <button\r\n                className=\"user-button\"\r\n                onClick={() => setShowUserMenu(!showUserMenu)}\r\n              >\r\n                <i className=\"fas fa-user\"></i>\r\n                <span>{user.name}</span>\r\n                <i className=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              {showUserMenu && (\r\n                <div className=\"user-dropdown\">\r\n                  <Link to=\"/profile\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-user-circle\"></i>\r\n                    Profile\r\n                  </Link>\r\n                  <Link to=\"/wishlist\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-heart\"></i>\r\n                    Wishlist\r\n                  </Link>\r\n                  <Link to=\"/orders\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-box\"></i>\r\n                    Orders\r\n                  </Link>\r\n                  <button onClick={() => { logout(); setShowUserMenu(false); }}>\r\n                    <i className=\"fas fa-sign-out-alt\"></i>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <Link to=\"/login\" className=\"nav-link\">\r\n              <i className=\"fas fa-sign-in-alt\"></i>\r\n              <span>Login</span>\r\n            </Link>\r\n          )}\r\n        </nav>\r\n\r\n        {/* Mobile Menu Button */}\r\n        <button\r\n          className=\"mobile-menu-btn\"\r\n          onClick={() => setShowMobileMenu(!showMobileMenu)}\r\n        >\r\n          <i className={`fas ${showMobileMenu ? 'fa-times' : 'fa-bars'}`}></i>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Mobile Navigation */}\r\n      {showMobileMenu && (\r\n        <div className=\"mobile-menu\">\r\n          <div className=\"mobile-menu-content\">\r\n            <Link\r\n              to=\"/search\"\r\n              className=\"mobile-nav-link\"\r\n              onClick={() => setShowMobileMenu(false)}\r\n            >\r\n              <i className=\"fas fa-search\"></i>\r\n              Search\r\n            </Link>\r\n            <Link\r\n              to=\"/cart\"\r\n              className=\"mobile-nav-link\"\r\n              onClick={() => setShowMobileMenu(false)}\r\n            >\r\n              <i className=\"fas fa-shopping-cart\"></i>\r\n              Cart\r\n              {cartItemCount > 0 && (\r\n                <span className=\"cart-badge\">{cartItemCount}</span>\r\n              )}\r\n            </Link>\r\n            {user ? (\r\n              <>\r\n                <Link\r\n                  to=\"/profile\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-user-circle\"></i>\r\n                  Profile\r\n                </Link>\r\n                <Link\r\n                  to=\"/wishlist\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-heart\"></i>\r\n                  Wishlist\r\n                </Link>\r\n                <Link\r\n                  to=\"/orders\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-box\"></i>\r\n                  Orders\r\n                </Link>\r\n                <button\r\n                  className=\"mobile-nav-link logout-btn\"\r\n                  onClick={() => { logout(); setShowMobileMenu(false); }}\r\n                >\r\n                  <i className=\"fas fa-sign-out-alt\"></i>\r\n                  Logout\r\n                </button>\r\n              </>\r\n            ) : (\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"mobile-nav-link\"\r\n                onClick={() => setShowMobileMenu(false)}\r\n              >\r\n                <i className=\"fas fa-sign-in-alt\"></i>\r\n                Login\r\n              </Link>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <style jsx>{`\r\n        header {\r\n          position: relative;\r\n        }\r\n\r\n        .header-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          max-width: 1400px;\r\n          margin: 0 auto;\r\n          padding: 0 1rem;\r\n        }\r\n\r\n        .desktop-nav {\r\n          display: flex;\r\n          gap: 2rem;\r\n          align-items: center;\r\n        }\r\n\r\n        .nav-link {\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .nav-link span {\r\n          display: block;\r\n        }\r\n\r\n        .cart-link {\r\n          position: relative;\r\n        }\r\n\r\n        .cart-badge {\r\n          position: absolute;\r\n          top: -8px;\r\n          right: -8px;\r\n          background: #ef4444;\r\n          color: white;\r\n          border-radius: 50%;\r\n          width: 20px;\r\n          height: 20px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 0.75rem;\r\n          font-weight: 600;\r\n          z-index: 10;\r\n        }\r\n\r\n        .user-menu {\r\n          position: relative;\r\n        }\r\n\r\n        .user-button {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n          color: white;\r\n          padding: 0.5rem 1rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          font-weight: 500;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .user-button:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n        }\r\n\r\n        .user-dropdown {\r\n          position: absolute;\r\n          top: 100%;\r\n          right: 0;\r\n          background: white;\r\n          border-radius: 0.75rem;\r\n          box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);\r\n          padding: 0.75rem;\r\n          min-width: 220px;\r\n          z-index: 1000;\r\n          margin-top: 0.5rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .user-dropdown a,\r\n        .user-dropdown button {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          padding: 0.75rem 1rem;\r\n          color: #374151;\r\n          text-decoration: none;\r\n          border-radius: 0.5rem;\r\n          transition: all 0.2s;\r\n          width: 100%;\r\n          border: none;\r\n          background: none;\r\n          cursor: pointer;\r\n          font-size: 0.875rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .user-dropdown a:hover,\r\n        .user-dropdown button:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .mobile-menu-btn {\r\n          display: none;\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n          color: white;\r\n          padding: 0.75rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-size: 1.25rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .mobile-menu-btn:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n        }\r\n\r\n        .mobile-menu {\r\n          position: absolute;\r\n          top: 100%;\r\n          left: 0;\r\n          right: 0;\r\n          background: white;\r\n          border-top: 1px solid var(--border-color);\r\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\r\n          z-index: 1000;\r\n        }\r\n\r\n        .mobile-menu-content {\r\n          max-width: 1400px;\r\n          margin: 0 auto;\r\n          padding: 1rem;\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .mobile-nav-link {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 1rem;\r\n          padding: 1rem;\r\n          color: var(--text-primary);\r\n          text-decoration: none;\r\n          border-radius: 0.5rem;\r\n          transition: all 0.2s;\r\n          font-weight: 500;\r\n          position: relative;\r\n        }\r\n\r\n        .mobile-nav-link:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .mobile-nav-link.logout-btn {\r\n          background: none;\r\n          border: none;\r\n          width: 100%;\r\n          text-align: left;\r\n          cursor: pointer;\r\n          font-size: 1rem;\r\n        }\r\n\r\n        .mobile-nav-link i {\r\n          width: 20px;\r\n          text-align: center;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .desktop-nav {\r\n            display: none;\r\n          }\r\n\r\n          .mobile-menu-btn {\r\n            display: block;\r\n          }\r\n\r\n          .nav-link span {\r\n            display: none;\r\n          }\r\n\r\n          .header-content {\r\n            padding: 0 1rem;\r\n          }\r\n\r\n          header h1 {\r\n            font-size: 1.5rem;\r\n          }\r\n        }\r\n\r\n        @media (max-width: 480px) {\r\n          .nav-link span {\r\n            display: none;\r\n          }\r\n\r\n          .user-button span {\r\n            display: none;\r\n          }\r\n\r\n          .desktop-nav {\r\n            gap: 1rem;\r\n          }\r\n        }\r\n      `}</style>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM;IAAEC;EAAK,CAAC,GAAGX,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM;IAAES,IAAI;IAAEC;EAAO,CAAC,GAAGb,UAAU,CAACI,WAAW,CAAC;EAChD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMiB,aAAa,GAAGP,IAAI,CAACQ,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;;EAE5E;EACAvB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,CAACF,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;QAChFZ,eAAe,CAAC,KAAK,CAAC;QACtBE,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDW,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,kBAAkB,CAAC;IACtD,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElB,OAAA;IAAAyB,QAAA,gBACEzB,OAAA;MAAK0B,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBAC7BzB,OAAA;QAAAyB,QAAA,eACEzB,OAAA,CAACJ,IAAI;UAAC+B,EAAE,EAAC,GAAG;UAAAF,QAAA,gBACVzB,OAAA;YAAG0B,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGL/B,OAAA;QAAK0B,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1BzB,OAAA,CAACJ,IAAI;UAAC+B,EAAE,EAAC,SAAS;UAACD,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACrCzB,OAAA;YAAG0B,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC/B,OAAA;YAAAyB,QAAA,EAAM;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACP/B,OAAA,CAACJ,IAAI;UAAC+B,EAAE,EAAC,OAAO;UAACD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBAC7CzB,OAAA;YAAG0B,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC/B,OAAA;YAAAyB,QAAA,EAAM;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAChBnB,aAAa,GAAG,CAAC,iBAChBZ,OAAA;YAAM0B,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEb;UAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACP/B,OAAA,CAACJ,IAAI;UAAC+B,EAAE,EAAC,QAAQ;UAACD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAC/CzB,OAAA;YAAG0B,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B/B,OAAA;YAAAyB,QAAA,EAAM;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,EACNzB,IAAI,gBACHN,OAAA;UAAK0B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBzB,OAAA;YACE0B,SAAS,EAAC,aAAa;YACvBM,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,CAACD,YAAY,CAAE;YAAAiB,QAAA,gBAE9CzB,OAAA;cAAG0B,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B/B,OAAA;cAAAyB,QAAA,EAAOnB,IAAI,CAAC2B;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxB/B,OAAA;cAAG0B,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EACRvB,YAAY,iBACXR,OAAA;YAAK0B,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC5BzB,OAAA,CAACJ,IAAI;cAAC+B,EAAE,EAAC,UAAU;cAACK,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,KAAK,CAAE;cAAAgB,QAAA,gBACxDzB,OAAA;gBAAG0B,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;cAAC+B,EAAE,EAAC,WAAW;cAACK,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,KAAK,CAAE;cAAAgB,QAAA,gBACzDzB,OAAA;gBAAG0B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;cAAC+B,EAAE,EAAC,SAAS;cAACK,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,KAAK,CAAE;cAAAgB,QAAA,gBACvDzB,OAAA;gBAAG0B,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP/B,OAAA;cAAQgC,OAAO,EAAEA,CAAA,KAAM;gBAAEzB,MAAM,CAAC,CAAC;gBAAEE,eAAe,CAAC,KAAK,CAAC;cAAE,CAAE;cAAAgB,QAAA,gBAC3DzB,OAAA;gBAAG0B,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN/B,OAAA,CAACJ,IAAI;UAAC+B,EAAE,EAAC,QAAQ;UAACD,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACpCzB,OAAA;YAAG0B,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC/B,OAAA;YAAAyB,QAAA,EAAM;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/B,OAAA;QACE0B,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,CAACD,cAAc,CAAE;QAAAe,QAAA,eAElDzB,OAAA;UAAG0B,SAAS,EAAE,OAAOhB,cAAc,GAAG,UAAU,GAAG,SAAS;QAAG;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLrB,cAAc,iBACbV,OAAA;MAAK0B,SAAS,EAAC,aAAa;MAAAD,QAAA,eAC1BzB,OAAA;QAAK0B,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAClCzB,OAAA,CAACJ,IAAI;UACH+B,EAAE,EAAC,SAAS;UACZD,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;UAAAc,QAAA,gBAExCzB,OAAA;YAAG0B,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,UAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;UACH+B,EAAE,EAAC,OAAO;UACVD,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;UAAAc,QAAA,gBAExCzB,OAAA;YAAG0B,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,QAExC,EAACnB,aAAa,GAAG,CAAC,iBAChBZ,OAAA;YAAM0B,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEb;UAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EACNzB,IAAI,gBACHN,OAAA,CAAAE,SAAA;UAAAuB,QAAA,gBACEzB,OAAA,CAACJ,IAAI;YACH+B,EAAE,EAAC,UAAU;YACbD,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;YAAAc,QAAA,gBAExCzB,OAAA;cAAG0B,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;YACH+B,EAAE,EAAC,WAAW;YACdD,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;YAAAc,QAAA,gBAExCzB,OAAA;cAAG0B,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,YAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;YACH+B,EAAE,EAAC,SAAS;YACZD,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;YAAAc,QAAA,gBAExCzB,OAAA;cAAG0B,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/B,OAAA;YACE0B,SAAS,EAAC,4BAA4B;YACtCM,OAAO,EAAEA,CAAA,KAAM;cAAEzB,MAAM,CAAC,CAAC;cAAEI,iBAAiB,CAAC,KAAK,CAAC;YAAE,CAAE;YAAAc,QAAA,gBAEvDzB,OAAA;cAAG0B,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEH/B,OAAA,CAACJ,IAAI;UACH+B,EAAE,EAAC,QAAQ;UACXD,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,KAAK,CAAE;UAAAc,QAAA,gBAExCzB,OAAA;YAAG0B,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,SAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/B,OAAA;MAAOkC,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAAC3B,EAAA,CAjYQD,MAAM;AAAAgC,EAAA,GAANhC,MAAM;AAmYf,eAAeA,MAAM;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}