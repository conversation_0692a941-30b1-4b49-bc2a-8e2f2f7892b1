{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\SignOutPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction SignOutPage() {\n  _s();\n  const {\n    logout,\n    user\n  } = useContext(AuthContext);\n  const navigate = useNavigate();\n  const [countdown, setCountdown] = useState(5);\n  const [isSignedOut, setIsSignedOut] = useState(false);\n  useEffect(() => {\n    // Auto sign out after component mounts\n    const signOutTimer = setTimeout(() => {\n      logout();\n      setIsSignedOut(true);\n    }, 1000);\n    return () => clearTimeout(signOutTimer);\n  }, [logout]);\n  useEffect(() => {\n    if (isSignedOut) {\n      // Start countdown after sign out\n      const countdownTimer = setInterval(() => {\n        setCountdown(prev => {\n          if (prev <= 1) {\n            clearInterval(countdownTimer);\n            navigate('/');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n      return () => clearInterval(countdownTimer);\n    }\n  }, [isSignedOut, navigate]);\n  const handleGoHome = () => {\n    navigate('/');\n  };\n  const handleSignIn = () => {\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"signout-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"signout-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"signout-card\",\n        children: !isSignedOut ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"signout-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-sign-out-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Signing you out...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please wait while we securely sign you out of your account.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Successfully Signed Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Thank you for shopping with EcoStore! You have been securely signed out.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Hope to see you again soon! \\uD83D\\uDC4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"redirect-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Redirecting to home page in \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"countdown\",\n                children: countdown\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 48\n              }, this), \" seconds...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoHome,\n              className: \"btn-primary\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this), \"Go to Home\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignIn,\n              className: \"btn-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-in-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), \"Sign In Again\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"links-grid\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                className: \"quick-link\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-bag\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Browse Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                className: \"quick-link\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-info-circle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"About Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"quick-link\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-envelope\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/help\",\n                className: \"quick-link\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-question-circle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Help\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .signout-page {\n          min-height: 100vh;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 2rem;\n        }\n\n        .signout-container {\n          width: 100%;\n          max-width: 500px;\n        }\n\n        .signout-card {\n          background: white;\n          border-radius: 2rem;\n          padding: 3rem;\n          text-align: center;\n          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n          animation: slideUp 0.6s ease-out;\n        }\n\n        @keyframes slideUp {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .signout-icon {\n          width: 80px;\n          height: 80px;\n          background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 2rem auto;\n          color: white;\n          font-size: 2rem;\n        }\n\n        .success-icon {\n          width: 80px;\n          height: 80px;\n          background: linear-gradient(135deg, #00b894, #00a085);\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 2rem auto;\n          color: white;\n          font-size: 2rem;\n          animation: checkmark 0.6s ease-in-out;\n        }\n\n        @keyframes checkmark {\n          0% {\n            transform: scale(0);\n          }\n          50% {\n            transform: scale(1.2);\n          }\n          100% {\n            transform: scale(1);\n          }\n        }\n\n        .signout-card h2 {\n          margin: 0 0 1rem 0;\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n        }\n\n        .signout-card p {\n          margin: 0 0 2rem 0;\n          color: var(--text-secondary);\n          font-size: 1.125rem;\n          line-height: 1.6;\n        }\n\n        .loading-spinner {\n          margin: 2rem 0;\n        }\n\n        .spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #f3f3f3;\n          border-top: 4px solid var(--primary-color);\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        .user-info {\n          background: var(--bg-secondary);\n          padding: 1.5rem;\n          border-radius: 1rem;\n          margin: 2rem 0;\n        }\n\n        .user-info p {\n          margin: 0;\n          font-size: 1.125rem;\n          color: var(--text-primary);\n        }\n\n        .redirect-info {\n          background: #e3f2fd;\n          padding: 1rem;\n          border-radius: 0.5rem;\n          margin: 2rem 0;\n          border-left: 4px solid var(--primary-color);\n        }\n\n        .redirect-info p {\n          margin: 0;\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .countdown {\n          font-weight: 700;\n          color: var(--primary-color);\n          font-size: 1.125rem;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n          margin: 2rem 0;\n        }\n\n        .btn-primary, .btn-secondary {\n          flex: 1;\n          padding: 1rem 1.5rem;\n          border-radius: 0.75rem;\n          font-weight: 600;\n          font-size: 1rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          text-decoration: none;\n          border: none;\n        }\n\n        .btn-primary {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: var(--primary-dark);\n          transform: translateY(-2px);\n        }\n\n        .btn-secondary {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n        }\n\n        .btn-secondary:hover {\n          background: var(--border-color);\n          transform: translateY(-2px);\n        }\n\n        .quick-links {\n          margin-top: 3rem;\n          padding-top: 2rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .quick-links h4 {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .links-grid {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          gap: 1rem;\n        }\n\n        .quick-link {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 1rem;\n          background: var(--bg-secondary);\n          border-radius: 0.75rem;\n          text-decoration: none;\n          color: var(--text-secondary);\n          transition: all 0.2s;\n          border: 1px solid var(--border-color);\n        }\n\n        .quick-link:hover {\n          background: var(--primary-color);\n          color: white;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .quick-link i {\n          font-size: 1.25rem;\n        }\n\n        .quick-link span {\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        @media (max-width: 768px) {\n          .signout-page {\n            padding: 1rem;\n          }\n\n          .signout-card {\n            padding: 2rem;\n          }\n\n          .action-buttons {\n            flex-direction: column;\n          }\n\n          .links-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .signout-card h2 {\n            font-size: 1.5rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\n_s(SignOutPage, \"qK4N0ebEmPGK58P662kN74IBf+0=\", false, function () {\n  return [useNavigate];\n});\n_c = SignOutPage;\nexport default SignOutPage;\nvar _c;\n$RefreshReg$(_c, \"SignOutPage\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Link", "useNavigate", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SignOutPage", "_s", "logout", "user", "navigate", "countdown", "setCountdown", "isSignedOut", "setIsSignedOut", "signOutTimer", "setTimeout", "clearTimeout", "countdownTimer", "setInterval", "prev", "clearInterval", "handleGoHome", "handleSignIn", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "to", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/SignOutPage.jsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../context/AuthContext';\n\nfunction SignOutPage() {\n  const { logout, user } = useContext(AuthContext);\n  const navigate = useNavigate();\n  const [countdown, setCountdown] = useState(5);\n  const [isSignedOut, setIsSignedOut] = useState(false);\n\n  useEffect(() => {\n    // Auto sign out after component mounts\n    const signOutTimer = setTimeout(() => {\n      logout();\n      setIsSignedOut(true);\n    }, 1000);\n\n    return () => clearTimeout(signOutTimer);\n  }, [logout]);\n\n  useEffect(() => {\n    if (isSignedOut) {\n      // Start countdown after sign out\n      const countdownTimer = setInterval(() => {\n        setCountdown(prev => {\n          if (prev <= 1) {\n            clearInterval(countdownTimer);\n            navigate('/');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(countdownTimer);\n    }\n  }, [isSignedOut, navigate]);\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  const handleSignIn = () => {\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"signout-page\">\n      <div className=\"signout-container\">\n        <div className=\"signout-card\">\n          {!isSignedOut ? (\n            <>\n              <div className=\"signout-icon\">\n                <i className=\"fas fa-sign-out-alt\"></i>\n              </div>\n              <h2>Signing you out...</h2>\n              <p>Please wait while we securely sign you out of your account.</p>\n              <div className=\"loading-spinner\">\n                <div className=\"spinner\"></div>\n              </div>\n            </>\n          ) : (\n            <>\n              <div className=\"success-icon\">\n                <i className=\"fas fa-check-circle\"></i>\n              </div>\n              <h2>Successfully Signed Out</h2>\n              <p>Thank you for shopping with EcoStore! You have been securely signed out.</p>\n              \n              <div className=\"user-info\">\n                <p>Hope to see you again soon! 👋</p>\n              </div>\n\n              <div className=\"redirect-info\">\n                <p>Redirecting to home page in <span className=\"countdown\">{countdown}</span> seconds...</p>\n              </div>\n\n              <div className=\"action-buttons\">\n                <button onClick={handleGoHome} className=\"btn-primary\">\n                  <i className=\"fas fa-home\"></i>\n                  Go to Home\n                </button>\n                <button onClick={handleSignIn} className=\"btn-secondary\">\n                  <i className=\"fas fa-sign-in-alt\"></i>\n                  Sign In Again\n                </button>\n              </div>\n\n              <div className=\"quick-links\">\n                <h4>Quick Links</h4>\n                <div className=\"links-grid\">\n                  <Link to=\"/products\" className=\"quick-link\">\n                    <i className=\"fas fa-shopping-bag\"></i>\n                    <span>Browse Products</span>\n                  </Link>\n                  <Link to=\"/about\" className=\"quick-link\">\n                    <i className=\"fas fa-info-circle\"></i>\n                    <span>About Us</span>\n                  </Link>\n                  <Link to=\"/contact\" className=\"quick-link\">\n                    <i className=\"fas fa-envelope\"></i>\n                    <span>Contact</span>\n                  </Link>\n                  <Link to=\"/help\" className=\"quick-link\">\n                    <i className=\"fas fa-question-circle\"></i>\n                    <span>Help</span>\n                  </Link>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n\n      <style jsx>{`\n        .signout-page {\n          min-height: 100vh;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 2rem;\n        }\n\n        .signout-container {\n          width: 100%;\n          max-width: 500px;\n        }\n\n        .signout-card {\n          background: white;\n          border-radius: 2rem;\n          padding: 3rem;\n          text-align: center;\n          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n          animation: slideUp 0.6s ease-out;\n        }\n\n        @keyframes slideUp {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .signout-icon {\n          width: 80px;\n          height: 80px;\n          background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 2rem auto;\n          color: white;\n          font-size: 2rem;\n        }\n\n        .success-icon {\n          width: 80px;\n          height: 80px;\n          background: linear-gradient(135deg, #00b894, #00a085);\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 2rem auto;\n          color: white;\n          font-size: 2rem;\n          animation: checkmark 0.6s ease-in-out;\n        }\n\n        @keyframes checkmark {\n          0% {\n            transform: scale(0);\n          }\n          50% {\n            transform: scale(1.2);\n          }\n          100% {\n            transform: scale(1);\n          }\n        }\n\n        .signout-card h2 {\n          margin: 0 0 1rem 0;\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n        }\n\n        .signout-card p {\n          margin: 0 0 2rem 0;\n          color: var(--text-secondary);\n          font-size: 1.125rem;\n          line-height: 1.6;\n        }\n\n        .loading-spinner {\n          margin: 2rem 0;\n        }\n\n        .spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #f3f3f3;\n          border-top: 4px solid var(--primary-color);\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        .user-info {\n          background: var(--bg-secondary);\n          padding: 1.5rem;\n          border-radius: 1rem;\n          margin: 2rem 0;\n        }\n\n        .user-info p {\n          margin: 0;\n          font-size: 1.125rem;\n          color: var(--text-primary);\n        }\n\n        .redirect-info {\n          background: #e3f2fd;\n          padding: 1rem;\n          border-radius: 0.5rem;\n          margin: 2rem 0;\n          border-left: 4px solid var(--primary-color);\n        }\n\n        .redirect-info p {\n          margin: 0;\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .countdown {\n          font-weight: 700;\n          color: var(--primary-color);\n          font-size: 1.125rem;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n          margin: 2rem 0;\n        }\n\n        .btn-primary, .btn-secondary {\n          flex: 1;\n          padding: 1rem 1.5rem;\n          border-radius: 0.75rem;\n          font-weight: 600;\n          font-size: 1rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          text-decoration: none;\n          border: none;\n        }\n\n        .btn-primary {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: var(--primary-dark);\n          transform: translateY(-2px);\n        }\n\n        .btn-secondary {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n        }\n\n        .btn-secondary:hover {\n          background: var(--border-color);\n          transform: translateY(-2px);\n        }\n\n        .quick-links {\n          margin-top: 3rem;\n          padding-top: 2rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .quick-links h4 {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .links-grid {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          gap: 1rem;\n        }\n\n        .quick-link {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 1rem;\n          background: var(--bg-secondary);\n          border-radius: 0.75rem;\n          text-decoration: none;\n          color: var(--text-secondary);\n          transition: all 0.2s;\n          border: 1px solid var(--border-color);\n        }\n\n        .quick-link:hover {\n          background: var(--primary-color);\n          color: white;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .quick-link i {\n          font-size: 1.25rem;\n        }\n\n        .quick-link span {\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        @media (max-width: 768px) {\n          .signout-page {\n            padding: 1rem;\n          }\n\n          .signout-card {\n            padding: 2rem;\n          }\n\n          .action-buttons {\n            flex-direction: column;\n          }\n\n          .links-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .signout-card h2 {\n            font-size: 1.5rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default SignOutPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGb,UAAU,CAACK,WAAW,CAAC;EAChD,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAErDD,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,YAAY,GAAGC,UAAU,CAAC,MAAM;MACpCR,MAAM,CAAC,CAAC;MACRM,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,YAAY,CAACF,YAAY,CAAC;EACzC,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;EAEZX,SAAS,CAAC,MAAM;IACd,IAAIgB,WAAW,EAAE;MACf;MACA,MAAMK,cAAc,GAAGC,WAAW,CAAC,MAAM;QACvCP,YAAY,CAACQ,IAAI,IAAI;UACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;YACbC,aAAa,CAACH,cAAc,CAAC;YAC7BR,QAAQ,CAAC,GAAG,CAAC;YACb,OAAO,CAAC;UACV;UACA,OAAOU,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMC,aAAa,CAACH,cAAc,CAAC;IAC5C;EACF,CAAC,EAAE,CAACL,WAAW,EAAEH,QAAQ,CAAC,CAAC;EAE3B,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBZ,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBb,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEP,OAAA;IAAKqB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BtB,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCtB,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B,CAACZ,WAAW,gBACXV,OAAA,CAAAE,SAAA;UAAAoB,QAAA,gBACEtB,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BtB,OAAA;cAAGqB,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACN1B,OAAA;YAAAsB,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B1B,OAAA;YAAAsB,QAAA,EAAG;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClE1B,OAAA;YAAKqB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BtB,OAAA;cAAKqB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA,eACN,CAAC,gBAEH1B,OAAA,CAAAE,SAAA;UAAAoB,QAAA,gBACEtB,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BtB,OAAA;cAAGqB,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACN1B,OAAA;YAAAsB,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC1B,OAAA;YAAAsB,QAAA,EAAG;UAAwE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/E1B,OAAA;YAAKqB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBtB,OAAA;cAAAsB,QAAA,EAAG;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEN1B,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BtB,OAAA;cAAAsB,QAAA,GAAG,8BAA4B,eAAAtB,OAAA;gBAAMqB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEd;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAAW;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAEN1B,OAAA;YAAKqB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BtB,OAAA;cAAQ2B,OAAO,EAAER,YAAa;cAACE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACpDtB,OAAA;gBAAGqB,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,cAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1B,OAAA;cAAQ2B,OAAO,EAAEP,YAAa;cAACC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBACtDtB,OAAA;gBAAGqB,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1B,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BtB,OAAA;cAAAsB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB1B,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA,CAACJ,IAAI;gBAACgC,EAAE,EAAC,WAAW;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzCtB,OAAA;kBAAGqB,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvC1B,OAAA;kBAAAsB,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACP1B,OAAA,CAACJ,IAAI;gBAACgC,EAAE,EAAC,QAAQ;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACtCtB,OAAA;kBAAGqB,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtC1B,OAAA;kBAAAsB,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACP1B,OAAA,CAACJ,IAAI;gBAACgC,EAAE,EAAC,UAAU;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxCtB,OAAA;kBAAGqB,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnC1B,OAAA;kBAAAsB,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACP1B,OAAA,CAACJ,IAAI;gBAACgC,EAAE,EAAC,OAAO;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACrCtB,OAAA;kBAAGqB,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1C1B,OAAA;kBAAAsB,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1B,OAAA;MAAO6B,GAAG;MAAAP,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACtB,EAAA,CA9WQD,WAAW;EAAA,QAEDN,WAAW;AAAA;AAAAiC,EAAA,GAFrB3B,WAAW;AAgXpB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}