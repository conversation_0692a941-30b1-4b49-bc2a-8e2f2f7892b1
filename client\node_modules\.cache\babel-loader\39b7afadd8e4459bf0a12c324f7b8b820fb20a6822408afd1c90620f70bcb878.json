{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\ProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductPage() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const {\n    addToCart\n  } = useContext(CartContext);\n  const {\n    wishlist,\n    addToWishlist,\n    removeFromWishlist\n  } = useContext(WishlistContext) || {\n    wishlist: [],\n    addToWishlist: () => {},\n    removeFromWishlist: () => {}\n  };\n  const isInWishlist = wishlist.some(item => item._id === (product === null || product === void 0 ? void 0 : product._id));\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    fetchProduct();\n  }, [id]);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 23\n  }, this);\n  if (!product) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Product not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 24\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '800px',\n      margin: '0 auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '30px',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          minWidth: '300px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.imageUrl || '/placeholder-image.jpg',\n          alt: product.name,\n          style: {\n            width: '100%',\n            maxWidth: '400px',\n            height: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          minWidth: '300px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '24px',\n            color: '#28a745',\n            fontWeight: 'bold'\n          },\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), product.isEcoFriendly && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#28a745',\n            marginBottom: '10px'\n          },\n          children: \"\\uD83C\\uDF31 Eco-Friendly Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), product.tags && product.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tags: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), product.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#e9ecef',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              marginRight: '5px'\n            },\n            children: tag\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => addToCart(product),\n          style: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '16px'\n          },\n          children: \"Add to Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductPage, \"RhfkAgY+CQXW7+QJvPGPeDzSrxM=\", false, function () {\n  return [useParams];\n});\n_c = ProductPage;\nexport default ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useParams", "CartContext", "WishlistContext", "ProductReviews", "axios", "jsxDEV", "_jsxDEV", "ProductPage", "_s", "id", "product", "setProduct", "loading", "setLoading", "quantity", "setQuantity", "addToCart", "wishlist", "addToWishlist", "removeFromWishlist", "isInWishlist", "some", "item", "_id", "fetchProduct", "response", "get", "data", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "display", "gap", "flexWrap", "flex", "min<PERSON><PERSON><PERSON>", "src", "imageUrl", "alt", "name", "width", "height", "fontSize", "color", "fontWeight", "price", "isEcoFriendly", "marginBottom", "description", "tags", "length", "map", "tag", "index", "backgroundColor", "borderRadius", "marginRight", "onClick", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { WishlistContext } from '../context/WishlistContext';\nimport ProductReviews from '../components/ProductReviews';\nimport axios from 'axios';\n\nfunction ProductPage() {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const { addToCart } = useContext(CartContext);\n  const { wishlist, addToWishlist, removeFromWishlist } = useContext(WishlistContext) || { wishlist: [], addToWishlist: () => {}, removeFromWishlist: () => {} };\n\n  const isInWishlist = wishlist.some(item => item._id === product?._id);\n\n  useEffect(() => {\n    async function fetchProduct() {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/products/${id}`);\n        setProduct(response.data);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n      } finally {\n        setLoading(false);\n      }\n    }\n    \n    fetchProduct();\n  }, [id]);\n\n  if (loading) return <div>Loading...</div>;\n  if (!product) return <div>Product not found</div>;\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <div style={{ display: 'flex', gap: '30px', flexWrap: 'wrap' }}>\n        <div style={{ flex: '1', minWidth: '300px' }}>\n          <img \n            src={product.imageUrl || '/placeholder-image.jpg'} \n            alt={product.name}\n            style={{ width: '100%', maxWidth: '400px', height: 'auto' }}\n          />\n        </div>\n        <div style={{ flex: '1', minWidth: '300px' }}>\n          <h1>{product.name}</h1>\n          <p style={{ fontSize: '24px', color: '#28a745', fontWeight: 'bold' }}>\n            ${product.price}\n          </p>\n          {product.isEcoFriendly && (\n            <div style={{ color: '#28a745', marginBottom: '10px' }}>\n              🌱 Eco-Friendly Product\n            </div>\n          )}\n          <p style={{ marginBottom: '20px' }}>{product.description}</p>\n          {product.tags && product.tags.length > 0 && (\n            <div style={{ marginBottom: '20px' }}>\n              <strong>Tags: </strong>\n              {product.tags.map((tag, index) => (\n                <span key={index} style={{ \n                  backgroundColor: '#e9ecef', \n                  padding: '4px 8px', \n                  borderRadius: '4px', \n                  marginRight: '5px' \n                }}>\n                  {tag}\n                </span>\n              ))}\n            </div>\n          )}\n          <button \n            onClick={() => addToCart(product)}\n            style={{\n              backgroundColor: '#007bff',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '16px'\n            }}\n          >\n            Add to Cart\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default ProductPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM;IAAEmB;EAAU,CAAC,GAAGjB,UAAU,CAACE,WAAW,CAAC;EAC7C,MAAM;IAAEgB,QAAQ;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGpB,UAAU,CAACG,eAAe,CAAC,IAAI;IAAEe,QAAQ,EAAE,EAAE;IAAEC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAEC,kBAAkB,EAAEA,CAAA,KAAM,CAAC;EAAE,CAAC;EAE9J,MAAMC,YAAY,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,MAAKb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,GAAG,EAAC;EAErEzB,SAAS,CAAC,MAAM;IACd,eAAe0B,YAAYA,CAAA,EAAG;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,sCAAsCjB,EAAE,EAAE,CAAC;QAC5EE,UAAU,CAACc,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;IAEAW,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACf,EAAE,CAAC,CAAC;EAER,IAAIG,OAAO,EAAE,oBAAON,OAAA;IAAAwB,QAAA,EAAK;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzC,IAAI,CAACxB,OAAO,EAAE,oBAAOJ,OAAA;IAAAwB,QAAA,EAAK;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAEjD,oBACE5B,OAAA;IAAK6B,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,eACnExB,OAAA;MAAK6B,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,GAAG,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAX,QAAA,gBAC7DxB,OAAA;QAAK6B,KAAK,EAAE;UAAEO,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAb,QAAA,eAC3CxB,OAAA;UACEsC,GAAG,EAAElC,OAAO,CAACmC,QAAQ,IAAI,wBAAyB;UAClDC,GAAG,EAAEpC,OAAO,CAACqC,IAAK;UAClBZ,KAAK,EAAE;YAAEa,KAAK,EAAE,MAAM;YAAEX,QAAQ,EAAE,OAAO;YAAEY,MAAM,EAAE;UAAO;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN5B,OAAA;QAAK6B,KAAK,EAAE;UAAEO,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAb,QAAA,gBAC3CxB,OAAA;UAAAwB,QAAA,EAAKpB,OAAO,CAACqC;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvB5B,OAAA;UAAG6B,KAAK,EAAE;YAAEe,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAtB,QAAA,GAAC,GACnE,EAACpB,OAAO,CAAC2C,KAAK;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,EACHxB,OAAO,CAAC4C,aAAa,iBACpBhD,OAAA;UAAK6B,KAAK,EAAE;YAAEgB,KAAK,EAAE,SAAS;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eACD5B,OAAA;UAAG6B,KAAK,EAAE;YAAEoB,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAAEpB,OAAO,CAAC8C;QAAW;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5DxB,OAAO,CAAC+C,IAAI,IAAI/C,OAAO,CAAC+C,IAAI,CAACC,MAAM,GAAG,CAAC,iBACtCpD,OAAA;UAAK6B,KAAK,EAAE;YAAEoB,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,gBACnCxB,OAAA;YAAAwB,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACtBxB,OAAO,CAAC+C,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3BvD,OAAA;YAAkB6B,KAAK,EAAE;cACvB2B,eAAe,EAAE,SAAS;cAC1B1B,OAAO,EAAE,SAAS;cAClB2B,YAAY,EAAE,KAAK;cACnBC,WAAW,EAAE;YACf,CAAE;YAAAlC,QAAA,EACC8B;UAAG,GANKC,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACD5B,OAAA;UACE2D,OAAO,EAAEA,CAAA,KAAMjD,SAAS,CAACN,OAAO,CAAE;UAClCyB,KAAK,EAAE;YACL2B,eAAe,EAAE,SAAS;YAC1BX,KAAK,EAAE,OAAO;YACde,MAAM,EAAE,MAAM;YACd9B,OAAO,EAAE,WAAW;YACpB2B,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,SAAS;YACjBjB,QAAQ,EAAE;UACZ,CAAE;UAAApB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CAlFQD,WAAW;EAAA,QACHP,SAAS;AAAA;AAAAoE,EAAA,GADjB7D,WAAW;AAoFpB,eAAeA,WAAW;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}