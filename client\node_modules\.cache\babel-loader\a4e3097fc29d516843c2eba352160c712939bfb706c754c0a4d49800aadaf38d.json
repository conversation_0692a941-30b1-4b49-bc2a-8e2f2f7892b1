{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\context\\\\UserStatsContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState, useEffect, useContext } from 'react';\nimport { AuthContext } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const UserStatsContext = /*#__PURE__*/createContext();\nexport function UserStatsProvider({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useContext(AuthContext);\n  const [userStats, setUserStats] = useState({\n    totalSpent: 0,\n    totalOrders: 0,\n    loyaltyPoints: 0,\n    ecoImpact: {\n      co2Saved: 12.5,\n      ecoProducts: 8,\n      treesPlanted: 3\n    }\n  });\n  const [orders, setOrders] = useState([{\n    id: 'ORD-001',\n    date: '2024-01-15',\n    status: 'Delivered',\n    total: 7497,\n    items: [{\n      name: 'Eco-Friendly Water Bottle',\n      quantity: 2,\n      price: 2499,\n      imageUrl: 'https://images.unsplash.com/photo-*************-7111542de6e8?w=100'\n    }, {\n      name: 'Organic Cotton T-Shirt',\n      quantity: 1,\n      price: 1999,\n      imageUrl: 'https://images.unsplash.com/photo-*************-6864f9cf17ab?w=100'\n    }],\n    shippingAddress: '123 Main St, City, State 12345',\n    trackingNumber: 'TRK123456789'\n  }, {\n    id: 'ORD-002',\n    date: '2024-01-08',\n    status: 'Shipped',\n    total: 49.99,\n    items: [{\n      name: 'Solar Power Bank',\n      quantity: 1,\n      price: 49.99,\n      imageUrl: 'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=100'\n    }],\n    shippingAddress: '123 Main St, City, State 12345',\n    trackingNumber: 'TRK987654321'\n  }, {\n    id: 'ORD-003',\n    date: '2024-01-01',\n    status: 'Processing',\n    total: 32.98,\n    items: [{\n      name: 'Bamboo Phone Case',\n      quantity: 1,\n      price: 19.99,\n      imageUrl: 'https://images.unsplash.com/photo-**********-08538906a9f8?w=100'\n    }, {\n      name: 'Recycled Notebook',\n      quantity: 1,\n      price: 12.99,\n      imageUrl: 'https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=100'\n    }],\n    shippingAddress: '123 Main St, City, State 12345',\n    trackingNumber: null\n  }]);\n\n  // Calculate stats from orders\n  useEffect(() => {\n    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n    const totalOrders = orders.length;\n    const loyaltyPoints = Math.floor(totalSpent); // 1 point per dollar\n\n    setUserStats(prev => ({\n      ...prev,\n      totalSpent,\n      totalOrders,\n      loyaltyPoints\n    }));\n  }, [orders]);\n  const addOrder = newOrder => {\n    const order = {\n      id: `ORD-${Date.now()}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'Processing',\n      ...newOrder,\n      trackingNumber: null\n    };\n    setOrders(prev => [order, ...prev]);\n\n    // Update eco impact based on eco-friendly products\n    const ecoItems = newOrder.items.filter(item => item.isEcoFriendly);\n    if (ecoItems.length > 0) {\n      setUserStats(prev => ({\n        ...prev,\n        ecoImpact: {\n          ...prev.ecoImpact,\n          co2Saved: prev.ecoImpact.co2Saved + ecoItems.length * 2.5,\n          ecoProducts: prev.ecoImpact.ecoProducts + ecoItems.length,\n          treesPlanted: prev.ecoImpact.treesPlanted + Math.floor(ecoItems.length / 3)\n        }\n      }));\n    }\n  };\n  const updateOrderStatus = (orderId, newStatus, trackingNumber = null) => {\n    setOrders(prev => prev.map(order => order.id === orderId ? {\n      ...order,\n      status: newStatus,\n      trackingNumber\n    } : order));\n  };\n  const redeemReward = pointsCost => {\n    setUserStats(prev => ({\n      ...prev,\n      loyaltyPoints: Math.max(0, prev.loyaltyPoints - pointsCost)\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(UserStatsContext.Provider, {\n    value: {\n      userStats,\n      orders,\n      addOrder,\n      updateOrderStatus,\n      redeemReward,\n      setUserStats\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n}\n_s(UserStatsProvider, \"BFJcSEv4CNSxuLMt6cr0TYyAcmA=\");\n_c = UserStatsProvider;\nvar _c;\n$RefreshReg$(_c, \"UserStatsProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useContext", "AuthContext", "jsxDEV", "_jsxDEV", "UserStatsContext", "UserStatsProvider", "children", "_s", "user", "userStats", "setUserStats", "totalSpent", "totalOrders", "loyaltyPoints", "ecoImpact", "co2Saved", "ecoProducts", "treesPlanted", "orders", "setOrders", "id", "date", "status", "total", "items", "name", "quantity", "price", "imageUrl", "shippingAddress", "trackingNumber", "reduce", "sum", "order", "length", "Math", "floor", "prev", "addOrder", "newOrder", "Date", "now", "toISOString", "split", "ecoItems", "filter", "item", "isEcoFriendly", "updateOrderStatus", "orderId", "newStatus", "map", "redeemReward", "pointsCost", "max", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/UserStatsContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect, useContext } from 'react';\nimport { AuthContext } from './AuthContext';\n\nexport const UserStatsContext = createContext();\n\nexport function UserStatsProvider({ children }) {\n  const { user } = useContext(AuthContext);\n  const [userStats, setUserStats] = useState({\n    totalSpent: 0,\n    totalOrders: 0,\n    loyaltyPoints: 0,\n    ecoImpact: {\n      co2Saved: 12.5,\n      ecoProducts: 8,\n      treesPlanted: 3\n    }\n  });\n\n  const [orders, setOrders] = useState([\n    {\n      id: 'ORD-001',\n      date: '2024-01-15',\n      status: 'Delivered',\n      total: 7497,\n      items: [\n        {\n          name: 'Eco-Friendly Water Bottle',\n          quantity: 2,\n          price: 2499,\n          imageUrl: 'https://images.unsplash.com/photo-*************-7111542de6e8?w=100'\n        },\n        {\n          name: 'Organic Cotton T-Shirt',\n          quantity: 1,\n          price: 1999,\n          imageUrl: 'https://images.unsplash.com/photo-*************-6864f9cf17ab?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: 'TRK123456789'\n    },\n    {\n      id: 'ORD-002',\n      date: '2024-01-08',\n      status: 'Shipped',\n      total: 49.99,\n      items: [\n        { \n          name: 'Solar Power Bank', \n          quantity: 1, \n          price: 49.99,\n          imageUrl: 'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: 'TRK987654321'\n    },\n    {\n      id: 'ORD-003',\n      date: '2024-01-01',\n      status: 'Processing',\n      total: 32.98,\n      items: [\n        { \n          name: 'Bamboo Phone Case', \n          quantity: 1, \n          price: 19.99,\n          imageUrl: 'https://images.unsplash.com/photo-**********-08538906a9f8?w=100'\n        },\n        { \n          name: 'Recycled Notebook', \n          quantity: 1, \n          price: 12.99,\n          imageUrl: 'https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: null\n    }\n  ]);\n\n  // Calculate stats from orders\n  useEffect(() => {\n    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n    const totalOrders = orders.length;\n    const loyaltyPoints = Math.floor(totalSpent); // 1 point per dollar\n\n    setUserStats(prev => ({\n      ...prev,\n      totalSpent,\n      totalOrders,\n      loyaltyPoints\n    }));\n  }, [orders]);\n\n  const addOrder = (newOrder) => {\n    const order = {\n      id: `ORD-${Date.now()}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'Processing',\n      ...newOrder,\n      trackingNumber: null\n    };\n    \n    setOrders(prev => [order, ...prev]);\n    \n    // Update eco impact based on eco-friendly products\n    const ecoItems = newOrder.items.filter(item => item.isEcoFriendly);\n    if (ecoItems.length > 0) {\n      setUserStats(prev => ({\n        ...prev,\n        ecoImpact: {\n          ...prev.ecoImpact,\n          co2Saved: prev.ecoImpact.co2Saved + (ecoItems.length * 2.5),\n          ecoProducts: prev.ecoImpact.ecoProducts + ecoItems.length,\n          treesPlanted: prev.ecoImpact.treesPlanted + Math.floor(ecoItems.length / 3)\n        }\n      }));\n    }\n  };\n\n  const updateOrderStatus = (orderId, newStatus, trackingNumber = null) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: newStatus, trackingNumber }\n        : order\n    ));\n  };\n\n  const redeemReward = (pointsCost) => {\n    setUserStats(prev => ({\n      ...prev,\n      loyaltyPoints: Math.max(0, prev.loyaltyPoints - pointsCost)\n    }));\n  };\n\n  return (\n    <UserStatsContext.Provider value={{ \n      userStats, \n      orders, \n      addOrder, \n      updateOrderStatus,\n      redeemReward,\n      setUserStats\n    }}>\n      {children}\n    </UserStatsContext.Provider>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,OAAO,MAAMC,gBAAgB,gBAAGP,aAAa,CAAC,CAAC;AAE/C,OAAO,SAASQ,iBAAiBA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC9C,MAAM;IAAEC;EAAK,CAAC,GAAGR,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC;IACzCa,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE;MACTC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,CACnC;IACEsB,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,CACL;MACEC,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEH,IAAI,EAAE,wBAAwB;MAC9BC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,eAAe,EAAE,gCAAgC;IACjDC,cAAc,EAAE;EAClB,CAAC,EACD;IACEV,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,CACL;MACEC,IAAI,EAAE,kBAAkB;MACxBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,eAAe,EAAE,gCAAgC;IACjDC,cAAc,EAAE;EAClB,CAAC,EACD;IACEV,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,CACL;MACEC,IAAI,EAAE,mBAAmB;MACzBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEH,IAAI,EAAE,mBAAmB;MACzBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,eAAe,EAAE,gCAAgC;IACjDC,cAAc,EAAE;EAClB,CAAC,CACF,CAAC;;EAEF;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMY,UAAU,GAAGO,MAAM,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACV,KAAK,EAAE,CAAC,CAAC;IACtE,MAAMX,WAAW,GAAGM,MAAM,CAACgB,MAAM;IACjC,MAAMrB,aAAa,GAAGsB,IAAI,CAACC,KAAK,CAACzB,UAAU,CAAC,CAAC,CAAC;;IAE9CD,YAAY,CAAC2B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP1B,UAAU;MACVC,WAAW;MACXC;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACK,MAAM,CAAC,CAAC;EAEZ,MAAMoB,QAAQ,GAAIC,QAAQ,IAAK;IAC7B,MAAMN,KAAK,GAAG;MACZb,EAAE,EAAE,OAAOoB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACvBpB,IAAI,EAAE,IAAImB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CrB,MAAM,EAAE,YAAY;MACpB,GAAGiB,QAAQ;MACXT,cAAc,EAAE;IAClB,CAAC;IAEDX,SAAS,CAACkB,IAAI,IAAI,CAACJ,KAAK,EAAE,GAAGI,IAAI,CAAC,CAAC;;IAEnC;IACA,MAAMO,QAAQ,GAAGL,QAAQ,CAACf,KAAK,CAACqB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,aAAa,CAAC;IAClE,IAAIH,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;MACvBxB,YAAY,CAAC2B,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPvB,SAAS,EAAE;UACT,GAAGuB,IAAI,CAACvB,SAAS;UACjBC,QAAQ,EAAEsB,IAAI,CAACvB,SAAS,CAACC,QAAQ,GAAI6B,QAAQ,CAACV,MAAM,GAAG,GAAI;UAC3DlB,WAAW,EAAEqB,IAAI,CAACvB,SAAS,CAACE,WAAW,GAAG4B,QAAQ,CAACV,MAAM;UACzDjB,YAAY,EAAEoB,IAAI,CAACvB,SAAS,CAACG,YAAY,GAAGkB,IAAI,CAACC,KAAK,CAACQ,QAAQ,CAACV,MAAM,GAAG,CAAC;QAC5E;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMc,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,SAAS,EAAEpB,cAAc,GAAG,IAAI,KAAK;IACvEX,SAAS,CAACkB,IAAI,IAAIA,IAAI,CAACc,GAAG,CAAClB,KAAK,IAC9BA,KAAK,CAACb,EAAE,KAAK6B,OAAO,GAChB;MAAE,GAAGhB,KAAK;MAAEX,MAAM,EAAE4B,SAAS;MAAEpB;IAAe,CAAC,GAC/CG,KACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAIC,UAAU,IAAK;IACnC3C,YAAY,CAAC2B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPxB,aAAa,EAAEsB,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAACxB,aAAa,GAAGwC,UAAU;IAC5D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,oBACElD,OAAA,CAACC,gBAAgB,CAACmD,QAAQ;IAACC,KAAK,EAAE;MAChC/C,SAAS;MACTS,MAAM;MACNoB,QAAQ;MACRU,iBAAiB;MACjBI,YAAY;MACZ1C;IACF,CAAE;IAAAJ,QAAA,EACCA;EAAQ;IAAAmD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC;AAACrD,EAAA,CA/IeF,iBAAiB;AAAAwD,EAAA,GAAjBxD,iBAAiB;AAAA,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}