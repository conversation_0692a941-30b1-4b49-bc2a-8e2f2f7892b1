{"ast": null, "code": "import React,{createContext,useState,useEffect}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";export const AuthContext=/*#__PURE__*/createContext();export function AuthProvider(_ref){let{children}=_ref;const[user,setUser]=useState(()=>JSON.parse(localStorage.getItem('user')));const[token,setToken]=useState(()=>localStorage.getItem('token'));useEffect(()=>{if(user)localStorage.setItem('user',JSON.stringify(user));if(token)localStorage.setItem('token',token);},[user,token]);function logout(){setUser(null);setToken(null);localStorage.removeItem('user');localStorage.removeItem('token');}return/*#__PURE__*/_jsx(AuthContext.Provider,{value:{user,setUser,token,setToken,logout},children:children});}", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "jsx", "_jsx", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "JSON", "parse", "localStorage", "getItem", "token", "setToken", "setItem", "stringify", "logout", "removeItem", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect } from 'react';\r\n\r\nexport const AuthContext = createContext();\r\n\r\nexport function AuthProvider({ children }) {\r\n  const [user, setUser] = useState(() => JSON.parse(localStorage.getItem('user')));\r\n  const [token, setToken] = useState(() => localStorage.getItem('token'));\r\n\r\n  useEffect(() => {\r\n    if (user) localStorage.setItem('user', JSON.stringify(user));\r\n    if (token) localStorage.setItem('token', token);\r\n  }, [user, token]);\r\n\r\n  function logout() {\r\n    setUser(null);\r\n    setToken(null);\r\n    localStorage.removeItem('user');\r\n    localStorage.removeItem('token');\r\n  }\r\n\r\n  return (\r\n    <AuthContext.Provider value={{ user, setUser, token, setToken, logout }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n}"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElE,MAAO,MAAM,CAAAC,WAAW,cAAGL,aAAa,CAAC,CAAC,CAE1C,MAAO,SAAS,CAAAM,YAAYA,CAAAC,IAAA,CAAe,IAAd,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGT,QAAQ,CAAC,IAAMU,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAChF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,IAAMY,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CAEvEZ,SAAS,CAAC,IAAM,CACd,GAAIO,IAAI,CAAEI,YAAY,CAACI,OAAO,CAAC,MAAM,CAAEN,IAAI,CAACO,SAAS,CAACT,IAAI,CAAC,CAAC,CAC5D,GAAIM,KAAK,CAAEF,YAAY,CAACI,OAAO,CAAC,OAAO,CAAEF,KAAK,CAAC,CACjD,CAAC,CAAE,CAACN,IAAI,CAAEM,KAAK,CAAC,CAAC,CAEjB,QAAS,CAAAI,MAAMA,CAAA,CAAG,CAChBT,OAAO,CAAC,IAAI,CAAC,CACbM,QAAQ,CAAC,IAAI,CAAC,CACdH,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC,CAC/BP,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC,CAClC,CAEA,mBACEhB,IAAA,CAACC,WAAW,CAACgB,QAAQ,EAACC,KAAK,CAAE,CAAEb,IAAI,CAAEC,OAAO,CAAEK,KAAK,CAAEC,QAAQ,CAAEG,MAAO,CAAE,CAAAX,QAAA,CACrEA,QAAQ,CACW,CAAC,CAE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}