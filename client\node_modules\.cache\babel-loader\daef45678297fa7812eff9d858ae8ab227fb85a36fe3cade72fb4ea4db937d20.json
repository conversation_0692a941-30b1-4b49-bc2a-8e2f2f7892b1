{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\context\\\\WishlistContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const WishlistContext = /*#__PURE__*/createContext();\nexport function WishlistProvider({\n  children\n}) {\n  _s();\n  const [wishlist, setWishlist] = useState(() => {\n    const saved = localStorage.getItem('wishlist');\n    return saved ? JSON.parse(saved) : [];\n  });\n  useEffect(() => {\n    localStorage.setItem('wishlist', JSON.stringify(wishlist));\n  }, [wishlist]);\n  function addToWishlist(product) {\n    setWishlist(prev => {\n      if (prev.some(item => item._id === product._id)) {\n        return prev; // Already in wishlist\n      }\n      return [...prev, product];\n    });\n  }\n  function removeFromWishlist(productId) {\n    setWishlist(prev => prev.filter(item => item._id !== productId));\n  }\n  function clearWishlist() {\n    setWishlist([]);\n  }\n  function isInWishlist(productId) {\n    return wishlist.some(item => item._id === productId);\n  }\n  return /*#__PURE__*/_jsxDEV(WishlistContext.Provider, {\n    value: {\n      wishlist,\n      addToWishlist,\n      removeFromWishlist,\n      clearWishlist,\n      isInWishlist\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(WishlistProvider, \"6Sx6b/tOS8TGhuNLZjBr8CBLiAk=\");\n_c = WishlistProvider;\nvar _c;\n$RefreshReg$(_c, \"WishlistProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "WishlistContext", "WishlistProvider", "children", "_s", "wishlist", "setWishlist", "saved", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "addToWishlist", "product", "prev", "some", "item", "_id", "removeFromWishlist", "productId", "filter", "clearWishlist", "isInWishlist", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/WishlistContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect } from 'react';\n\nexport const WishlistContext = createContext();\n\nexport function WishlistProvider({ children }) {\n  const [wishlist, setWishlist] = useState(() => {\n    const saved = localStorage.getItem('wishlist');\n    return saved ? JSON.parse(saved) : [];\n  });\n\n  useEffect(() => {\n    localStorage.setItem('wishlist', JSON.stringify(wishlist));\n  }, [wishlist]);\n\n  function addToWishlist(product) {\n    setWishlist(prev => {\n      if (prev.some(item => item._id === product._id)) {\n        return prev; // Already in wishlist\n      }\n      return [...prev, product];\n    });\n  }\n\n  function removeFromWishlist(productId) {\n    setWishlist(prev => prev.filter(item => item._id !== productId));\n  }\n\n  function clearWishlist() {\n    setWishlist([]);\n  }\n\n  function isInWishlist(productId) {\n    return wishlist.some(item => item._id === productId);\n  }\n\n  return (\n    <WishlistContext.Provider value={{ \n      wishlist, \n      addToWishlist, \n      removeFromWishlist, \n      clearWishlist,\n      isInWishlist \n    }}>\n      {children}\n    </WishlistContext.Provider>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,OAAO,MAAMC,eAAe,gBAAGL,aAAa,CAAC,CAAC;AAE9C,OAAO,SAASM,gBAAgBA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC7C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,MAAM;IAC7C,MAAMU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,EAAE;EACvC,CAAC,CAAC;EAEFT,SAAS,CAAC,MAAM;IACdU,YAAY,CAACI,OAAO,CAAC,UAAU,EAAEF,IAAI,CAACG,SAAS,CAACR,QAAQ,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,SAASS,aAAaA,CAACC,OAAO,EAAE;IAC9BT,WAAW,CAACU,IAAI,IAAI;MAClB,IAAIA,IAAI,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKJ,OAAO,CAACI,GAAG,CAAC,EAAE;QAC/C,OAAOH,IAAI,CAAC,CAAC;MACf;MACA,OAAO,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEA,SAASK,kBAAkBA,CAACC,SAAS,EAAE;IACrCf,WAAW,CAACU,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKE,SAAS,CAAC,CAAC;EAClE;EAEA,SAASE,aAAaA,CAAA,EAAG;IACvBjB,WAAW,CAAC,EAAE,CAAC;EACjB;EAEA,SAASkB,YAAYA,CAACH,SAAS,EAAE;IAC/B,OAAOhB,QAAQ,CAACY,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKE,SAAS,CAAC;EACtD;EAEA,oBACErB,OAAA,CAACC,eAAe,CAACwB,QAAQ;IAACC,KAAK,EAAE;MAC/BrB,QAAQ;MACRS,aAAa;MACbM,kBAAkB;MAClBG,aAAa;MACbC;IACF,CAAE;IAAArB,QAAA,EACCA;EAAQ;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B;AAAC1B,EAAA,CA1CeF,gBAAgB;AAAA6B,EAAA,GAAhB7B,gBAAgB;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}