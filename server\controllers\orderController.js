exports.placeOrder = async (req, res) => {
  try {
    const { items, total } = req.body;
    const order = {
      _id: Date.now().toString(),
      user: req.user.id,
      items,
      total,
      createdAt: new Date()
    };

    global.orders.push(order);

    // Award points for each dollar spent
    const points = Math.floor(total); // 1 point per $1
    const user = global.users.find(u => u._id === req.user.id);
    if (user) {
      user.points += points;
    }

    res.status(201).json({ message: 'Order placed!', order });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};