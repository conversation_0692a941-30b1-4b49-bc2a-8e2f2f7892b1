const Order = require('../models/Order');
const User = require('../models/User');

exports.placeOrder = async (req, res) => {
  try {
    const { items, total } = req.body;
    const order = new Order({
      user: req.user.id,
      items,
      total,
    });
    await order.save();

    // Award points for each dollar spent
    const points = Math.floor(total); // 1 point per $1
    await User.findByIdAndUpdate(req.user.id, { $inc: { points } });

    res.status(201).json({ message: 'Order placed!', order });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};