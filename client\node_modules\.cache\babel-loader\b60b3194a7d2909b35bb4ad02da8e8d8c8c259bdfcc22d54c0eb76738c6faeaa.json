{"ast": null, "code": "// Currency formatting utility for Indian Rupees\nexport const formatCurrency = amount => {\n  // Convert amount to number if it's a string\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\n\n  // Format with Indian number system (lakhs, crores)\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2\n  }).format(numAmount);\n};\n\n// Format currency without symbol (just the number)\nexport const formatCurrencyNumber = amount => {\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\n  return new Intl.NumberFormat('en-IN', {\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2\n  }).format(numAmount);\n};\n\n// Convert price to display format (divide by 100 if needed)\nexport const formatPrice = price => {\n  return formatCurrency(price);\n};\n\n// Format price for display in cards and lists\nexport const formatDisplayPrice = price => {\n  const numPrice = typeof price === 'string' ? parseFloat(price) : price;\n  if (numPrice >= 100000) {\n    return `₹${(numPrice / 100000).toFixed(1)}L`;\n  } else if (numPrice >= 1000) {\n    return `₹${(numPrice / 1000).toFixed(1)}K`;\n  } else {\n    return `₹${numPrice.toLocaleString('en-IN')}`;\n  }\n};", "map": {"version": 3, "names": ["formatCurrency", "amount", "numAmount", "parseFloat", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatCurrencyNumber", "formatPrice", "price", "formatDisplayPrice", "numPrice", "toFixed", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/utils/currency.js"], "sourcesContent": ["// Currency formatting utility for Indian Rupees\nexport const formatCurrency = (amount) => {\n  // Convert amount to number if it's a string\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\n  \n  // Format with Indian number system (lakhs, crores)\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2\n  }).format(numAmount);\n};\n\n// Format currency without symbol (just the number)\nexport const formatCurrencyNumber = (amount) => {\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\n  \n  return new Intl.NumberFormat('en-IN', {\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2\n  }).format(numAmount);\n};\n\n// Convert price to display format (divide by 100 if needed)\nexport const formatPrice = (price) => {\n  return formatCurrency(price);\n};\n\n// Format price for display in cards and lists\nexport const formatDisplayPrice = (price) => {\n  const numPrice = typeof price === 'string' ? parseFloat(price) : price;\n  \n  if (numPrice >= 100000) {\n    return `₹${(numPrice / 100000).toFixed(1)}L`;\n  } else if (numPrice >= 1000) {\n    return `₹${(numPrice / 1000).toFixed(1)}K`;\n  } else {\n    return `₹${numPrice.toLocaleString('en-IN')}`;\n  }\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,cAAc,GAAIC,MAAM,IAAK;EACxC;EACA,MAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ,GAAGE,UAAU,CAACF,MAAM,CAAC,GAAGA,MAAM;;EAE1E;EACA,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACR,SAAS,CAAC;AACtB,CAAC;;AAED;AACA,OAAO,MAAMS,oBAAoB,GAAIV,MAAM,IAAK;EAC9C,MAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ,GAAGE,UAAU,CAACF,MAAM,CAAC,GAAGA,MAAM;EAE1E,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCG,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACR,SAAS,CAAC;AACtB,CAAC;;AAED;AACA,OAAO,MAAMU,WAAW,GAAIC,KAAK,IAAK;EACpC,OAAOb,cAAc,CAACa,KAAK,CAAC;AAC9B,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAID,KAAK,IAAK;EAC3C,MAAME,QAAQ,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAGV,UAAU,CAACU,KAAK,CAAC,GAAGA,KAAK;EAEtE,IAAIE,QAAQ,IAAI,MAAM,EAAE;IACtB,OAAO,IAAI,CAACA,QAAQ,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9C,CAAC,MAAM,IAAID,QAAQ,IAAI,IAAI,EAAE;IAC3B,OAAO,IAAI,CAACA,QAAQ,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC5C,CAAC,MAAM;IACL,OAAO,IAAID,QAAQ,CAACE,cAAc,CAAC,OAAO,CAAC,EAAE;EAC/C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}