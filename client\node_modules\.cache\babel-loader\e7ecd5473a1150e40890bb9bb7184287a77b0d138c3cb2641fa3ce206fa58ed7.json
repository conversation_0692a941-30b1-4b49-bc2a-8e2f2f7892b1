{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../context/AuthContext';\nimport AuthModal from '../components/AuthModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginPage() {\n  _s();\n  const [showModal, setShowModal] = useState(true);\n  const {\n    user\n  } = useContext(AuthContext);\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/profile');\n    }\n  }, [user, navigate]);\n  const handleCloseModal = () => {\n    setShowModal(false);\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(AuthModal, {\n      isOpen: showModal,\n      onClose: handleCloseModal,\n      initialMode: \"login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-page-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Join EcoCommerce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign in to access your account and start shopping sustainably\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-heart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Save Favorites\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Create wishlists of your favorite eco-friendly products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-star\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Earn Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get loyalty points with every purchase\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-truck\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Track Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Monitor your order status and delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .login-page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .hero-section {\n          text-align: center;\n          padding: 4rem 0;\n        }\n\n        .hero-section h1 {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .hero-section p {\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          margin: 0 0 3rem 0;\n        }\n\n        .features {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 2rem;\n          margin-top: 3rem;\n        }\n\n        .feature {\n          background: white;\n          padding: 2rem;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 2.5rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .feature h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 0.5rem 0;\n        }\n\n        .feature p {\n          color: var(--text-secondary);\n          margin: 0;\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .hero-section h1 {\n            font-size: 2rem;\n          }\n          \n          .features {\n            grid-template-columns: 1fr;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(LoginPage, \"PU3nP0MMvPS9iC/trH/ZtJLfgcY=\", false, function () {\n  return [useNavigate];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "AuthContext", "AuthModal", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "showModal", "setShowModal", "user", "navigate", "handleCloseModal", "children", "isOpen", "onClose", "initialMode", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../context/AuthContext';\nimport AuthModal from '../components/AuthModal';\n\nfunction LoginPage() {\n  const [showModal, setShowModal] = useState(true);\n  const { user } = useContext(AuthContext);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/profile');\n    }\n  }, [user, navigate]);\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    navigate('/');\n  };\n\n  return (\n    <main>\n      <AuthModal \n        isOpen={showModal} \n        onClose={handleCloseModal}\n        initialMode=\"login\"\n      />\n      \n      <div className=\"login-page-content\">\n        <div className=\"hero-section\">\n          <h1>Join EcoCommerce</h1>\n          <p>Sign in to access your account and start shopping sustainably</p>\n          \n          <div className=\"features\">\n            <div className=\"feature\">\n              <i className=\"fas fa-heart\"></i>\n              <h3>Save Favorites</h3>\n              <p>Create wishlists of your favorite eco-friendly products</p>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-star\"></i>\n              <h3>Earn Points</h3>\n              <p>Get loyalty points with every purchase</p>\n            </div>\n            <div className=\"feature\">\n              <i className=\"fas fa-truck\"></i>\n              <h3>Track Orders</h3>\n              <p>Monitor your order status and delivery</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .login-page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .hero-section {\n          text-align: center;\n          padding: 4rem 0;\n        }\n\n        .hero-section h1 {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0 0 1rem 0;\n        }\n\n        .hero-section p {\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          margin: 0 0 3rem 0;\n        }\n\n        .features {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 2rem;\n          margin-top: 3rem;\n        }\n\n        .feature {\n          background: white;\n          padding: 2rem;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n        }\n\n        .feature i {\n          font-size: 2.5rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .feature h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0 0 0.5rem 0;\n        }\n\n        .feature p {\n          color: var(--text-secondary);\n          margin: 0;\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .hero-section h1 {\n            font-size: 2rem;\n          }\n          \n          .features {\n            grid-template-columns: 1fr;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,SAAS,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEY;EAAK,CAAC,GAAGX,UAAU,CAACG,WAAW,CAAC;EACxC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,EAAE;MACRC,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC,EAAE,CAACD,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAEpB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BH,YAAY,CAAC,KAAK,CAAC;IACnBE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEN,OAAA;IAAAQ,QAAA,gBACER,OAAA,CAACF,SAAS;MACRW,MAAM,EAAEN,SAAU;MAClBO,OAAO,EAAEH,gBAAiB;MAC1BI,WAAW,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAEFf,OAAA;MAAKgB,SAAS,EAAC,oBAAoB;MAAAR,QAAA,eACjCR,OAAA;QAAKgB,SAAS,EAAC,cAAc;QAAAR,QAAA,gBAC3BR,OAAA;UAAAQ,QAAA,EAAI;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBf,OAAA;UAAAQ,QAAA,EAAG;QAA6D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEpEf,OAAA;UAAKgB,SAAS,EAAC,UAAU;UAAAR,QAAA,gBACvBR,OAAA;YAAKgB,SAAS,EAAC,SAAS;YAAAR,QAAA,gBACtBR,OAAA;cAAGgB,SAAS,EAAC;YAAc;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCf,OAAA;cAAAQ,QAAA,EAAI;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBf,OAAA;cAAAQ,QAAA,EAAG;YAAuD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNf,OAAA;YAAKgB,SAAS,EAAC,SAAS;YAAAR,QAAA,gBACtBR,OAAA;cAAGgB,SAAS,EAAC;YAAa;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/Bf,OAAA;cAAAQ,QAAA,EAAI;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBf,OAAA;cAAAQ,QAAA,EAAG;YAAsC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNf,OAAA;YAAKgB,SAAS,EAAC,SAAS;YAAAR,QAAA,gBACtBR,OAAA;cAAGgB,SAAS,EAAC;YAAc;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCf,OAAA;cAAAQ,QAAA,EAAI;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBf,OAAA;cAAAQ,QAAA,EAAG;YAAsC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAOiB,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX;AAACb,EAAA,CAxHQD,SAAS;EAAA,QAGCL,WAAW;AAAA;AAAAsB,EAAA,GAHrBjB,SAAS;AA0HlB,eAAeA,SAAS;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}