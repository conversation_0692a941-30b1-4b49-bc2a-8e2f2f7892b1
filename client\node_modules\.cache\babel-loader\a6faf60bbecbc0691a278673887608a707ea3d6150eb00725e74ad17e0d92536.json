{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\LoadingSpinner.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoadingSpinner({\n  size = 'medium',\n  message = 'Loading...',\n  fullScreen = false\n}) {\n  const sizeClasses = {\n    small: 'spinner-small',\n    medium: 'spinner-medium',\n    large: 'spinner-large'\n  };\n  const Container = fullScreen ? 'div' : React.Fragment;\n  const containerProps = fullScreen ? {\n    className: 'fullscreen-loader'\n  } : {};\n  return /*#__PURE__*/_jsxDEV(Container, {\n    ...containerProps,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `spinner ${sizeClasses[size]}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-ring\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"loading-message\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .fullscreen-loader {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(255, 255, 255, 0.9);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 9999;\n          backdrop-filter: blur(4px);\n        }\n\n        .loading-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n          padding: 2rem;\n        }\n\n        .spinner {\n          display: inline-block;\n          position: relative;\n        }\n\n        .spinner-small {\n          width: 24px;\n          height: 24px;\n        }\n\n        .spinner-medium {\n          width: 40px;\n          height: 40px;\n        }\n\n        .spinner-large {\n          width: 64px;\n          height: 64px;\n        }\n\n        .spinner-ring {\n          width: 100%;\n          height: 100%;\n          position: relative;\n        }\n\n        .spinner-ring div {\n          box-sizing: border-box;\n          display: block;\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          border: 3px solid var(--primary-color);\n          border-radius: 50%;\n          animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;\n          border-color: var(--primary-color) transparent transparent transparent;\n        }\n\n        .spinner-ring div:nth-child(1) {\n          animation-delay: -0.45s;\n        }\n\n        .spinner-ring div:nth-child(2) {\n          animation-delay: -0.3s;\n        }\n\n        .spinner-ring div:nth-child(3) {\n          animation-delay: -0.15s;\n        }\n\n        @keyframes spinner-ring {\n          0% {\n            transform: rotate(0deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n\n        .loading-message {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          font-weight: 500;\n          margin: 0;\n          text-align: center;\n        }\n\n        /* Pulse animation for skeleton loading */\n        @keyframes pulse {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.5;\n          }\n        }\n\n        .pulse {\n          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "message", "fullScreen", "sizeClasses", "small", "medium", "large", "Container", "Fragment", "containerProps", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/LoadingSpinner.jsx"], "sourcesContent": ["import React from 'react';\n\nfunction LoadingSpinner({ size = 'medium', message = 'Loading...', fullScreen = false }) {\n  const sizeClasses = {\n    small: 'spinner-small',\n    medium: 'spinner-medium',\n    large: 'spinner-large'\n  };\n\n  const Container = fullScreen ? 'div' : React.Fragment;\n  const containerProps = fullScreen ? { className: 'fullscreen-loader' } : {};\n\n  return (\n    <Container {...containerProps}>\n      <div className=\"loading-container\">\n        <div className={`spinner ${sizeClasses[size]}`}>\n          <div className=\"spinner-ring\">\n            <div></div>\n            <div></div>\n            <div></div>\n            <div></div>\n          </div>\n        </div>\n        {message && <p className=\"loading-message\">{message}</p>}\n      </div>\n\n      <style jsx>{`\n        .fullscreen-loader {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(255, 255, 255, 0.9);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 9999;\n          backdrop-filter: blur(4px);\n        }\n\n        .loading-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n          padding: 2rem;\n        }\n\n        .spinner {\n          display: inline-block;\n          position: relative;\n        }\n\n        .spinner-small {\n          width: 24px;\n          height: 24px;\n        }\n\n        .spinner-medium {\n          width: 40px;\n          height: 40px;\n        }\n\n        .spinner-large {\n          width: 64px;\n          height: 64px;\n        }\n\n        .spinner-ring {\n          width: 100%;\n          height: 100%;\n          position: relative;\n        }\n\n        .spinner-ring div {\n          box-sizing: border-box;\n          display: block;\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          border: 3px solid var(--primary-color);\n          border-radius: 50%;\n          animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;\n          border-color: var(--primary-color) transparent transparent transparent;\n        }\n\n        .spinner-ring div:nth-child(1) {\n          animation-delay: -0.45s;\n        }\n\n        .spinner-ring div:nth-child(2) {\n          animation-delay: -0.3s;\n        }\n\n        .spinner-ring div:nth-child(3) {\n          animation-delay: -0.15s;\n        }\n\n        @keyframes spinner-ring {\n          0% {\n            transform: rotate(0deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n\n        .loading-message {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          font-weight: 500;\n          margin: 0;\n          text-align: center;\n        }\n\n        /* Pulse animation for skeleton loading */\n        @keyframes pulse {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.5;\n          }\n        }\n\n        .pulse {\n          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n        }\n      `}</style>\n    </Container>\n  );\n}\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,cAAcA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,OAAO,GAAG,YAAY;EAAEC,UAAU,GAAG;AAAM,CAAC,EAAE;EACvF,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,gBAAgB;IACxBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,SAAS,GAAGL,UAAU,GAAG,KAAK,GAAGN,KAAK,CAACY,QAAQ;EACrD,MAAMC,cAAc,GAAGP,UAAU,GAAG;IAAEQ,SAAS,EAAE;EAAoB,CAAC,GAAG,CAAC,CAAC;EAE3E,oBACEZ,OAAA,CAACS,SAAS;IAAA,GAAKE,cAAc;IAAAE,QAAA,gBAC3Bb,OAAA;MAAKY,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCb,OAAA;QAAKY,SAAS,EAAE,WAAWP,WAAW,CAACH,IAAI,CAAC,EAAG;QAAAW,QAAA,eAC7Cb,OAAA;UAAKY,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3Bb,OAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXjB,OAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXjB,OAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXjB,OAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLd,OAAO,iBAAIH,OAAA;QAAGY,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEV;MAAO;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC,eAENjB,OAAA;MAAOkB,GAAG;MAAAL,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEhB;AAACE,EAAA,GAnIQlB,cAAc;AAqIvB,eAAeA,cAAc;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}