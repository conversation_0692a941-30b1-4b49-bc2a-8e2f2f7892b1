{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { CartContext } from '../context/CartContext';\nimport { AuthContext } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  const {\n    cart\n  } = useContext(CartContext);\n  const {\n    user,\n    logout\n  } = useContext(AuthContext);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const cartItemCount = cart.reduce((total, item) => total + item.quantity, 0);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-leaf\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), \"EcoCommerce\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/search\",\n        className: \"nav-link\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), \"Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/cart\",\n        className: \"nav-link cart-link\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-shopping-cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), \"Cart\", cartItemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"cart-badge\",\n          children: cartItemCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), user ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"user-button\",\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this), user.name, /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-down\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/profile\",\n            onClick: () => setShowUserMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 19\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/wishlist\",\n            onClick: () => setShowUserMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-heart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 19\n            }, this), \"Wishlist\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/orders\",\n            onClick: () => setShowUserMenu(false),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-box\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 19\n            }, this), \"Orders\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              logout();\n              setShowUserMenu(false);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-sign-out-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"nav-link\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-sign-in-alt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), \"Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .nav-link {\n          position: relative;\n        }\n\n        .cart-link {\n          position: relative;\n        }\n\n        .cart-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          background: #ef4444;\n          color: white;\n          border-radius: 50%;\n          width: 20px;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .user-menu {\n          position: relative;\n        }\n\n        .user-button {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          color: white;\n          padding: 0.5rem 1rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-weight: 500;\n        }\n\n        .user-dropdown {\n          position: absolute;\n          top: 100%;\n          right: 0;\n          background: white;\n          border-radius: 0.5rem;\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\n          padding: 0.5rem;\n          min-width: 200px;\n          z-index: 1000;\n          margin-top: 0.5rem;\n        }\n\n        .user-dropdown a,\n        .user-dropdown button {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 0.75rem;\n          color: #374151;\n          text-decoration: none;\n          border-radius: 0.375rem;\n          transition: background-color 0.2s;\n          width: 100%;\n          border: none;\n          background: none;\n          cursor: pointer;\n          font-size: 0.875rem;\n        }\n\n        .user-dropdown a:hover,\n        .user-dropdown button:hover {\n          background: #f3f4f6;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"It4cfcCXcFkAYVYLhHbfIyRaiIs=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "Link", "CartContext", "AuthContext", "jsxDEV", "_jsxDEV", "Header", "_s", "cart", "user", "logout", "showUserMenu", "setShowUserMenu", "cartItemCount", "reduce", "total", "item", "quantity", "children", "to", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "name", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/Header.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { CartContext } from '../context/CartContext';\r\nimport { AuthContext } from '../context/AuthContext';\r\n\r\nfunction Header() {\r\n  const { cart } = useContext(CartContext);\r\n  const { user, logout } = useContext(AuthContext);\r\n  const [showUserMenu, setShowUserMenu] = useState(false);\r\n\r\n  const cartItemCount = cart.reduce((total, item) => total + item.quantity, 0);\r\n\r\n  return (\r\n    <header>\r\n      <h1>\r\n        <Link to=\"/\">\r\n          <i className=\"fas fa-leaf\"></i>\r\n          EcoCommerce\r\n        </Link>\r\n      </h1>\r\n      <nav>\r\n        <Link to=\"/search\" className=\"nav-link\">\r\n          <i className=\"fas fa-search\"></i>\r\n          Search\r\n        </Link>\r\n        <Link to=\"/cart\" className=\"nav-link cart-link\">\r\n          <i className=\"fas fa-shopping-cart\"></i>\r\n          Cart\r\n          {cartItemCount > 0 && (\r\n            <span className=\"cart-badge\">{cartItemCount}</span>\r\n          )}\r\n        </Link>\r\n        {user ? (\r\n          <div className=\"user-menu\">\r\n            <button\r\n              className=\"user-button\"\r\n              onClick={() => setShowUserMenu(!showUserMenu)}\r\n            >\r\n              <i className=\"fas fa-user\"></i>\r\n              {user.name}\r\n              <i className=\"fas fa-chevron-down\"></i>\r\n            </button>\r\n            {showUserMenu && (\r\n              <div className=\"user-dropdown\">\r\n                <Link to=\"/profile\" onClick={() => setShowUserMenu(false)}>\r\n                  <i className=\"fas fa-user-circle\"></i>\r\n                  Profile\r\n                </Link>\r\n                <Link to=\"/wishlist\" onClick={() => setShowUserMenu(false)}>\r\n                  <i className=\"fas fa-heart\"></i>\r\n                  Wishlist\r\n                </Link>\r\n                <Link to=\"/orders\" onClick={() => setShowUserMenu(false)}>\r\n                  <i className=\"fas fa-box\"></i>\r\n                  Orders\r\n                </Link>\r\n                <button onClick={() => { logout(); setShowUserMenu(false); }}>\r\n                  <i className=\"fas fa-sign-out-alt\"></i>\r\n                  Logout\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <Link to=\"/login\" className=\"nav-link\">\r\n            <i className=\"fas fa-sign-in-alt\"></i>\r\n            Login\r\n          </Link>\r\n        )}\r\n      </nav>\r\n\r\n      <style jsx>{`\r\n        .nav-link {\r\n          position: relative;\r\n        }\r\n\r\n        .cart-link {\r\n          position: relative;\r\n        }\r\n\r\n        .cart-badge {\r\n          position: absolute;\r\n          top: -8px;\r\n          right: -8px;\r\n          background: #ef4444;\r\n          color: white;\r\n          border-radius: 50%;\r\n          width: 20px;\r\n          height: 20px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 0.75rem;\r\n          font-weight: 600;\r\n        }\r\n\r\n        .user-menu {\r\n          position: relative;\r\n        }\r\n\r\n        .user-button {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n          color: white;\r\n          padding: 0.5rem 1rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .user-dropdown {\r\n          position: absolute;\r\n          top: 100%;\r\n          right: 0;\r\n          background: white;\r\n          border-radius: 0.5rem;\r\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\r\n          padding: 0.5rem;\r\n          min-width: 200px;\r\n          z-index: 1000;\r\n          margin-top: 0.5rem;\r\n        }\r\n\r\n        .user-dropdown a,\r\n        .user-dropdown button {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          padding: 0.75rem;\r\n          color: #374151;\r\n          text-decoration: none;\r\n          border-radius: 0.375rem;\r\n          transition: background-color 0.2s;\r\n          width: 100%;\r\n          border: none;\r\n          background: none;\r\n          cursor: pointer;\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .user-dropdown a:hover,\r\n        .user-dropdown button:hover {\r\n          background: #f3f4f6;\r\n        }\r\n      `}</style>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM;IAAEC;EAAK,CAAC,GAAGT,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM;IAAEO,IAAI;IAAEC;EAAO,CAAC,GAAGX,UAAU,CAACI,WAAW,CAAC;EAChD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMa,aAAa,GAAGL,IAAI,CAACM,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;EAE5E,oBACEZ,OAAA;IAAAa,QAAA,gBACEb,OAAA;MAAAa,QAAA,eACEb,OAAA,CAACJ,IAAI;QAACkB,EAAE,EAAC,GAAG;QAAAD,QAAA,gBACVb,OAAA;UAAGe,SAAS,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACLnB,OAAA;MAAAa,QAAA,gBACEb,OAAA,CAACJ,IAAI;QAACkB,EAAE,EAAC,SAAS;QAACC,SAAS,EAAC,UAAU;QAAAF,QAAA,gBACrCb,OAAA;UAAGe,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,UAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPnB,OAAA,CAACJ,IAAI;QAACkB,EAAE,EAAC,OAAO;QAACC,SAAS,EAAC,oBAAoB;QAAAF,QAAA,gBAC7Cb,OAAA;UAAGe,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,QAExC,EAACX,aAAa,GAAG,CAAC,iBAChBR,OAAA;UAAMe,SAAS,EAAC,YAAY;UAAAF,QAAA,EAAEL;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EACNf,IAAI,gBACHJ,OAAA;QAAKe,SAAS,EAAC,WAAW;QAAAF,QAAA,gBACxBb,OAAA;UACEe,SAAS,EAAC,aAAa;UACvBK,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAO,QAAA,gBAE9Cb,OAAA;YAAGe,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9Bf,IAAI,CAACiB,IAAI,eACVrB,OAAA;YAAGe,SAAS,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,EACRb,YAAY,iBACXN,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAF,QAAA,gBAC5Bb,OAAA,CAACJ,IAAI;YAACkB,EAAE,EAAC,UAAU;YAACM,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,KAAK,CAAE;YAAAM,QAAA,gBACxDb,OAAA;cAAGe,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA,CAACJ,IAAI;YAACkB,EAAE,EAAC,WAAW;YAACM,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,KAAK,CAAE;YAAAM,QAAA,gBACzDb,OAAA;cAAGe,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,YAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA,CAACJ,IAAI;YAACkB,EAAE,EAAC,SAAS;YAACM,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,KAAK,CAAE;YAAAM,QAAA,gBACvDb,OAAA;cAAGe,SAAS,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA;YAAQoB,OAAO,EAAEA,CAAA,KAAM;cAAEf,MAAM,CAAC,CAAC;cAAEE,eAAe,CAAC,KAAK,CAAC;YAAE,CAAE;YAAAM,QAAA,gBAC3Db,OAAA;cAAGe,SAAS,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENnB,OAAA,CAACJ,IAAI;QAACkB,EAAE,EAAC,QAAQ;QAACC,SAAS,EAAC,UAAU;QAAAF,QAAA,gBACpCb,OAAA;UAAGe,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,SAExC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnB,OAAA;MAAOsB,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAACjB,EAAA,CAjJQD,MAAM;AAAAsB,EAAA,GAANtB,MAAM;AAmJf,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}