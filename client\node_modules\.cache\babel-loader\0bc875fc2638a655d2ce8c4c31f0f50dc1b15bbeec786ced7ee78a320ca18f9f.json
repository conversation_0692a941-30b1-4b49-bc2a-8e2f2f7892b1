{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useContext,useEffect,useState}from'react';import{AuthContext}from'../context/AuthContext';import{UserStatsContext}from'../context/UserStatsContext';import LoadingSpinner from'../components/LoadingSpinner';import ErrorMessage from'../components/ErrorMessage';import ImageUpload from'../components/ImageUpload';import OrderCard from'../components/OrderCard';import RewardCard from'../components/RewardCard';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ProfilePage(){const{token,user,logout}=useContext(AuthContext);const{userStats,orders,redeemReward}=useContext(UserStatsContext);const[profile,setProfile]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[activeTab,setActiveTab]=useState('overview');const[editMode,setEditMode]=useState(false);const[showImageUpload,setShowImageUpload]=useState(false);const[profileImage,setProfileImage]=useState(null);const[formData,setFormData]=useState({name:'',email:'',phone:'',address:{street:'',city:'',state:'',zipCode:'',country:''},preferences:{newsletter:true,notifications:true,ecoTips:true}});// Available rewards\nconst availableRewards=[{id:1,title:'10% Off Next Order',description:'Get 10% discount on your next purchase of any amount',cost:100,type:'discount',value:'$10+ savings',popular:true,features:['Valid for 30 days','Stackable with sales','No minimum purchase']},{id:2,title:'Free Shipping',description:'Get free shipping on any order, regardless of amount',cost:50,type:'shipping',value:'$9.99 value',features:['Valid for 60 days','Any order size','Express shipping available']},{id:3,title:'Mystery Eco Box',description:'Receive a curated box of sustainable products worth $50+',cost:500,type:'product',value:'$50+ value',savings:'$25',features:['3-5 eco products','Surprise items','Limited edition items']},{id:4,title:'Plant a Tree',description:'We\\'ll plant a tree in your name to offset carbon emissions',cost:200,type:'eco',value:'Environmental impact',features:['Certificate included','GPS coordinates','Annual updates']}];useEffect(()=>{if(token){fetchProfile();}else{setLoading(false);setError('Please login to view your profile');}},[token]);const fetchProfile=async()=>{try{setLoading(true);const response=await axios.get('http://localhost:5000/api/users/profile',{headers:{Authorization:\"Bearer \".concat(token)}});setProfile(response.data);setProfileImage(response.data.profileImage||null);setFormData({name:response.data.name||'',email:response.data.email||'',phone:response.data.phone||'',address:response.data.address||{street:'',city:'',state:'',zipCode:'',country:''},preferences:response.data.preferences||{newsletter:true,notifications:true,ecoTips:true}});}catch(err){setError('Failed to load profile');}finally{setLoading(false);}};const handleInputChange=e=>{const{name,value,type,checked}=e.target;if(name.includes('.')){const[parent,child]=name.split('.');setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[parent]:_objectSpread(_objectSpread({},prev[parent]),{},{[child]:type==='checkbox'?checked:value})}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));}};const handleSaveProfile=async()=>{try{// Simulate API call\nsetProfile(_objectSpread(_objectSpread(_objectSpread({},profile),formData),{},{profileImage}));setEditMode(false);// Show success message\n}catch(err){setError('Failed to update profile');}};const handleImageChange=newImage=>{setProfileImage(newImage);// Simulate saving to profile\nsetProfile(prev=>_objectSpread(_objectSpread({},prev),{},{profileImage:newImage}));};const getInitials=name=>{return name.split(' ').map(word=>word.charAt(0)).join('').toUpperCase().slice(0,2);};const getLoyaltyLevel=points=>{if(points>=1000)return{level:'Platinum',color:'#8b5cf6',next:null};if(points>=500)return{level:'Gold',color:'#f59e0b',next:1000};if(points>=200)return{level:'Silver',color:'#6b7280',next:500};return{level:'Bronze',color:'#92400e',next:200};};const handleReorder=order=>{// Implement reorder functionality\nconsole.log('Reordering:',order);};const handleReview=order=>{// Implement review functionality\nconsole.log('Writing review for:',order);};const handleRedeemReward=async reward=>{try{await redeemReward(reward.cost);alert(\"Successfully redeemed: \".concat(reward.title));}catch(error){alert('Failed to redeem reward');}};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{size:\"large\",message:\"Loading your profile...\",fullScreen:true});}if(error){return/*#__PURE__*/_jsx(ErrorMessage,{title:\"Profile Error\",message:error,onRetry:fetchProfile});}if(!profile){return/*#__PURE__*/_jsx(\"main\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-required\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"auth-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user-lock\"})}),/*#__PURE__*/_jsx(\"h2\",{children:\"Authentication Required\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Please log in to view your profile\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.location.href='/login',className:\"login-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-in-alt\"}),\"Go to Login\"]})]})});}const loyaltyInfo=getLoyaltyLevel(userStats.loyaltyPoints||0);return/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"profile-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"profile-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"profile-avatar-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"profile-avatar\",children:profileImage?/*#__PURE__*/_jsx(\"img\",{src:profileImage,alt:profile.name}):/*#__PURE__*/_jsx(\"div\",{className:\"avatar-initials\",children:getInitials(profile.name)})}),/*#__PURE__*/_jsx(\"button\",{className:\"edit-avatar-btn\",onClick:()=>setShowImageUpload(true),title:\"Change profile picture\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-camera\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"profile-info\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[\"Welcome back, \",profile.name,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"profile-email\",children:profile.email}),/*#__PURE__*/_jsxs(\"div\",{className:\"loyalty-badge\",style:{backgroundColor:loyaltyInfo.color},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-crown\"}),loyaltyInfo.level,\" Member\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"profile-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:userStats.loyaltyPoints}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Loyalty Points\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:userStats.totalOrders}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Total Orders\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-value\",children:[\"$\",userStats.totalSpent.toFixed(2)]}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Total Spent\"})]})]})]}),loyaltyInfo.next&&/*#__PURE__*/_jsxs(\"div\",{className:\"loyalty-progress\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"progress-header\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"Progress to \",loyaltyInfo.next===1000?'Platinum':loyaltyInfo.next===500?'Gold':'Silver']}),/*#__PURE__*/_jsxs(\"span\",{children:[userStats.loyaltyPoints,\" / \",loyaltyInfo.next,\" points\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-fill\",style:{width:\"\".concat(userStats.loyaltyPoints/loyaltyInfo.next*100,\"%\"),backgroundColor:loyaltyInfo.color}})}),/*#__PURE__*/_jsxs(\"p\",{className:\"progress-text\",children:[\"Earn \",loyaltyInfo.next-userStats.loyaltyPoints,\" more points to reach the next level!\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"profile-tabs\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"tab \".concat(activeTab==='overview'?'active':''),onClick:()=>setActiveTab('overview'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chart-line\"}),\"Overview\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"tab \".concat(activeTab==='orders'?'active':''),onClick:()=>setActiveTab('orders'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-box\"}),\"Order History\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"tab \".concat(activeTab==='settings'?'active':''),onClick:()=>setActiveTab('settings'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-cog\"}),\"Account Settings\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"tab \".concat(activeTab==='rewards'?'active':''),onClick:()=>setActiveTab('rewards'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-gift\"}),\"Rewards\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[activeTab==='overview'&&/*#__PURE__*/_jsx(\"div\",{className:\"overview-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"overview-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"overview-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-leaf\"}),\"Eco Impact\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"eco-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"eco-stat\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"eco-value\",children:[userStats.ecoImpact.co2Saved,\" kg\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"eco-label\",children:\"CO\\u2082 Saved\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"eco-stat\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"eco-value\",children:userStats.ecoImpact.ecoProducts}),/*#__PURE__*/_jsx(\"div\",{className:\"eco-label\",children:\"Eco Products\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"eco-stat\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"eco-value\",children:userStats.ecoImpact.treesPlanted}),/*#__PURE__*/_jsx(\"div\",{className:\"eco-label\",children:\"Trees Planted\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"overview-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock\"}),\"Recent Activity\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"activity-list\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"activity-item\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart activity-icon\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"activity-text\",children:\"Ordered Solar Power Bank\"}),/*#__PURE__*/_jsx(\"div\",{className:\"activity-date\",children:\"2 days ago\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"activity-item\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-heart activity-icon\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"activity-text\",children:\"Added Bamboo Toothbrush to wishlist\"}),/*#__PURE__*/_jsx(\"div\",{className:\"activity-date\",children:\"1 week ago\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"activity-item\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star activity-icon\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"activity-text\",children:\"Reviewed Organic Cotton T-Shirt\"}),/*#__PURE__*/_jsx(\"div\",{className:\"activity-date\",children:\"2 weeks ago\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"overview-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-trophy\"}),\"Achievements\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"achievements\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"achievement earned\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-seedling\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Eco Warrior\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"achievement earned\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-bag\"}),/*#__PURE__*/_jsx(\"span\",{children:\"First Purchase\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"achievement\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-users\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Referral Master\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"achievement\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-calendar\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Monthly Shopper\"})]})]})]})]})}),activeTab==='orders'&&/*#__PURE__*/_jsxs(\"div\",{className:\"orders-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"orders-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Order History\"}),/*#__PURE__*/_jsx(\"div\",{className:\"order-filters\",children:/*#__PURE__*/_jsxs(\"select\",{className:\"filter-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Orders\"}),/*#__PURE__*/_jsx(\"option\",{value:\"delivered\",children:\"Delivered\"}),/*#__PURE__*/_jsx(\"option\",{value:\"shipped\",children:\"Shipped\"}),/*#__PURE__*/_jsx(\"option\",{value:\"processing\",children:\"Processing\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"orders-list\",children:orders.map(order=>/*#__PURE__*/_jsx(OrderCard,{order:order,onReorder:handleReorder,onReview:handleReview},order.id))})]}),activeTab==='settings'&&/*#__PURE__*/_jsxs(\"div\",{className:\"settings-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Account Settings\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setEditMode(!editMode),className:\"edit-btn \".concat(editMode?'active':''),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(editMode?'fa-times':'fa-edit')}),editMode?'Cancel':'Edit Profile']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Profile Picture\"}),/*#__PURE__*/_jsx(\"div\",{className:\"profile-picture-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"current-picture\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"picture-preview\",children:profileImage?/*#__PURE__*/_jsx(\"img\",{src:profileImage,alt:\"Profile\"}):/*#__PURE__*/_jsx(\"div\",{className:\"picture-placeholder\",children:getInitials(profile.name)})}),/*#__PURE__*/_jsxs(\"div\",{className:\"picture-info\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Your profile picture is visible to other users\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowImageUpload(true),className:\"change-picture-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-camera\"}),\"Change Picture\"]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Personal Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:formData.name,onChange:handleInputChange,disabled:!editMode})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,disabled:!editMode})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"phone\",children:\"Phone Number\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",id:\"phone\",name:\"phone\",value:formData.phone,onChange:handleInputChange,disabled:!editMode,placeholder:\"(*************\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Shipping Address\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"address.street\",children:\"Street Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"address.street\",name:\"address.street\",value:formData.address.street,onChange:handleInputChange,disabled:!editMode})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"address.city\",children:\"City\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"address.city\",name:\"address.city\",value:formData.address.city,onChange:handleInputChange,disabled:!editMode})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"address.state\",children:\"State\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"address.state\",name:\"address.state\",value:formData.address.state,onChange:handleInputChange,disabled:!editMode})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"address.zipCode\",children:\"ZIP Code\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"address.zipCode\",name:\"address.zipCode\",value:formData.address.zipCode,onChange:handleInputChange,disabled:!editMode})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Preferences\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"preferences-grid\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"preferences.newsletter\",checked:formData.preferences.newsletter,onChange:handleInputChange,disabled:!editMode}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Newsletter\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Receive updates about new eco-friendly products\"})]})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"preferences.notifications\",checked:formData.preferences.notifications,onChange:handleInputChange,disabled:!editMode}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Order Notifications\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Get notified about order status updates\"})]})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"preferences.ecoTips\",checked:formData.preferences.ecoTips,onChange:handleInputChange,disabled:!editMode}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Eco Tips\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Receive sustainability tips and advice\"})]})]})]})]}),editMode&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setEditMode(false),className:\"btn-secondary\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSaveProfile,className:\"btn-primary\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-save\"}),\"Save Changes\"]})]})]})]}),activeTab==='rewards'&&/*#__PURE__*/_jsxs(\"div\",{className:\"rewards-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"rewards-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Loyalty Rewards\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"points-balance\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"points-value\",children:userStats.loyaltyPoints}),/*#__PURE__*/_jsx(\"span\",{className:\"points-label\",children:\"Available Points\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"rewards-grid\",children:availableRewards.map(reward=>/*#__PURE__*/_jsx(RewardCard,{reward:reward,userPoints:userStats.loyaltyPoints,onRedeem:handleRedeemReward},reward.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"earn-points\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"How to Earn More Points\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"earn-methods\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"earn-method\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Make Purchases\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Earn 1 point for every $1 spent\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"earn-method\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Write Reviews\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Get 10 points for each product review\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"earn-method\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-users\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Refer Friends\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Earn 50 points for each successful referral\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"earn-method\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-birthday-cake\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Birthday Bonus\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Get 100 points on your birthday\"})]})]})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"profile-footer\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:logout,className:\"logout-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-out-alt\"}),\"Sign Out\"]})})]}),showImageUpload&&/*#__PURE__*/_jsx(ImageUpload,{currentImage:profileImage,onImageChange:handleImageChange,onClose:()=>setShowImageUpload(false)}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .profile-container {\\n          max-width: 1200px;\\n          margin: 0 auto;\\n          padding: 2rem;\\n        }\\n\\n        .auth-required {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          min-height: 60vh;\\n          text-align: center;\\n          gap: 1.5rem;\\n        }\\n\\n        .auth-icon {\\n          font-size: 4rem;\\n          color: var(--text-secondary);\\n        }\\n\\n        .login-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 1rem 2rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .profile-header {\\n          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n          color: white;\\n          padding: 2rem;\\n          border-radius: 1rem;\\n          display: grid;\\n          grid-template-columns: auto 1fr auto;\\n          gap: 2rem;\\n          align-items: center;\\n          margin-bottom: 2rem;\\n        }\\n\\n        .profile-avatar-container {\\n          position: relative;\\n          display: inline-block;\\n        }\\n\\n        .profile-avatar {\\n          width: 100px;\\n          height: 100px;\\n          border-radius: 50%;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 2rem;\\n          overflow: hidden;\\n          border: 4px solid rgba(255, 255, 255, 0.3);\\n          transition: all 0.3s ease;\\n        }\\n\\n        .profile-avatar img {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n        }\\n\\n        .avatar-initials {\\n          background: rgba(255, 255, 255, 0.2);\\n          width: 100%;\\n          height: 100%;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 2rem;\\n          font-weight: 700;\\n          color: white;\\n        }\\n\\n        .edit-avatar-btn {\\n          position: absolute;\\n          bottom: 0;\\n          right: 0;\\n          background: var(--primary-color);\\n          color: white;\\n          border: 2px solid white;\\n          border-radius: 50%;\\n          width: 36px;\\n          height: 36px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          font-size: 0.875rem;\\n          transition: all 0.2s ease;\\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n        }\\n\\n        .edit-avatar-btn:hover {\\n          background: var(--primary-dark);\\n          transform: scale(1.1);\\n        }\\n\\n        .profile-avatar-container:hover .profile-avatar {\\n          transform: scale(1.05);\\n          border-color: rgba(255, 255, 255, 0.5);\\n        }\\n\\n        .profile-info h1 {\\n          margin: 0 0 0.5rem 0;\\n          font-size: 2rem;\\n          font-weight: 700;\\n        }\\n\\n        .profile-email {\\n          opacity: 0.9;\\n          margin: 0 0 1rem 0;\\n        }\\n\\n        .loyalty-badge {\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          padding: 0.5rem 1rem;\\n          border-radius: 9999px;\\n          font-weight: 600;\\n          font-size: 0.875rem;\\n        }\\n\\n        .profile-stats {\\n          display: flex;\\n          gap: 2rem;\\n        }\\n\\n        .stat {\\n          text-align: center;\\n        }\\n\\n        .stat-value {\\n          font-size: 2rem;\\n          font-weight: 700;\\n          line-height: 1;\\n        }\\n\\n        .stat-label {\\n          font-size: 0.875rem;\\n          opacity: 0.9;\\n          margin-top: 0.25rem;\\n        }\\n\\n        .loyalty-progress {\\n          background: white;\\n          padding: 1.5rem;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n          margin-bottom: 2rem;\\n        }\\n\\n        .progress-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 1rem;\\n          font-weight: 500;\\n        }\\n\\n        .progress-bar {\\n          height: 8px;\\n          background: var(--bg-secondary);\\n          border-radius: 4px;\\n          overflow: hidden;\\n          margin-bottom: 0.75rem;\\n        }\\n\\n        .progress-fill {\\n          height: 100%;\\n          transition: width 0.3s ease;\\n        }\\n\\n        .progress-text {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n          margin: 0;\\n        }\\n\\n        .profile-tabs {\\n          display: flex;\\n          gap: 0.5rem;\\n          margin-bottom: 2rem;\\n          background: white;\\n          padding: 0.5rem;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .tab {\\n          flex: 1;\\n          background: none;\\n          border: none;\\n          padding: 1rem;\\n          border-radius: 0.75rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          color: var(--text-secondary);\\n          transition: all 0.2s;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .tab:hover {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n        }\\n\\n        .tab.active {\\n          background: var(--primary-color);\\n          color: white;\\n        }\\n\\n        .tab-content {\\n          background: white;\\n          border-radius: 1rem;\\n          box-shadow: var(--shadow-sm);\\n          border: 1px solid var(--border-color);\\n          overflow: hidden;\\n        }\\n\\n        .overview-grid {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n          gap: 2rem;\\n          padding: 2rem;\\n        }\\n\\n        .overview-card {\\n          background: var(--bg-secondary);\\n          border-radius: 1rem;\\n          padding: 1.5rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .card-header {\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .card-header h3 {\\n          margin: 0;\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .eco-stats {\\n          display: grid;\\n          grid-template-columns: repeat(3, 1fr);\\n          gap: 1rem;\\n        }\\n\\n        .eco-stat {\\n          text-align: center;\\n        }\\n\\n        .eco-value {\\n          font-size: 1.5rem;\\n          font-weight: 700;\\n          color: var(--secondary-color);\\n          line-height: 1;\\n        }\\n\\n        .eco-label {\\n          font-size: 0.875rem;\\n          color: var(--text-secondary);\\n          margin-top: 0.25rem;\\n        }\\n\\n        .activity-list {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 1rem;\\n        }\\n\\n        .activity-item {\\n          display: flex;\\n          gap: 1rem;\\n          align-items: flex-start;\\n        }\\n\\n        .activity-icon {\\n          width: 40px;\\n          height: 40px;\\n          background: var(--primary-color);\\n          color: white;\\n          border-radius: 50%;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          flex-shrink: 0;\\n        }\\n\\n        .activity-text {\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .activity-date {\\n          font-size: 0.875rem;\\n          color: var(--text-secondary);\\n        }\\n\\n        .achievements {\\n          display: grid;\\n          grid-template-columns: repeat(2, 1fr);\\n          gap: 1rem;\\n        }\\n\\n        .achievement {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n          padding: 1rem;\\n          border-radius: 0.75rem;\\n          border: 2px solid var(--border-color);\\n          opacity: 0.5;\\n          transition: all 0.2s;\\n        }\\n\\n        .achievement.earned {\\n          opacity: 1;\\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\\n          color: white;\\n          border-color: var(--secondary-color);\\n        }\\n\\n        .achievement i {\\n          font-size: 1.25rem;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .profile-header {\\n            grid-template-columns: 1fr;\\n            text-align: center;\\n            gap: 1.5rem;\\n          }\\n\\n          .profile-avatar-container {\\n            align-self: center;\\n            justify-self: center;\\n          }\\n\\n          .profile-avatar {\\n            width: 120px;\\n            height: 120px;\\n          }\\n\\n          .edit-avatar-btn {\\n            width: 40px;\\n            height: 40px;\\n            font-size: 1rem;\\n          }\\n\\n          .profile-stats {\\n            justify-content: center;\\n          }\\n\\n          .profile-tabs {\\n            flex-direction: column;\\n          }\\n\\n          .overview-grid {\\n            grid-template-columns: 1fr;\\n            padding: 1rem;\\n          }\\n\\n          .current-picture {\\n            flex-direction: column;\\n            text-align: center;\\n            gap: 1rem;\\n          }\\n        }\\n\\n        /* Profile Picture Section Styles */\\n        .profile-picture-section {\\n          margin-bottom: 2rem;\\n        }\\n\\n        .current-picture {\\n          display: flex;\\n          align-items: center;\\n          gap: 2rem;\\n          padding: 1.5rem;\\n          background: var(--bg-secondary);\\n          border-radius: 1rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .picture-preview {\\n          width: 100px;\\n          height: 100px;\\n          border-radius: 50%;\\n          overflow: hidden;\\n          border: 3px solid var(--border-color);\\n          flex-shrink: 0;\\n        }\\n\\n        .picture-preview img {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n        }\\n\\n        .picture-placeholder {\\n          width: 100%;\\n          height: 100%;\\n          background: var(--primary-color);\\n          color: white;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 2rem;\\n          font-weight: 700;\\n        }\\n\\n        .picture-info {\\n          flex: 1;\\n        }\\n\\n        .picture-info p {\\n          margin: 0 0 1rem 0;\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .change-picture-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .change-picture-btn:hover {\\n          background: var(--primary-dark);\\n        }\\n      \"})]});}export default ProfilePage;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "AuthContext", "UserStatsContext", "LoadingSpinner", "ErrorMessage", "ImageUpload", "OrderCard", "RewardCard", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "ProfilePage", "token", "user", "logout", "userStats", "orders", "redeemReward", "profile", "setProfile", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "editMode", "setEditMode", "showImageUpload", "setShowImageUpload", "profileImage", "setProfileImage", "formData", "setFormData", "name", "email", "phone", "address", "street", "city", "state", "zipCode", "country", "preferences", "newsletter", "notifications", "ecoTips", "availableRewards", "id", "title", "description", "cost", "type", "value", "popular", "features", "savings", "fetchProfile", "response", "get", "headers", "Authorization", "concat", "data", "err", "handleInputChange", "e", "checked", "target", "includes", "parent", "child", "split", "prev", "_objectSpread", "handleSaveProfile", "handleImageChange", "newImage", "getInitials", "map", "word", "char<PERSON>t", "join", "toUpperCase", "slice", "getLoyaltyLevel", "points", "level", "color", "next", "handleReorder", "order", "console", "log", "handleReview", "handleRedeemReward", "reward", "alert", "size", "message", "fullScreen", "onRetry", "children", "className", "onClick", "window", "location", "href", "loyaltyInfo", "loyaltyPoints", "src", "alt", "style", "backgroundColor", "totalOrders", "totalSpent", "toFixed", "width", "ecoImpact", "co2Saved", "ecoProducts", "treesPlanted", "onReorder", "onReview", "htmlFor", "onChange", "disabled", "placeholder", "userPoints", "onRedeem", "currentImage", "onImageChange", "onClose"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/ProfilePage.jsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\r\nimport { AuthContext } from '../context/AuthContext';\r\nimport { UserStatsContext } from '../context/UserStatsContext';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport ImageUpload from '../components/ImageUpload';\r\nimport OrderCard from '../components/OrderCard';\r\nimport RewardCard from '../components/RewardCard';\r\nimport axios from 'axios';\r\n\r\nfunction ProfilePage() {\r\n  const { token, user, logout } = useContext(AuthContext);\r\n  const { userStats, orders, redeemReward } = useContext(UserStatsContext);\r\n  const [profile, setProfile] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [editMode, setEditMode] = useState(false);\r\n  const [showImageUpload, setShowImageUpload] = useState(false);\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    phone: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: ''\r\n    },\r\n    preferences: {\r\n      newsletter: true,\r\n      notifications: true,\r\n      ecoTips: true\r\n    }\r\n  });\r\n\r\n  // Available rewards\r\n  const availableRewards = [\r\n    {\r\n      id: 1,\r\n      title: '10% Off Next Order',\r\n      description: 'Get 10% discount on your next purchase of any amount',\r\n      cost: 100,\r\n      type: 'discount',\r\n      value: '$10+ savings',\r\n      popular: true,\r\n      features: ['Valid for 30 days', 'Stackable with sales', 'No minimum purchase']\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'Free Shipping',\r\n      description: 'Get free shipping on any order, regardless of amount',\r\n      cost: 50,\r\n      type: 'shipping',\r\n      value: '$9.99 value',\r\n      features: ['Valid for 60 days', 'Any order size', 'Express shipping available']\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'Mystery Eco Box',\r\n      description: 'Receive a curated box of sustainable products worth $50+',\r\n      cost: 500,\r\n      type: 'product',\r\n      value: '$50+ value',\r\n      savings: '$25',\r\n      features: ['3-5 eco products', 'Surprise items', 'Limited edition items']\r\n    },\r\n    {\r\n      id: 4,\r\n      title: 'Plant a Tree',\r\n      description: 'We\\'ll plant a tree in your name to offset carbon emissions',\r\n      cost: 200,\r\n      type: 'eco',\r\n      value: 'Environmental impact',\r\n      features: ['Certificate included', 'GPS coordinates', 'Annual updates']\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      fetchProfile();\r\n    } else {\r\n      setLoading(false);\r\n      setError('Please login to view your profile');\r\n    }\r\n  }, [token]);\r\n\r\n  const fetchProfile = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get('http://localhost:5000/api/users/profile', {\r\n        headers: { Authorization: `Bearer ${token}` }\r\n      });\r\n      setProfile(response.data);\r\n      setProfileImage(response.data.profileImage || null);\r\n      setFormData({\r\n        name: response.data.name || '',\r\n        email: response.data.email || '',\r\n        phone: response.data.phone || '',\r\n        address: response.data.address || {\r\n          street: '',\r\n          city: '',\r\n          state: '',\r\n          zipCode: '',\r\n          country: ''\r\n        },\r\n        preferences: response.data.preferences || {\r\n          newsletter: true,\r\n          notifications: true,\r\n          ecoTips: true\r\n        }\r\n      });\r\n    } catch (err) {\r\n      setError('Failed to load profile');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [parent]: {\r\n          ...prev[parent],\r\n          [child]: type === 'checkbox' ? checked : value\r\n        }\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [name]: type === 'checkbox' ? checked : value\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleSaveProfile = async () => {\r\n    try {\r\n      // Simulate API call\r\n      setProfile({ ...profile, ...formData, profileImage });\r\n      setEditMode(false);\r\n      // Show success message\r\n    } catch (err) {\r\n      setError('Failed to update profile');\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (newImage) => {\r\n    setProfileImage(newImage);\r\n    // Simulate saving to profile\r\n    setProfile(prev => ({ ...prev, profileImage: newImage }));\r\n  };\r\n\r\n  const getInitials = (name) => {\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n\r\n\r\n  const getLoyaltyLevel = (points) => {\r\n    if (points >= 1000) return { level: 'Platinum', color: '#8b5cf6', next: null };\r\n    if (points >= 500) return { level: 'Gold', color: '#f59e0b', next: 1000 };\r\n    if (points >= 200) return { level: 'Silver', color: '#6b7280', next: 500 };\r\n    return { level: 'Bronze', color: '#92400e', next: 200 };\r\n  };\r\n\r\n  const handleReorder = (order) => {\r\n    // Implement reorder functionality\r\n    console.log('Reordering:', order);\r\n  };\r\n\r\n  const handleReview = (order) => {\r\n    // Implement review functionality\r\n    console.log('Writing review for:', order);\r\n  };\r\n\r\n  const handleRedeemReward = async (reward) => {\r\n    try {\r\n      await redeemReward(reward.cost);\r\n      alert(`Successfully redeemed: ${reward.title}`);\r\n    } catch (error) {\r\n      alert('Failed to redeem reward');\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner size=\"large\" message=\"Loading your profile...\" fullScreen />;\r\n  }\r\n\r\n  if (error) {\r\n    return <ErrorMessage title=\"Profile Error\" message={error} onRetry={fetchProfile} />;\r\n  }\r\n\r\n  if (!profile) {\r\n    return (\r\n      <main>\r\n        <div className=\"auth-required\">\r\n          <div className=\"auth-icon\">\r\n            <i className=\"fas fa-user-lock\"></i>\r\n          </div>\r\n          <h2>Authentication Required</h2>\r\n          <p>Please log in to view your profile</p>\r\n          <button onClick={() => window.location.href = '/login'} className=\"login-btn\">\r\n            <i className=\"fas fa-sign-in-alt\"></i>\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  const loyaltyInfo = getLoyaltyLevel(userStats.loyaltyPoints || 0);\r\n\r\n  return (\r\n    <main>\r\n      <div className=\"profile-container\">\r\n        {/* Profile Header */}\r\n        <div className=\"profile-header\">\r\n          <div className=\"profile-avatar-container\">\r\n            <div className=\"profile-avatar\">\r\n              {profileImage ? (\r\n                <img src={profileImage} alt={profile.name} />\r\n              ) : (\r\n                <div className=\"avatar-initials\">\r\n                  {getInitials(profile.name)}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <button\r\n              className=\"edit-avatar-btn\"\r\n              onClick={() => setShowImageUpload(true)}\r\n              title=\"Change profile picture\"\r\n            >\r\n              <i className=\"fas fa-camera\"></i>\r\n            </button>\r\n          </div>\r\n          <div className=\"profile-info\">\r\n            <h1>Welcome back, {profile.name}!</h1>\r\n            <p className=\"profile-email\">{profile.email}</p>\r\n            <div className=\"loyalty-badge\" style={{ backgroundColor: loyaltyInfo.color }}>\r\n              <i className=\"fas fa-crown\"></i>\r\n              {loyaltyInfo.level} Member\r\n            </div>\r\n          </div>\r\n          <div className=\"profile-stats\">\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">{userStats.loyaltyPoints}</div>\r\n              <div className=\"stat-label\">Loyalty Points</div>\r\n            </div>\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">{userStats.totalOrders}</div>\r\n              <div className=\"stat-label\">Total Orders</div>\r\n            </div>\r\n            <div className=\"stat\">\r\n              <div className=\"stat-value\">${userStats.totalSpent.toFixed(2)}</div>\r\n              <div className=\"stat-label\">Total Spent</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Loyalty Progress */}\r\n        {loyaltyInfo.next && (\r\n          <div className=\"loyalty-progress\">\r\n            <div className=\"progress-header\">\r\n              <span>Progress to {loyaltyInfo.next === 1000 ? 'Platinum' : loyaltyInfo.next === 500 ? 'Gold' : 'Silver'}</span>\r\n              <span>{userStats.loyaltyPoints} / {loyaltyInfo.next} points</span>\r\n            </div>\r\n            <div className=\"progress-bar\">\r\n              <div\r\n                className=\"progress-fill\"\r\n                style={{\r\n                  width: `${(userStats.loyaltyPoints / loyaltyInfo.next) * 100}%`,\r\n                  backgroundColor: loyaltyInfo.color\r\n                }}\r\n              ></div>\r\n            </div>\r\n            <p className=\"progress-text\">\r\n              Earn {loyaltyInfo.next - userStats.loyaltyPoints} more points to reach the next level!\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Navigation Tabs */}\r\n        <div className=\"profile-tabs\">\r\n          <button\r\n            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('overview')}\r\n          >\r\n            <i className=\"fas fa-chart-line\"></i>\r\n            Overview\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'orders' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('orders')}\r\n          >\r\n            <i className=\"fas fa-box\"></i>\r\n            Order History\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'settings' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('settings')}\r\n          >\r\n            <i className=\"fas fa-cog\"></i>\r\n            Account Settings\r\n          </button>\r\n          <button\r\n            className={`tab ${activeTab === 'rewards' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('rewards')}\r\n          >\r\n            <i className=\"fas fa-gift\"></i>\r\n            Rewards\r\n          </button>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        <div className=\"tab-content\">\r\n          {activeTab === 'overview' && (\r\n            <div className=\"overview-content\">\r\n              <div className=\"overview-grid\">\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-leaf\"></i>\r\n                      Eco Impact\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"eco-stats\">\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.co2Saved} kg</div>\r\n                      <div className=\"eco-label\">CO₂ Saved</div>\r\n                    </div>\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.ecoProducts}</div>\r\n                      <div className=\"eco-label\">Eco Products</div>\r\n                    </div>\r\n                    <div className=\"eco-stat\">\r\n                      <div className=\"eco-value\">{userStats.ecoImpact.treesPlanted}</div>\r\n                      <div className=\"eco-label\">Trees Planted</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-clock\"></i>\r\n                      Recent Activity\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"activity-list\">\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-shopping-cart activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Ordered Solar Power Bank</div>\r\n                        <div className=\"activity-date\">2 days ago</div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-heart activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Added Bamboo Toothbrush to wishlist</div>\r\n                        <div className=\"activity-date\">1 week ago</div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"activity-item\">\r\n                      <i className=\"fas fa-star activity-icon\"></i>\r\n                      <div>\r\n                        <div className=\"activity-text\">Reviewed Organic Cotton T-Shirt</div>\r\n                        <div className=\"activity-date\">2 weeks ago</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"overview-card\">\r\n                  <div className=\"card-header\">\r\n                    <h3>\r\n                      <i className=\"fas fa-trophy\"></i>\r\n                      Achievements\r\n                    </h3>\r\n                  </div>\r\n                  <div className=\"achievements\">\r\n                    <div className=\"achievement earned\">\r\n                      <i className=\"fas fa-seedling\"></i>\r\n                      <span>Eco Warrior</span>\r\n                    </div>\r\n                    <div className=\"achievement earned\">\r\n                      <i className=\"fas fa-shopping-bag\"></i>\r\n                      <span>First Purchase</span>\r\n                    </div>\r\n                    <div className=\"achievement\">\r\n                      <i className=\"fas fa-users\"></i>\r\n                      <span>Referral Master</span>\r\n                    </div>\r\n                    <div className=\"achievement\">\r\n                      <i className=\"fas fa-calendar\"></i>\r\n                      <span>Monthly Shopper</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'orders' && (\r\n            <div className=\"orders-content\">\r\n              <div className=\"orders-header\">\r\n                <h3>Order History</h3>\r\n                <div className=\"order-filters\">\r\n                  <select className=\"filter-select\">\r\n                    <option value=\"all\">All Orders</option>\r\n                    <option value=\"delivered\">Delivered</option>\r\n                    <option value=\"shipped\">Shipped</option>\r\n                    <option value=\"processing\">Processing</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"orders-list\">\r\n                {orders.map(order => (\r\n                  <OrderCard\r\n                    key={order.id}\r\n                    order={order}\r\n                    onReorder={handleReorder}\r\n                    onReview={handleReview}\r\n                  />\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'settings' && (\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-header\">\r\n                <h3>Account Settings</h3>\r\n                <button\r\n                  onClick={() => setEditMode(!editMode)}\r\n                  className={`edit-btn ${editMode ? 'active' : ''}`}\r\n                >\r\n                  <i className={`fas ${editMode ? 'fa-times' : 'fa-edit'}`}></i>\r\n                  {editMode ? 'Cancel' : 'Edit Profile'}\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h4>Profile Picture</h4>\r\n                  <div className=\"profile-picture-section\">\r\n                    <div className=\"current-picture\">\r\n                      <div className=\"picture-preview\">\r\n                        {profileImage ? (\r\n                          <img src={profileImage} alt=\"Profile\" />\r\n                        ) : (\r\n                          <div className=\"picture-placeholder\">\r\n                            {getInitials(profile.name)}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"picture-info\">\r\n                        <p>Your profile picture is visible to other users</p>\r\n                        <button\r\n                          onClick={() => setShowImageUpload(true)}\r\n                          className=\"change-picture-btn\"\r\n                        >\r\n                          <i className=\"fas fa-camera\"></i>\r\n                          Change Picture\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Personal Information</h4>\r\n                  <div className=\"form-row\">\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"name\">Full Name</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"name\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"email\">Email Address</label>\r\n                      <input\r\n                        type=\"email\"\r\n                        id=\"email\"\r\n                        name=\"email\"\r\n                        value={formData.email}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"phone\">Phone Number</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      id=\"phone\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      disabled={!editMode}\r\n                      placeholder=\"(*************\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Shipping Address</h4>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"address.street\">Street Address</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"address.street\"\r\n                      name=\"address.street\"\r\n                      value={formData.address.street}\r\n                      onChange={handleInputChange}\r\n                      disabled={!editMode}\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-row\">\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.city\">City</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.city\"\r\n                        name=\"address.city\"\r\n                        value={formData.address.city}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.state\">State</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.state\"\r\n                        name=\"address.state\"\r\n                        value={formData.address.state}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"address.zipCode\">ZIP Code</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"address.zipCode\"\r\n                        name=\"address.zipCode\"\r\n                        value={formData.address.zipCode}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h4>Preferences</h4>\r\n                  <div className=\"preferences-grid\">\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.newsletter\"\r\n                        checked={formData.preferences.newsletter}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Newsletter</strong>\r\n                        <p>Receive updates about new eco-friendly products</p>\r\n                      </div>\r\n                    </label>\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.notifications\"\r\n                        checked={formData.preferences.notifications}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Order Notifications</strong>\r\n                        <p>Get notified about order status updates</p>\r\n                      </div>\r\n                    </label>\r\n                    <label className=\"checkbox-label\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"preferences.ecoTips\"\r\n                        checked={formData.preferences.ecoTips}\r\n                        onChange={handleInputChange}\r\n                        disabled={!editMode}\r\n                      />\r\n                      <span className=\"checkmark\"></span>\r\n                      <div>\r\n                        <strong>Eco Tips</strong>\r\n                        <p>Receive sustainability tips and advice</p>\r\n                      </div>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                {editMode && (\r\n                  <div className=\"form-actions\">\r\n                    <button onClick={() => setEditMode(false)} className=\"btn-secondary\">\r\n                      Cancel\r\n                    </button>\r\n                    <button onClick={handleSaveProfile} className=\"btn-primary\">\r\n                      <i className=\"fas fa-save\"></i>\r\n                      Save Changes\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === 'rewards' && (\r\n            <div className=\"rewards-content\">\r\n              <div className=\"rewards-header\">\r\n                <h3>Loyalty Rewards</h3>\r\n                <div className=\"points-balance\">\r\n                  <span className=\"points-value\">{userStats.loyaltyPoints}</span>\r\n                  <span className=\"points-label\">Available Points</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"rewards-grid\">\r\n                {availableRewards.map(reward => (\r\n                  <RewardCard\r\n                    key={reward.id}\r\n                    reward={reward}\r\n                    userPoints={userStats.loyaltyPoints}\r\n                    onRedeem={handleRedeemReward}\r\n                  />\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"earn-points\">\r\n                <h4>How to Earn More Points</h4>\r\n                <div className=\"earn-methods\">\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-shopping-cart\"></i>\r\n                    <div>\r\n                      <strong>Make Purchases</strong>\r\n                      <p>Earn 1 point for every $1 spent</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-star\"></i>\r\n                    <div>\r\n                      <strong>Write Reviews</strong>\r\n                      <p>Get 10 points for each product review</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-users\"></i>\r\n                    <div>\r\n                      <strong>Refer Friends</strong>\r\n                      <p>Earn 50 points for each successful referral</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"earn-method\">\r\n                    <i className=\"fas fa-birthday-cake\"></i>\r\n                    <div>\r\n                      <strong>Birthday Bonus</strong>\r\n                      <p>Get 100 points on your birthday</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Logout Button */}\r\n        <div className=\"profile-footer\">\r\n          <button onClick={logout} className=\"logout-btn\">\r\n            <i className=\"fas fa-sign-out-alt\"></i>\r\n            Sign Out\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Image Upload Modal */}\r\n      {showImageUpload && (\r\n        <ImageUpload\r\n          currentImage={profileImage}\r\n          onImageChange={handleImageChange}\r\n          onClose={() => setShowImageUpload(false)}\r\n        />\r\n      )}\r\n\r\n      <style jsx>{`\r\n        .profile-container {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n          padding: 2rem;\r\n        }\r\n\r\n        .auth-required {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-height: 60vh;\r\n          text-align: center;\r\n          gap: 1.5rem;\r\n        }\r\n\r\n        .auth-icon {\r\n          font-size: 4rem;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .login-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 1rem 2rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .profile-header {\r\n          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\r\n          color: white;\r\n          padding: 2rem;\r\n          border-radius: 1rem;\r\n          display: grid;\r\n          grid-template-columns: auto 1fr auto;\r\n          gap: 2rem;\r\n          align-items: center;\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .profile-avatar-container {\r\n          position: relative;\r\n          display: inline-block;\r\n        }\r\n\r\n        .profile-avatar {\r\n          width: 100px;\r\n          height: 100px;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          overflow: hidden;\r\n          border: 4px solid rgba(255, 255, 255, 0.3);\r\n          transition: all 0.3s ease;\r\n        }\r\n\r\n        .profile-avatar img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .avatar-initials {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          width: 100%;\r\n          height: 100%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n          color: white;\r\n        }\r\n\r\n        .edit-avatar-btn {\r\n          position: absolute;\r\n          bottom: 0;\r\n          right: 0;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: 2px solid white;\r\n          border-radius: 50%;\r\n          width: 36px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          font-size: 0.875rem;\r\n          transition: all 0.2s ease;\r\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        .edit-avatar-btn:hover {\r\n          background: var(--primary-dark);\r\n          transform: scale(1.1);\r\n        }\r\n\r\n        .profile-avatar-container:hover .profile-avatar {\r\n          transform: scale(1.05);\r\n          border-color: rgba(255, 255, 255, 0.5);\r\n        }\r\n\r\n        .profile-info h1 {\r\n          margin: 0 0 0.5rem 0;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n        }\r\n\r\n        .profile-email {\r\n          opacity: 0.9;\r\n          margin: 0 0 1rem 0;\r\n        }\r\n\r\n        .loyalty-badge {\r\n          display: inline-flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          padding: 0.5rem 1rem;\r\n          border-radius: 9999px;\r\n          font-weight: 600;\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .profile-stats {\r\n          display: flex;\r\n          gap: 2rem;\r\n        }\r\n\r\n        .stat {\r\n          text-align: center;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 0.875rem;\r\n          opacity: 0.9;\r\n          margin-top: 0.25rem;\r\n        }\r\n\r\n        .loyalty-progress {\r\n          background: white;\r\n          padding: 1.5rem;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .progress-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 1rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .progress-bar {\r\n          height: 8px;\r\n          background: var(--bg-secondary);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-bottom: 0.75rem;\r\n        }\r\n\r\n        .progress-fill {\r\n          height: 100%;\r\n          transition: width 0.3s ease;\r\n        }\r\n\r\n        .progress-text {\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n          margin: 0;\r\n        }\r\n\r\n        .profile-tabs {\r\n          display: flex;\r\n          gap: 0.5rem;\r\n          margin-bottom: 2rem;\r\n          background: white;\r\n          padding: 0.5rem;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .tab {\r\n          flex: 1;\r\n          background: none;\r\n          border: none;\r\n          padding: 1rem;\r\n          border-radius: 0.75rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          color: var(--text-secondary);\r\n          transition: all 0.2s;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .tab:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .tab.active {\r\n          background: var(--primary-color);\r\n          color: white;\r\n        }\r\n\r\n        .tab-content {\r\n          background: white;\r\n          border-radius: 1rem;\r\n          box-shadow: var(--shadow-sm);\r\n          border: 1px solid var(--border-color);\r\n          overflow: hidden;\r\n        }\r\n\r\n        .overview-grid {\r\n          display: grid;\r\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n          gap: 2rem;\r\n          padding: 2rem;\r\n        }\r\n\r\n        .overview-card {\r\n          background: var(--bg-secondary);\r\n          border-radius: 1rem;\r\n          padding: 1.5rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .card-header {\r\n          margin-bottom: 1.5rem;\r\n        }\r\n\r\n        .card-header h3 {\r\n          margin: 0;\r\n          font-size: 1.25rem;\r\n          font-weight: 600;\r\n          color: var(--text-primary);\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .eco-stats {\r\n          display: grid;\r\n          grid-template-columns: repeat(3, 1fr);\r\n          gap: 1rem;\r\n        }\r\n\r\n        .eco-stat {\r\n          text-align: center;\r\n        }\r\n\r\n        .eco-value {\r\n          font-size: 1.5rem;\r\n          font-weight: 700;\r\n          color: var(--secondary-color);\r\n          line-height: 1;\r\n        }\r\n\r\n        .eco-label {\r\n          font-size: 0.875rem;\r\n          color: var(--text-secondary);\r\n          margin-top: 0.25rem;\r\n        }\r\n\r\n        .activity-list {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 1rem;\r\n        }\r\n\r\n        .activity-item {\r\n          display: flex;\r\n          gap: 1rem;\r\n          align-items: flex-start;\r\n        }\r\n\r\n        .activity-icon {\r\n          width: 40px;\r\n          height: 40px;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .activity-text {\r\n          font-weight: 500;\r\n          color: var(--text-primary);\r\n        }\r\n\r\n        .activity-date {\r\n          font-size: 0.875rem;\r\n          color: var(--text-secondary);\r\n        }\r\n\r\n        .achievements {\r\n          display: grid;\r\n          grid-template-columns: repeat(2, 1fr);\r\n          gap: 1rem;\r\n        }\r\n\r\n        .achievement {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          padding: 1rem;\r\n          border-radius: 0.75rem;\r\n          border: 2px solid var(--border-color);\r\n          opacity: 0.5;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .achievement.earned {\r\n          opacity: 1;\r\n          background: linear-gradient(135deg, var(--secondary-color), #059669);\r\n          color: white;\r\n          border-color: var(--secondary-color);\r\n        }\r\n\r\n        .achievement i {\r\n          font-size: 1.25rem;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .profile-header {\r\n            grid-template-columns: 1fr;\r\n            text-align: center;\r\n            gap: 1.5rem;\r\n          }\r\n\r\n          .profile-avatar-container {\r\n            align-self: center;\r\n            justify-self: center;\r\n          }\r\n\r\n          .profile-avatar {\r\n            width: 120px;\r\n            height: 120px;\r\n          }\r\n\r\n          .edit-avatar-btn {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: 1rem;\r\n          }\r\n\r\n          .profile-stats {\r\n            justify-content: center;\r\n          }\r\n\r\n          .profile-tabs {\r\n            flex-direction: column;\r\n          }\r\n\r\n          .overview-grid {\r\n            grid-template-columns: 1fr;\r\n            padding: 1rem;\r\n          }\r\n\r\n          .current-picture {\r\n            flex-direction: column;\r\n            text-align: center;\r\n            gap: 1rem;\r\n          }\r\n        }\r\n\r\n        /* Profile Picture Section Styles */\r\n        .profile-picture-section {\r\n          margin-bottom: 2rem;\r\n        }\r\n\r\n        .current-picture {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 2rem;\r\n          padding: 1.5rem;\r\n          background: var(--bg-secondary);\r\n          border-radius: 1rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .picture-preview {\r\n          width: 100px;\r\n          height: 100px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n          border: 3px solid var(--border-color);\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .picture-preview img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .picture-placeholder {\r\n          width: 100%;\r\n          height: 100%;\r\n          background: var(--primary-color);\r\n          color: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 2rem;\r\n          font-weight: 700;\r\n        }\r\n\r\n        .picture-info {\r\n          flex: 1;\r\n        }\r\n\r\n        .picture-info p {\r\n          margin: 0 0 1rem 0;\r\n          color: var(--text-secondary);\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        .change-picture-btn {\r\n          background: var(--primary-color);\r\n          color: white;\r\n          border: none;\r\n          padding: 0.75rem 1.5rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .change-picture-btn:hover {\r\n          background: var(--primary-dark);\r\n        }\r\n      `}</style>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default ProfilePage;"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC9D,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,gBAAgB,KAAQ,6BAA6B,CAC9D,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CACrD,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,WAAWA,CAAA,CAAG,CACrB,KAAM,CAAEC,KAAK,CAAEC,IAAI,CAAEC,MAAO,CAAC,CAAGlB,UAAU,CAACG,WAAW,CAAC,CACvD,KAAM,CAAEgB,SAAS,CAAEC,MAAM,CAAEC,YAAa,CAAC,CAAGrB,UAAU,CAACI,gBAAgB,CAAC,CACxE,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwB,KAAK,CAAEC,QAAQ,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,UAAU,CAAC,CACtD,KAAM,CAAC4B,QAAQ,CAAEC,WAAW,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC8B,eAAe,CAAEC,kBAAkB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgC,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACkC,QAAQ,CAAEC,WAAW,CAAC,CAAGnC,QAAQ,CAAC,CACvCoC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,CACPC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CACDC,WAAW,CAAE,CACXC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,IACX,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,gBAAgB,CAAG,CACvB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,sDAAsD,CACnEC,IAAI,CAAE,GAAG,CACTC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,CAAC,mBAAmB,CAAE,sBAAsB,CAAE,qBAAqB,CAC/E,CAAC,CACD,CACEP,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,sDAAsD,CACnEC,IAAI,CAAE,EAAE,CACRC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,aAAa,CACpBE,QAAQ,CAAE,CAAC,mBAAmB,CAAE,gBAAgB,CAAE,4BAA4B,CAChF,CAAC,CACD,CACEP,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,0DAA0D,CACvEC,IAAI,CAAE,GAAG,CACTC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,YAAY,CACnBG,OAAO,CAAE,KAAK,CACdD,QAAQ,CAAE,CAAC,kBAAkB,CAAE,gBAAgB,CAAE,uBAAuB,CAC1E,CAAC,CACD,CACEP,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,6DAA6D,CAC1EC,IAAI,CAAE,GAAG,CACTC,IAAI,CAAE,KAAK,CACXC,KAAK,CAAE,sBAAsB,CAC7BE,QAAQ,CAAE,CAAC,sBAAsB,CAAE,iBAAiB,CAAE,gBAAgB,CACxE,CAAC,CACF,CAED1D,SAAS,CAAC,IAAM,CACd,GAAIe,KAAK,CAAE,CACT6C,YAAY,CAAC,CAAC,CAChB,CAAC,IAAM,CACLpC,UAAU,CAAC,KAAK,CAAC,CACjBE,QAAQ,CAAC,mCAAmC,CAAC,CAC/C,CACF,CAAC,CAAE,CAACX,KAAK,CAAC,CAAC,CAEX,KAAM,CAAA6C,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFpC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAqC,QAAQ,CAAG,KAAM,CAAApD,KAAK,CAACqD,GAAG,CAAC,yCAAyC,CAAE,CAC1EC,OAAO,CAAE,CAAEC,aAAa,WAAAC,MAAA,CAAYlD,KAAK,CAAG,CAC9C,CAAC,CAAC,CACFO,UAAU,CAACuC,QAAQ,CAACK,IAAI,CAAC,CACzBhC,eAAe,CAAC2B,QAAQ,CAACK,IAAI,CAACjC,YAAY,EAAI,IAAI,CAAC,CACnDG,WAAW,CAAC,CACVC,IAAI,CAAEwB,QAAQ,CAACK,IAAI,CAAC7B,IAAI,EAAI,EAAE,CAC9BC,KAAK,CAAEuB,QAAQ,CAACK,IAAI,CAAC5B,KAAK,EAAI,EAAE,CAChCC,KAAK,CAAEsB,QAAQ,CAACK,IAAI,CAAC3B,KAAK,EAAI,EAAE,CAChCC,OAAO,CAAEqB,QAAQ,CAACK,IAAI,CAAC1B,OAAO,EAAI,CAChCC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CACDC,WAAW,CAAEe,QAAQ,CAACK,IAAI,CAACpB,WAAW,EAAI,CACxCC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,IACX,CACF,CAAC,CAAC,CACJ,CAAE,MAAOkB,GAAG,CAAE,CACZzC,QAAQ,CAAC,wBAAwB,CAAC,CACpC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4C,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEhC,IAAI,CAAEmB,KAAK,CAAED,IAAI,CAAEe,OAAQ,CAAC,CAAGD,CAAC,CAACE,MAAM,CAE/C,GAAIlC,IAAI,CAACmC,QAAQ,CAAC,GAAG,CAAC,CAAE,CACtB,KAAM,CAACC,MAAM,CAAEC,KAAK,CAAC,CAAGrC,IAAI,CAACsC,KAAK,CAAC,GAAG,CAAC,CACvCvC,WAAW,CAACwC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACH,MAAM,EAAAI,aAAA,CAAAA,aAAA,IACFD,IAAI,CAACH,MAAM,CAAC,MACf,CAACC,KAAK,EAAGnB,IAAI,GAAK,UAAU,CAAGe,OAAO,CAAGd,KAAK,EAC/C,EACD,CAAC,CACL,CAAC,IAAM,CACLpB,WAAW,CAACwC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACvC,IAAI,EAAGkB,IAAI,GAAK,UAAU,CAAGe,OAAO,CAAGd,KAAK,EAC7C,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAsB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF;AACAxD,UAAU,CAAAuD,aAAA,CAAAA,aAAA,CAAAA,aAAA,IAAMxD,OAAO,EAAKc,QAAQ,MAAEF,YAAY,EAAE,CAAC,CACrDH,WAAW,CAAC,KAAK,CAAC,CAClB;AACF,CAAE,MAAOqC,GAAG,CAAE,CACZzC,QAAQ,CAAC,0BAA0B,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAqD,iBAAiB,CAAIC,QAAQ,EAAK,CACtC9C,eAAe,CAAC8C,QAAQ,CAAC,CACzB;AACA1D,UAAU,CAACsD,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE3C,YAAY,CAAE+C,QAAQ,EAAG,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAC,WAAW,CAAI5C,IAAI,EAAK,CAC5B,MAAO,CAAAA,IAAI,CACRsC,KAAK,CAAC,GAAG,CAAC,CACVO,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAChB,CAAC,CAID,KAAM,CAAAC,eAAe,CAAIC,MAAM,EAAK,CAClC,GAAIA,MAAM,EAAI,IAAI,CAAE,MAAO,CAAEC,KAAK,CAAE,UAAU,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC9E,GAAIH,MAAM,EAAI,GAAG,CAAE,MAAO,CAAEC,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CACzE,GAAIH,MAAM,EAAI,GAAG,CAAE,MAAO,CAAEC,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,GAAI,CAAC,CAC1E,MAAO,CAAEF,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,GAAI,CAAC,CACzD,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIC,KAAK,EAAK,CAC/B;AACAC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEF,KAAK,CAAC,CACnC,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIH,KAAK,EAAK,CAC9B;AACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEF,KAAK,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAG,KAAO,CAAAC,MAAM,EAAK,CAC3C,GAAI,CACF,KAAM,CAAA/E,YAAY,CAAC+E,MAAM,CAAC7C,IAAI,CAAC,CAC/B8C,KAAK,2BAAAnC,MAAA,CAA2BkC,MAAM,CAAC/C,KAAK,CAAE,CAAC,CACjD,CAAE,MAAO3B,KAAK,CAAE,CACd2E,KAAK,CAAC,yBAAyB,CAAC,CAClC,CACF,CAAC,CAED,GAAI7E,OAAO,CAAE,CACX,mBAAOZ,IAAA,CAACP,cAAc,EAACiG,IAAI,CAAC,OAAO,CAACC,OAAO,CAAC,yBAAyB,CAACC,UAAU,MAAE,CAAC,CACrF,CAEA,GAAI9E,KAAK,CAAE,CACT,mBAAOd,IAAA,CAACN,YAAY,EAAC+C,KAAK,CAAC,eAAe,CAACkD,OAAO,CAAE7E,KAAM,CAAC+E,OAAO,CAAE5C,YAAa,CAAE,CAAC,CACtF,CAEA,GAAI,CAACvC,OAAO,CAAE,CACZ,mBACEV,IAAA,SAAA8F,QAAA,cACE5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB9F,IAAA,MAAG+F,SAAS,CAAC,kBAAkB,CAAI,CAAC,CACjC,CAAC,cACN/F,IAAA,OAAA8F,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC9F,IAAA,MAAA8F,QAAA,CAAG,oCAAkC,CAAG,CAAC,cACzC5F,KAAA,WAAQ8F,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAS,CAACJ,SAAS,CAAC,WAAW,CAAAD,QAAA,eAC3E9F,IAAA,MAAG+F,SAAS,CAAC,oBAAoB,CAAI,CAAC,cAExC,EAAQ,CAAC,EACN,CAAC,CACF,CAAC,CAEX,CAEA,KAAM,CAAAK,WAAW,CAAGvB,eAAe,CAACtE,SAAS,CAAC8F,aAAa,EAAI,CAAC,CAAC,CAEjE,mBACEnG,KAAA,SAAA4F,QAAA,eACE5F,KAAA,QAAK6F,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAEhC5F,KAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B5F,KAAA,QAAK6F,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC9F,IAAA,QAAK+F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,CAC5BxE,YAAY,cACXtB,IAAA,QAAKsG,GAAG,CAAEhF,YAAa,CAACiF,GAAG,CAAE7F,OAAO,CAACgB,IAAK,CAAE,CAAC,cAE7C1B,IAAA,QAAK+F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7BxB,WAAW,CAAC5D,OAAO,CAACgB,IAAI,CAAC,CACvB,CACN,CACE,CAAC,cACN1B,IAAA,WACE+F,SAAS,CAAC,iBAAiB,CAC3BC,OAAO,CAAEA,CAAA,GAAM3E,kBAAkB,CAAC,IAAI,CAAE,CACxCoB,KAAK,CAAC,wBAAwB,CAAAqD,QAAA,cAE9B9F,IAAA,MAAG+F,SAAS,CAAC,eAAe,CAAI,CAAC,CAC3B,CAAC,EACN,CAAC,cACN7F,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B5F,KAAA,OAAA4F,QAAA,EAAI,gBAAc,CAACpF,OAAO,CAACgB,IAAI,CAAC,GAAC,EAAI,CAAC,cACtC1B,IAAA,MAAG+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAEpF,OAAO,CAACiB,KAAK,CAAI,CAAC,cAChDzB,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAACS,KAAK,CAAE,CAAEC,eAAe,CAAEL,WAAW,CAACpB,KAAM,CAAE,CAAAc,QAAA,eAC3E9F,IAAA,MAAG+F,SAAS,CAAC,cAAc,CAAI,CAAC,CAC/BK,WAAW,CAACrB,KAAK,CAAC,SACrB,EAAK,CAAC,EACH,CAAC,cACN7E,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B5F,KAAA,QAAK6F,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB9F,IAAA,QAAK+F,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEvF,SAAS,CAAC8F,aAAa,CAAM,CAAC,cAC3DrG,IAAA,QAAK+F,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,EAC7C,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB9F,IAAA,QAAK+F,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEvF,SAAS,CAACmG,WAAW,CAAM,CAAC,cACzD1G,IAAA,QAAK+F,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,EAC3C,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB5F,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,EAAC,GAAC,CAACvF,SAAS,CAACoG,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cACpE5G,IAAA,QAAK+F,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,aAAW,CAAK,CAAC,EAC1C,CAAC,EACH,CAAC,EACH,CAAC,CAGLM,WAAW,CAACnB,IAAI,eACf/E,KAAA,QAAK6F,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B5F,KAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B5F,KAAA,SAAA4F,QAAA,EAAM,cAAY,CAACM,WAAW,CAACnB,IAAI,GAAK,IAAI,CAAG,UAAU,CAAGmB,WAAW,CAACnB,IAAI,GAAK,GAAG,CAAG,MAAM,CAAG,QAAQ,EAAO,CAAC,cAChH/E,KAAA,SAAA4F,QAAA,EAAOvF,SAAS,CAAC8F,aAAa,CAAC,KAAG,CAACD,WAAW,CAACnB,IAAI,CAAC,SAAO,EAAM,CAAC,EAC/D,CAAC,cACNjF,IAAA,QAAK+F,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3B9F,IAAA,QACE+F,SAAS,CAAC,eAAe,CACzBS,KAAK,CAAE,CACLK,KAAK,IAAAvD,MAAA,CAAM/C,SAAS,CAAC8F,aAAa,CAAGD,WAAW,CAACnB,IAAI,CAAI,GAAG,KAAG,CAC/DwB,eAAe,CAAEL,WAAW,CAACpB,KAC/B,CAAE,CACE,CAAC,CACJ,CAAC,cACN9E,KAAA,MAAG6F,SAAS,CAAC,eAAe,CAAAD,QAAA,EAAC,OACtB,CAACM,WAAW,CAACnB,IAAI,CAAG1E,SAAS,CAAC8F,aAAa,CAAC,uCACnD,EAAG,CAAC,EACD,CACN,cAGDnG,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B5F,KAAA,WACE6F,SAAS,QAAAzC,MAAA,CAAStC,SAAS,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC7DgF,OAAO,CAAEA,CAAA,GAAM/E,YAAY,CAAC,UAAU,CAAE,CAAA6E,QAAA,eAExC9F,IAAA,MAAG+F,SAAS,CAAC,mBAAmB,CAAI,CAAC,WAEvC,EAAQ,CAAC,cACT7F,KAAA,WACE6F,SAAS,QAAAzC,MAAA,CAAStC,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC3DgF,OAAO,CAAEA,CAAA,GAAM/E,YAAY,CAAC,QAAQ,CAAE,CAAA6E,QAAA,eAEtC9F,IAAA,MAAG+F,SAAS,CAAC,YAAY,CAAI,CAAC,gBAEhC,EAAQ,CAAC,cACT7F,KAAA,WACE6F,SAAS,QAAAzC,MAAA,CAAStC,SAAS,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC7DgF,OAAO,CAAEA,CAAA,GAAM/E,YAAY,CAAC,UAAU,CAAE,CAAA6E,QAAA,eAExC9F,IAAA,MAAG+F,SAAS,CAAC,YAAY,CAAI,CAAC,mBAEhC,EAAQ,CAAC,cACT7F,KAAA,WACE6F,SAAS,QAAAzC,MAAA,CAAStC,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC5DgF,OAAO,CAAEA,CAAA,GAAM/E,YAAY,CAAC,SAAS,CAAE,CAAA6E,QAAA,eAEvC9F,IAAA,MAAG+F,SAAS,CAAC,aAAa,CAAI,CAAC,UAEjC,EAAQ,CAAC,EACN,CAAC,cAGN7F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,EACzB9E,SAAS,GAAK,UAAU,eACvBhB,IAAA,QAAK+F,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/B5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,QAAK+F,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B5F,KAAA,OAAA4F,QAAA,eACE9F,IAAA,MAAG+F,SAAS,CAAC,aAAa,CAAI,CAAC,aAEjC,EAAI,CAAC,CACF,CAAC,cACN7F,KAAA,QAAK6F,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB5F,KAAA,QAAK6F,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB5F,KAAA,QAAK6F,SAAS,CAAC,WAAW,CAAAD,QAAA,EAAEvF,SAAS,CAACuG,SAAS,CAACC,QAAQ,CAAC,KAAG,EAAK,CAAC,cAClE/G,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,gBAAS,CAAK,CAAC,EACvC,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB9F,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAEvF,SAAS,CAACuG,SAAS,CAACE,WAAW,CAAM,CAAC,cAClEhH,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,EAC1C,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB9F,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAEvF,SAAS,CAACuG,SAAS,CAACG,YAAY,CAAM,CAAC,cACnEjH,IAAA,QAAK+F,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,eAAa,CAAK,CAAC,EAC3C,CAAC,EACH,CAAC,EACH,CAAC,cAEN5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,QAAK+F,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B5F,KAAA,OAAA4F,QAAA,eACE9F,IAAA,MAAG+F,SAAS,CAAC,cAAc,CAAI,CAAC,kBAElC,EAAI,CAAC,CACF,CAAC,cACN7F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,MAAG+F,SAAS,CAAC,oCAAoC,CAAI,CAAC,cACtD7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,0BAAwB,CAAK,CAAC,cAC7D9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,EAC5C,CAAC,EACH,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,MAAG+F,SAAS,CAAC,4BAA4B,CAAI,CAAC,cAC9C7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qCAAmC,CAAK,CAAC,cACxE9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,EAC5C,CAAC,EACH,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,MAAG+F,SAAS,CAAC,2BAA2B,CAAI,CAAC,cAC7C7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iCAA+B,CAAK,CAAC,cACpE9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAK,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,QAAK+F,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B5F,KAAA,OAAA4F,QAAA,eACE9F,IAAA,MAAG+F,SAAS,CAAC,eAAe,CAAI,CAAC,eAEnC,EAAI,CAAC,CACF,CAAC,cACN7F,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B5F,KAAA,QAAK6F,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eACjC9F,IAAA,MAAG+F,SAAS,CAAC,iBAAiB,CAAI,CAAC,cACnC/F,IAAA,SAAA8F,QAAA,CAAM,aAAW,CAAM,CAAC,EACrB,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eACjC9F,IAAA,MAAG+F,SAAS,CAAC,qBAAqB,CAAI,CAAC,cACvC/F,IAAA,SAAA8F,QAAA,CAAM,gBAAc,CAAM,CAAC,EACxB,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,cAAc,CAAI,CAAC,cAChC/F,IAAA,SAAA8F,QAAA,CAAM,iBAAe,CAAM,CAAC,EACzB,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,iBAAiB,CAAI,CAAC,cACnC/F,IAAA,SAAA8F,QAAA,CAAM,iBAAe,CAAM,CAAC,EACzB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAEA9E,SAAS,GAAK,QAAQ,eACrBd,KAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B5F,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9F,IAAA,OAAA8F,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB9F,IAAA,QAAK+F,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5B5F,KAAA,WAAQ6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC/B9F,IAAA,WAAQ6C,KAAK,CAAC,KAAK,CAAAiD,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvC9F,IAAA,WAAQ6C,KAAK,CAAC,WAAW,CAAAiD,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5C9F,IAAA,WAAQ6C,KAAK,CAAC,SAAS,CAAAiD,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC9F,IAAA,WAAQ6C,KAAK,CAAC,YAAY,CAAAiD,QAAA,CAAC,YAAU,CAAQ,CAAC,EACxC,CAAC,CACN,CAAC,EACH,CAAC,cAEN9F,IAAA,QAAK+F,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBtF,MAAM,CAAC+D,GAAG,CAACY,KAAK,eACfnF,IAAA,CAACJ,SAAS,EAERuF,KAAK,CAAEA,KAAM,CACb+B,SAAS,CAAEhC,aAAc,CACzBiC,QAAQ,CAAE7B,YAAa,EAHlBH,KAAK,CAAC3C,EAIZ,CACF,CAAC,CACC,CAAC,EACH,CACN,CAEAxB,SAAS,GAAK,UAAU,eACvBd,KAAA,QAAK6F,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B5F,KAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B9F,IAAA,OAAA8F,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB5F,KAAA,WACE8F,OAAO,CAAEA,CAAA,GAAM7E,WAAW,CAAC,CAACD,QAAQ,CAAE,CACtC6E,SAAS,aAAAzC,MAAA,CAAcpC,QAAQ,CAAG,QAAQ,CAAG,EAAE,CAAG,CAAA4E,QAAA,eAElD9F,IAAA,MAAG+F,SAAS,QAAAzC,MAAA,CAASpC,QAAQ,CAAG,UAAU,CAAG,SAAS,CAAG,CAAI,CAAC,CAC7DA,QAAQ,CAAG,QAAQ,CAAG,cAAc,EAC/B,CAAC,EACN,CAAC,cAENhB,KAAA,QAAK6F,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B5F,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,OAAA8F,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB9F,IAAA,QAAK+F,SAAS,CAAC,yBAAyB,CAAAD,QAAA,cACtC5F,KAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B9F,IAAA,QAAK+F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7BxE,YAAY,cACXtB,IAAA,QAAKsG,GAAG,CAAEhF,YAAa,CAACiF,GAAG,CAAC,SAAS,CAAE,CAAC,cAExCvG,IAAA,QAAK+F,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CACjCxB,WAAW,CAAC5D,OAAO,CAACgB,IAAI,CAAC,CACvB,CACN,CACE,CAAC,cACNxB,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,MAAA8F,QAAA,CAAG,gDAA8C,CAAG,CAAC,cACrD5F,KAAA,WACE8F,OAAO,CAAEA,CAAA,GAAM3E,kBAAkB,CAAC,IAAI,CAAE,CACxC0E,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eAE9B9F,IAAA,MAAG+F,SAAS,CAAC,eAAe,CAAI,CAAC,iBAEnC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN7F,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,OAAA8F,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7B5F,KAAA,QAAK6F,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB5F,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,MAAM,CAAAtB,QAAA,CAAC,WAAS,CAAO,CAAC,cACvC9F,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXJ,EAAE,CAAC,MAAM,CACTd,IAAI,CAAC,MAAM,CACXmB,KAAK,CAAErB,QAAQ,CAACE,IAAK,CACrB2F,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,cACNhB,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,OAAO,CAAAtB,QAAA,CAAC,eAAa,CAAO,CAAC,cAC5C9F,IAAA,UACE4C,IAAI,CAAC,OAAO,CACZJ,EAAE,CAAC,OAAO,CACVd,IAAI,CAAC,OAAO,CACZmB,KAAK,CAAErB,QAAQ,CAACG,KAAM,CACtB0F,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,EACH,CAAC,cACNhB,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,OAAO,CAAAtB,QAAA,CAAC,cAAY,CAAO,CAAC,cAC3C9F,IAAA,UACE4C,IAAI,CAAC,KAAK,CACVJ,EAAE,CAAC,OAAO,CACVd,IAAI,CAAC,OAAO,CACZmB,KAAK,CAAErB,QAAQ,CAACI,KAAM,CACtByF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACpBqG,WAAW,CAAC,gBAAgB,CAC7B,CAAC,EACC,CAAC,EACH,CAAC,cAENrH,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,OAAA8F,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB5F,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,gBAAgB,CAAAtB,QAAA,CAAC,gBAAc,CAAO,CAAC,cACtD9F,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXJ,EAAE,CAAC,gBAAgB,CACnBd,IAAI,CAAC,gBAAgB,CACrBmB,KAAK,CAAErB,QAAQ,CAACK,OAAO,CAACC,MAAO,CAC/BuF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,cACNhB,KAAA,QAAK6F,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB5F,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,cAAc,CAAAtB,QAAA,CAAC,MAAI,CAAO,CAAC,cAC1C9F,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXJ,EAAE,CAAC,cAAc,CACjBd,IAAI,CAAC,cAAc,CACnBmB,KAAK,CAAErB,QAAQ,CAACK,OAAO,CAACE,IAAK,CAC7BsF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,cACNhB,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,eAAe,CAAAtB,QAAA,CAAC,OAAK,CAAO,CAAC,cAC5C9F,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXJ,EAAE,CAAC,eAAe,CAClBd,IAAI,CAAC,eAAe,CACpBmB,KAAK,CAAErB,QAAQ,CAACK,OAAO,CAACG,KAAM,CAC9BqF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,cACNhB,KAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB9F,IAAA,UAAOoH,OAAO,CAAC,iBAAiB,CAAAtB,QAAA,CAAC,UAAQ,CAAO,CAAC,cACjD9F,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXJ,EAAE,CAAC,iBAAiB,CACpBd,IAAI,CAAC,iBAAiB,CACtBmB,KAAK,CAAErB,QAAQ,CAACK,OAAO,CAACI,OAAQ,CAChCoF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,cAENhB,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,OAAA8F,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB5F,KAAA,QAAK6F,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B5F,KAAA,UAAO6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC/B9F,IAAA,UACE4C,IAAI,CAAC,UAAU,CACflB,IAAI,CAAC,wBAAwB,CAC7BiC,OAAO,CAAEnC,QAAQ,CAACW,WAAW,CAACC,UAAW,CACzCiF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,cACFlB,IAAA,SAAM+F,SAAS,CAAC,WAAW,CAAO,CAAC,cACnC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,YAAU,CAAQ,CAAC,cAC3B9F,IAAA,MAAA8F,QAAA,CAAG,iDAA+C,CAAG,CAAC,EACnD,CAAC,EACD,CAAC,cACR5F,KAAA,UAAO6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC/B9F,IAAA,UACE4C,IAAI,CAAC,UAAU,CACflB,IAAI,CAAC,2BAA2B,CAChCiC,OAAO,CAAEnC,QAAQ,CAACW,WAAW,CAACE,aAAc,CAC5CgF,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,cACFlB,IAAA,SAAM+F,SAAS,CAAC,WAAW,CAAO,CAAC,cACnC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,cACpC9F,IAAA,MAAA8F,QAAA,CAAG,yCAAuC,CAAG,CAAC,EAC3C,CAAC,EACD,CAAC,cACR5F,KAAA,UAAO6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC/B9F,IAAA,UACE4C,IAAI,CAAC,UAAU,CACflB,IAAI,CAAC,qBAAqB,CAC1BiC,OAAO,CAAEnC,QAAQ,CAACW,WAAW,CAACG,OAAQ,CACtC+E,QAAQ,CAAE5D,iBAAkB,CAC5B6D,QAAQ,CAAE,CAACpG,QAAS,CACrB,CAAC,cACFlB,IAAA,SAAM+F,SAAS,CAAC,WAAW,CAAO,CAAC,cACnC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,UAAQ,CAAQ,CAAC,cACzB9F,IAAA,MAAA8F,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,EACD,CAAC,EACL,CAAC,EACH,CAAC,CAEL5E,QAAQ,eACPhB,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B9F,IAAA,WAAQgG,OAAO,CAAEA,CAAA,GAAM7E,WAAW,CAAC,KAAK,CAAE,CAAC4E,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAErE,CAAQ,CAAC,cACT5F,KAAA,WAAQ8F,OAAO,CAAE7B,iBAAkB,CAAC4B,SAAS,CAAC,aAAa,CAAAD,QAAA,eACzD9F,IAAA,MAAG+F,SAAS,CAAC,aAAa,CAAI,CAAC,eAEjC,EAAQ,CAAC,EACN,CACN,EACE,CAAC,EACH,CACN,CAEA/E,SAAS,GAAK,SAAS,eACtBd,KAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B5F,KAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B9F,IAAA,OAAA8F,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB5F,KAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B9F,IAAA,SAAM+F,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAEvF,SAAS,CAAC8F,aAAa,CAAO,CAAC,cAC/DrG,IAAA,SAAM+F,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,cAEN9F,IAAA,QAAK+F,SAAS,CAAC,cAAc,CAAAD,QAAA,CAC1BvD,gBAAgB,CAACgC,GAAG,CAACiB,MAAM,eAC1BxF,IAAA,CAACH,UAAU,EAET2F,MAAM,CAAEA,MAAO,CACfgC,UAAU,CAAEjH,SAAS,CAAC8F,aAAc,CACpCoB,QAAQ,CAAElC,kBAAmB,EAHxBC,MAAM,CAAChD,EAIb,CACF,CAAC,CACC,CAAC,cAENtC,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,OAAA8F,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC5F,KAAA,QAAK6F,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,sBAAsB,CAAI,CAAC,cACxC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,gBAAc,CAAQ,CAAC,cAC/B9F,IAAA,MAAA8F,QAAA,CAAG,iCAA+B,CAAG,CAAC,EACnC,CAAC,EACH,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,eAAa,CAAQ,CAAC,cAC9B9F,IAAA,MAAA8F,QAAA,CAAG,uCAAqC,CAAG,CAAC,EACzC,CAAC,EACH,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,cAAc,CAAI,CAAC,cAChC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,eAAa,CAAQ,CAAC,cAC9B9F,IAAA,MAAA8F,QAAA,CAAG,6CAA2C,CAAG,CAAC,EAC/C,CAAC,EACH,CAAC,cACN5F,KAAA,QAAK6F,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B9F,IAAA,MAAG+F,SAAS,CAAC,sBAAsB,CAAI,CAAC,cACxC7F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,WAAA8F,QAAA,CAAQ,gBAAc,CAAQ,CAAC,cAC/B9F,IAAA,MAAA8F,QAAA,CAAG,iCAA+B,CAAG,CAAC,EACnC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,EACE,CAAC,cAGN9F,IAAA,QAAK+F,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC7B5F,KAAA,WAAQ8F,OAAO,CAAE1F,MAAO,CAACyF,SAAS,CAAC,YAAY,CAAAD,QAAA,eAC7C9F,IAAA,MAAG+F,SAAS,CAAC,qBAAqB,CAAI,CAAC,WAEzC,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAGL3E,eAAe,eACdpB,IAAA,CAACL,WAAW,EACV+H,YAAY,CAAEpG,YAAa,CAC3BqG,aAAa,CAAEvD,iBAAkB,CACjCwD,OAAO,CAAEA,CAAA,GAAMvG,kBAAkB,CAAC,KAAK,CAAE,CAC1C,CACF,cAEDrB,IAAA,UAAOD,GAAG,MAAA+F,QAAA,8hWA6cD,CAAC,EACN,CAAC,CAEX,CAEA,cAAe,CAAA3F,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}