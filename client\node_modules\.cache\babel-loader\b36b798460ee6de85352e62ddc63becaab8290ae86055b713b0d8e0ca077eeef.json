{"ast": null, "code": "import React,{useContext,useState}from'react';import{Link}from'react-router-dom';import{CartContext}from'../context/CartContext';import{AuthContext}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function Header(){const{cart}=useContext(CartContext);const{user,logout}=useContext(AuthContext);const[showUserMenu,setShowUserMenu]=useState(false);const[showMobileMenu,setShowMobileMenu]=useState(false);const cartItemCount=cart.reduce((total,item)=>total+item.quantity,0);// Close menus when clicking outside\nReact.useEffect(()=>{const handleClickOutside=event=>{if(!event.target.closest('.user-menu')&&!event.target.closest('.mobile-menu')){setShowUserMenu(false);setShowMobileMenu(false);}};document.addEventListener('click',handleClickOutside);return()=>document.removeEventListener('click',handleClickOutside);},[]);return/*#__PURE__*/_jsxs(\"header\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header-content\",children:[/*#__PURE__*/_jsx(\"h1\",{children:/*#__PURE__*/_jsxs(Link,{to:\"/\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-leaf\"}),\"EcoCommerce\"]})}),/*#__PURE__*/_jsxs(\"nav\",{className:\"desktop-nav\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/search\",className:\"nav-link\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Search\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/cart\",className:\"nav-link cart-link\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Cart\"}),cartItemCount>0&&/*#__PURE__*/_jsx(\"span\",{className:\"cart-badge\",children:cartItemCount})]}),/*#__PURE__*/_jsxs(Link,{to:\"/admin\",className:\"nav-link admin-link\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-cog\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Admin\"})]}),user?/*#__PURE__*/_jsxs(\"div\",{className:\"user-menu\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"user-button\",onClick:()=>setShowUserMenu(!showUserMenu),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user\"}),/*#__PURE__*/_jsx(\"span\",{children:user.name}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-down\"})]}),showUserMenu&&/*#__PURE__*/_jsxs(\"div\",{className:\"user-dropdown\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/profile\",onClick:()=>setShowUserMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user-circle\"}),\"Profile\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/wishlist\",onClick:()=>setShowUserMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-heart\"}),\"Wishlist\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/orders\",onClick:()=>setShowUserMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-box\"}),\"Orders\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{logout();setShowUserMenu(false);},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-out-alt\"}),\"Logout\"]})]})]}):/*#__PURE__*/_jsxs(Link,{to:\"/login\",className:\"nav-link\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-in-alt\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Login\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"mobile-menu-btn\",onClick:()=>setShowMobileMenu(!showMobileMenu),children:/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(showMobileMenu?'fa-times':'fa-bars')})})]}),showMobileMenu&&/*#__PURE__*/_jsx(\"div\",{className:\"mobile-menu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-menu-content\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/search\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search\"}),\"Search\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/cart\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"}),\"Cart\",cartItemCount>0&&/*#__PURE__*/_jsx(\"span\",{className:\"cart-badge\",children:cartItemCount})]}),/*#__PURE__*/_jsxs(Link,{to:\"/admin\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-cog\"}),\"Admin\"]}),user?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Link,{to:\"/profile\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user-circle\"}),\"Profile\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/wishlist\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-heart\"}),\"Wishlist\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/orders\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-box\"}),\"Orders\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"mobile-nav-link logout-btn\",onClick:()=>{logout();setShowMobileMenu(false);},children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-out-alt\"}),\"Logout\"]})]}):/*#__PURE__*/_jsxs(Link,{to:\"/login\",className:\"mobile-nav-link\",onClick:()=>setShowMobileMenu(false),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sign-in-alt\"}),\"Login\"]})]})}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        header {\\n          position: relative;\\n        }\\n\\n        .header-content {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          max-width: 1400px;\\n          margin: 0 auto;\\n          padding: 0 1rem;\\n        }\\n\\n        .desktop-nav {\\n          display: flex;\\n          gap: 2rem;\\n          align-items: center;\\n        }\\n\\n        .nav-link {\\n          position: relative;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .nav-link span {\\n          display: block;\\n        }\\n\\n        .cart-link {\\n          position: relative;\\n        }\\n\\n        .cart-badge {\\n          position: absolute;\\n          top: -8px;\\n          right: -8px;\\n          background: #ef4444;\\n          color: white;\\n          border-radius: 50%;\\n          width: 20px;\\n          height: 20px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 0.75rem;\\n          font-weight: 600;\\n          z-index: 10;\\n        }\\n\\n        .user-menu {\\n          position: relative;\\n        }\\n\\n        .user-button {\\n          background: rgba(255, 255, 255, 0.1);\\n          border: 1px solid rgba(255, 255, 255, 0.2);\\n          color: white;\\n          padding: 0.5rem 1rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          font-weight: 500;\\n          transition: all 0.2s;\\n        }\\n\\n        .user-button:hover {\\n          background: rgba(255, 255, 255, 0.2);\\n        }\\n\\n        .user-dropdown {\\n          position: absolute;\\n          top: 100%;\\n          right: 0;\\n          background: white;\\n          border-radius: 0.75rem;\\n          box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);\\n          padding: 0.75rem;\\n          min-width: 220px;\\n          z-index: 1000;\\n          margin-top: 0.5rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .user-dropdown a,\\n        .user-dropdown button {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n          padding: 0.75rem 1rem;\\n          color: #374151;\\n          text-decoration: none;\\n          border-radius: 0.5rem;\\n          transition: all 0.2s;\\n          width: 100%;\\n          border: none;\\n          background: none;\\n          cursor: pointer;\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n        }\\n\\n        .user-dropdown a:hover,\\n        .user-dropdown button:hover {\\n          background: var(--bg-secondary);\\n          color: var(--primary-color);\\n        }\\n\\n        .admin-link {\\n          background: rgba(255, 255, 255, 0.1);\\n          border-radius: 0.375rem;\\n          border: 1px solid rgba(255, 255, 255, 0.2);\\n        }\\n\\n        .admin-link:hover {\\n          background: rgba(255, 255, 255, 0.2);\\n          border-color: rgba(255, 255, 255, 0.3);\\n        }\\n\\n        .mobile-menu-btn {\\n          display: none;\\n          background: rgba(255, 255, 255, 0.1);\\n          border: 1px solid rgba(255, 255, 255, 0.2);\\n          color: white;\\n          padding: 0.75rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-size: 1.25rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .mobile-menu-btn:hover {\\n          background: rgba(255, 255, 255, 0.2);\\n        }\\n\\n        .mobile-menu {\\n          position: absolute;\\n          top: 100%;\\n          left: 0;\\n          right: 0;\\n          background: white;\\n          border-top: 1px solid var(--border-color);\\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\\n          z-index: 1000;\\n        }\\n\\n        .mobile-menu-content {\\n          max-width: 1400px;\\n          margin: 0 auto;\\n          padding: 1rem;\\n          display: flex;\\n          flex-direction: column;\\n          gap: 0.5rem;\\n        }\\n\\n        .mobile-nav-link {\\n          display: flex;\\n          align-items: center;\\n          gap: 1rem;\\n          padding: 1rem;\\n          color: var(--text-primary);\\n          text-decoration: none;\\n          border-radius: 0.5rem;\\n          transition: all 0.2s;\\n          font-weight: 500;\\n          position: relative;\\n        }\\n\\n        .mobile-nav-link:hover {\\n          background: var(--bg-secondary);\\n          color: var(--primary-color);\\n        }\\n\\n        .mobile-nav-link.logout-btn {\\n          background: none;\\n          border: none;\\n          width: 100%;\\n          text-align: left;\\n          cursor: pointer;\\n          font-size: 1rem;\\n        }\\n\\n        .mobile-nav-link i {\\n          width: 20px;\\n          text-align: center;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .desktop-nav {\\n            display: none;\\n          }\\n\\n          .mobile-menu-btn {\\n            display: block;\\n          }\\n\\n          .nav-link span {\\n            display: none;\\n          }\\n\\n          .header-content {\\n            padding: 0 1rem;\\n          }\\n\\n          header h1 {\\n            font-size: 1.5rem;\\n          }\\n        }\\n\\n        @media (max-width: 480px) {\\n          .nav-link span {\\n            display: none;\\n          }\\n\\n          .user-button span {\\n            display: none;\\n          }\\n\\n          .desktop-nav {\\n            gap: 1rem;\\n          }\\n        }\\n      \"})]});}export default Header;", "map": {"version": 3, "names": ["React", "useContext", "useState", "Link", "CartContext", "AuthContext", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Header", "cart", "user", "logout", "showUserMenu", "setShowUserMenu", "showMobileMenu", "setShowMobileMenu", "cartItemCount", "reduce", "total", "item", "quantity", "useEffect", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "children", "className", "to", "onClick", "name", "concat"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/Header.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { CartContext } from '../context/CartContext';\r\nimport { AuthContext } from '../context/AuthContext';\r\n\r\nfunction Header() {\r\n  const { cart } = useContext(CartContext);\r\n  const { user, logout } = useContext(AuthContext);\r\n  const [showUserMenu, setShowUserMenu] = useState(false);\r\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\r\n\r\n  const cartItemCount = cart.reduce((total, item) => total + item.quantity, 0);\r\n\r\n  // Close menus when clicking outside\r\n  React.useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (!event.target.closest('.user-menu') && !event.target.closest('.mobile-menu')) {\r\n        setShowUserMenu(false);\r\n        setShowMobileMenu(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('click', handleClickOutside);\r\n    return () => document.removeEventListener('click', handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <header>\r\n      <div className=\"header-content\">\r\n        <h1>\r\n          <Link to=\"/\">\r\n            <i className=\"fas fa-leaf\"></i>\r\n            EcoCommerce\r\n          </Link>\r\n        </h1>\r\n\r\n        {/* Desktop Navigation */}\r\n        <nav className=\"desktop-nav\">\r\n          <Link to=\"/search\" className=\"nav-link\">\r\n            <i className=\"fas fa-search\"></i>\r\n            <span>Search</span>\r\n          </Link>\r\n          <Link to=\"/cart\" className=\"nav-link cart-link\">\r\n            <i className=\"fas fa-shopping-cart\"></i>\r\n            <span>Cart</span>\r\n            {cartItemCount > 0 && (\r\n              <span className=\"cart-badge\">{cartItemCount}</span>\r\n            )}\r\n          </Link>\r\n          <Link to=\"/admin\" className=\"nav-link admin-link\">\r\n            <i className=\"fas fa-cog\"></i>\r\n            <span>Admin</span>\r\n          </Link>\r\n          {user ? (\r\n            <div className=\"user-menu\">\r\n              <button\r\n                className=\"user-button\"\r\n                onClick={() => setShowUserMenu(!showUserMenu)}\r\n              >\r\n                <i className=\"fas fa-user\"></i>\r\n                <span>{user.name}</span>\r\n                <i className=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              {showUserMenu && (\r\n                <div className=\"user-dropdown\">\r\n                  <Link to=\"/profile\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-user-circle\"></i>\r\n                    Profile\r\n                  </Link>\r\n                  <Link to=\"/wishlist\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-heart\"></i>\r\n                    Wishlist\r\n                  </Link>\r\n                  <Link to=\"/orders\" onClick={() => setShowUserMenu(false)}>\r\n                    <i className=\"fas fa-box\"></i>\r\n                    Orders\r\n                  </Link>\r\n                  <button onClick={() => { logout(); setShowUserMenu(false); }}>\r\n                    <i className=\"fas fa-sign-out-alt\"></i>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <Link to=\"/login\" className=\"nav-link\">\r\n              <i className=\"fas fa-sign-in-alt\"></i>\r\n              <span>Login</span>\r\n            </Link>\r\n          )}\r\n        </nav>\r\n\r\n        {/* Mobile Menu Button */}\r\n        <button\r\n          className=\"mobile-menu-btn\"\r\n          onClick={() => setShowMobileMenu(!showMobileMenu)}\r\n        >\r\n          <i className={`fas ${showMobileMenu ? 'fa-times' : 'fa-bars'}`}></i>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Mobile Navigation */}\r\n      {showMobileMenu && (\r\n        <div className=\"mobile-menu\">\r\n          <div className=\"mobile-menu-content\">\r\n            <Link\r\n              to=\"/search\"\r\n              className=\"mobile-nav-link\"\r\n              onClick={() => setShowMobileMenu(false)}\r\n            >\r\n              <i className=\"fas fa-search\"></i>\r\n              Search\r\n            </Link>\r\n            <Link\r\n              to=\"/cart\"\r\n              className=\"mobile-nav-link\"\r\n              onClick={() => setShowMobileMenu(false)}\r\n            >\r\n              <i className=\"fas fa-shopping-cart\"></i>\r\n              Cart\r\n              {cartItemCount > 0 && (\r\n                <span className=\"cart-badge\">{cartItemCount}</span>\r\n              )}\r\n            </Link>\r\n            <Link\r\n              to=\"/admin\"\r\n              className=\"mobile-nav-link\"\r\n              onClick={() => setShowMobileMenu(false)}\r\n            >\r\n              <i className=\"fas fa-cog\"></i>\r\n              Admin\r\n            </Link>\r\n            {user ? (\r\n              <>\r\n                <Link\r\n                  to=\"/profile\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-user-circle\"></i>\r\n                  Profile\r\n                </Link>\r\n                <Link\r\n                  to=\"/wishlist\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-heart\"></i>\r\n                  Wishlist\r\n                </Link>\r\n                <Link\r\n                  to=\"/orders\"\r\n                  className=\"mobile-nav-link\"\r\n                  onClick={() => setShowMobileMenu(false)}\r\n                >\r\n                  <i className=\"fas fa-box\"></i>\r\n                  Orders\r\n                </Link>\r\n                <button\r\n                  className=\"mobile-nav-link logout-btn\"\r\n                  onClick={() => { logout(); setShowMobileMenu(false); }}\r\n                >\r\n                  <i className=\"fas fa-sign-out-alt\"></i>\r\n                  Logout\r\n                </button>\r\n              </>\r\n            ) : (\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"mobile-nav-link\"\r\n                onClick={() => setShowMobileMenu(false)}\r\n              >\r\n                <i className=\"fas fa-sign-in-alt\"></i>\r\n                Login\r\n              </Link>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <style jsx>{`\r\n        header {\r\n          position: relative;\r\n        }\r\n\r\n        .header-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          max-width: 1400px;\r\n          margin: 0 auto;\r\n          padding: 0 1rem;\r\n        }\r\n\r\n        .desktop-nav {\r\n          display: flex;\r\n          gap: 2rem;\r\n          align-items: center;\r\n        }\r\n\r\n        .nav-link {\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .nav-link span {\r\n          display: block;\r\n        }\r\n\r\n        .cart-link {\r\n          position: relative;\r\n        }\r\n\r\n        .cart-badge {\r\n          position: absolute;\r\n          top: -8px;\r\n          right: -8px;\r\n          background: #ef4444;\r\n          color: white;\r\n          border-radius: 50%;\r\n          width: 20px;\r\n          height: 20px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 0.75rem;\r\n          font-weight: 600;\r\n          z-index: 10;\r\n        }\r\n\r\n        .user-menu {\r\n          position: relative;\r\n        }\r\n\r\n        .user-button {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n          color: white;\r\n          padding: 0.5rem 1rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.5rem;\r\n          font-weight: 500;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .user-button:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n        }\r\n\r\n        .user-dropdown {\r\n          position: absolute;\r\n          top: 100%;\r\n          right: 0;\r\n          background: white;\r\n          border-radius: 0.75rem;\r\n          box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);\r\n          padding: 0.75rem;\r\n          min-width: 220px;\r\n          z-index: 1000;\r\n          margin-top: 0.5rem;\r\n          border: 1px solid var(--border-color);\r\n        }\r\n\r\n        .user-dropdown a,\r\n        .user-dropdown button {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.75rem;\r\n          padding: 0.75rem 1rem;\r\n          color: #374151;\r\n          text-decoration: none;\r\n          border-radius: 0.5rem;\r\n          transition: all 0.2s;\r\n          width: 100%;\r\n          border: none;\r\n          background: none;\r\n          cursor: pointer;\r\n          font-size: 0.875rem;\r\n          font-weight: 500;\r\n        }\r\n\r\n        .user-dropdown a:hover,\r\n        .user-dropdown button:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .admin-link {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border-radius: 0.375rem;\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n        }\r\n\r\n        .admin-link:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          border-color: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .mobile-menu-btn {\r\n          display: none;\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border: 1px solid rgba(255, 255, 255, 0.2);\r\n          color: white;\r\n          padding: 0.75rem;\r\n          border-radius: 0.5rem;\r\n          cursor: pointer;\r\n          font-size: 1.25rem;\r\n          transition: all 0.2s;\r\n        }\r\n\r\n        .mobile-menu-btn:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n        }\r\n\r\n        .mobile-menu {\r\n          position: absolute;\r\n          top: 100%;\r\n          left: 0;\r\n          right: 0;\r\n          background: white;\r\n          border-top: 1px solid var(--border-color);\r\n          box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);\r\n          z-index: 1000;\r\n        }\r\n\r\n        .mobile-menu-content {\r\n          max-width: 1400px;\r\n          margin: 0 auto;\r\n          padding: 1rem;\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 0.5rem;\r\n        }\r\n\r\n        .mobile-nav-link {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 1rem;\r\n          padding: 1rem;\r\n          color: var(--text-primary);\r\n          text-decoration: none;\r\n          border-radius: 0.5rem;\r\n          transition: all 0.2s;\r\n          font-weight: 500;\r\n          position: relative;\r\n        }\r\n\r\n        .mobile-nav-link:hover {\r\n          background: var(--bg-secondary);\r\n          color: var(--primary-color);\r\n        }\r\n\r\n        .mobile-nav-link.logout-btn {\r\n          background: none;\r\n          border: none;\r\n          width: 100%;\r\n          text-align: left;\r\n          cursor: pointer;\r\n          font-size: 1rem;\r\n        }\r\n\r\n        .mobile-nav-link i {\r\n          width: 20px;\r\n          text-align: center;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n          .desktop-nav {\r\n            display: none;\r\n          }\r\n\r\n          .mobile-menu-btn {\r\n            display: block;\r\n          }\r\n\r\n          .nav-link span {\r\n            display: none;\r\n          }\r\n\r\n          .header-content {\r\n            padding: 0 1rem;\r\n          }\r\n\r\n          header h1 {\r\n            font-size: 1.5rem;\r\n          }\r\n        }\r\n\r\n        @media (max-width: 480px) {\r\n          .nav-link span {\r\n            display: none;\r\n          }\r\n\r\n          .user-button span {\r\n            display: none;\r\n          }\r\n\r\n          .desktop-nav {\r\n            gap: 1rem;\r\n          }\r\n        }\r\n      `}</style>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,QAAQ,KAAQ,OAAO,CACnD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,WAAW,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,QAAS,CAAAC,MAAMA,CAAA,CAAG,CAChB,KAAM,CAAEC,IAAK,CAAC,CAAGZ,UAAU,CAACG,WAAW,CAAC,CACxC,KAAM,CAAEU,IAAI,CAAEC,MAAO,CAAC,CAAGd,UAAU,CAACI,WAAW,CAAC,CAChD,KAAM,CAACW,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAAkB,aAAa,CAAGP,IAAI,CAACQ,MAAM,CAAC,CAACC,KAAK,CAAEC,IAAI,GAAKD,KAAK,CAAGC,IAAI,CAACC,QAAQ,CAAE,CAAC,CAAC,CAE5E;AACAxB,KAAK,CAACyB,SAAS,CAAC,IAAM,CACpB,KAAM,CAAAC,kBAAkB,CAAIC,KAAK,EAAK,CACpC,GAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,EAAI,CAACF,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,CAAE,CAChFZ,eAAe,CAAC,KAAK,CAAC,CACtBE,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CACF,CAAC,CAEDW,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAEL,kBAAkB,CAAC,CACtD,MAAO,IAAMI,QAAQ,CAACE,mBAAmB,CAAC,OAAO,CAAEN,kBAAkB,CAAC,CACxE,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEjB,KAAA,WAAAwB,QAAA,eACExB,KAAA,QAAKyB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1B,IAAA,OAAA0B,QAAA,cACExB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,GAAG,CAAAF,QAAA,eACV1B,IAAA,MAAG2B,SAAS,CAAC,aAAa,CAAI,CAAC,cAEjC,EAAM,CAAC,CACL,CAAC,cAGLzB,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BxB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,SAAS,CAACD,SAAS,CAAC,UAAU,CAAAD,QAAA,eACrC1B,IAAA,MAAG2B,SAAS,CAAC,eAAe,CAAI,CAAC,cACjC3B,IAAA,SAAA0B,QAAA,CAAM,QAAM,CAAM,CAAC,EACf,CAAC,cACPxB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,OAAO,CAACD,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eAC7C1B,IAAA,MAAG2B,SAAS,CAAC,sBAAsB,CAAI,CAAC,cACxC3B,IAAA,SAAA0B,QAAA,CAAM,MAAI,CAAM,CAAC,CAChBb,aAAa,CAAG,CAAC,eAChBb,IAAA,SAAM2B,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEb,aAAa,CAAO,CACnD,EACG,CAAC,cACPX,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,QAAQ,CAACD,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAC/C1B,IAAA,MAAG2B,SAAS,CAAC,YAAY,CAAI,CAAC,cAC9B3B,IAAA,SAAA0B,QAAA,CAAM,OAAK,CAAM,CAAC,EACd,CAAC,CACNnB,IAAI,cACHL,KAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBxB,KAAA,WACEyB,SAAS,CAAC,aAAa,CACvBE,OAAO,CAAEA,CAAA,GAAMnB,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAiB,QAAA,eAE9C1B,IAAA,MAAG2B,SAAS,CAAC,aAAa,CAAI,CAAC,cAC/B3B,IAAA,SAAA0B,QAAA,CAAOnB,IAAI,CAACuB,IAAI,CAAO,CAAC,cACxB9B,IAAA,MAAG2B,SAAS,CAAC,qBAAqB,CAAI,CAAC,EACjC,CAAC,CACRlB,YAAY,eACXP,KAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BxB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,UAAU,CAACC,OAAO,CAAEA,CAAA,GAAMnB,eAAe,CAAC,KAAK,CAAE,CAAAgB,QAAA,eACxD1B,IAAA,MAAG2B,SAAS,CAAC,oBAAoB,CAAI,CAAC,UAExC,EAAM,CAAC,cACPzB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMnB,eAAe,CAAC,KAAK,CAAE,CAAAgB,QAAA,eACzD1B,IAAA,MAAG2B,SAAS,CAAC,cAAc,CAAI,CAAC,WAElC,EAAM,CAAC,cACPzB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,SAAS,CAACC,OAAO,CAAEA,CAAA,GAAMnB,eAAe,CAAC,KAAK,CAAE,CAAAgB,QAAA,eACvD1B,IAAA,MAAG2B,SAAS,CAAC,YAAY,CAAI,CAAC,SAEhC,EAAM,CAAC,cACPzB,KAAA,WAAQ2B,OAAO,CAAEA,CAAA,GAAM,CAAErB,MAAM,CAAC,CAAC,CAAEE,eAAe,CAAC,KAAK,CAAC,CAAE,CAAE,CAAAgB,QAAA,eAC3D1B,IAAA,MAAG2B,SAAS,CAAC,qBAAqB,CAAI,CAAC,SAEzC,EAAQ,CAAC,EACN,CACN,EACE,CAAC,cAENzB,KAAA,CAACN,IAAI,EAACgC,EAAE,CAAC,QAAQ,CAACD,SAAS,CAAC,UAAU,CAAAD,QAAA,eACpC1B,IAAA,MAAG2B,SAAS,CAAC,oBAAoB,CAAI,CAAC,cACtC3B,IAAA,SAAA0B,QAAA,CAAM,OAAK,CAAM,CAAC,EACd,CACP,EACE,CAAC,cAGN1B,IAAA,WACE2B,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAAAe,QAAA,cAElD1B,IAAA,MAAG2B,SAAS,QAAAI,MAAA,CAASpB,cAAc,CAAG,UAAU,CAAG,SAAS,CAAG,CAAI,CAAC,CAC9D,CAAC,EACN,CAAC,CAGLA,cAAc,eACbX,IAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,KAAA,QAAKyB,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCxB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,SAAS,CACZD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,eAAe,CAAI,CAAC,SAEnC,EAAM,CAAC,cACPzB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,OAAO,CACVD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,sBAAsB,CAAI,CAAC,OAExC,CAACd,aAAa,CAAG,CAAC,eAChBb,IAAA,SAAM2B,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEb,aAAa,CAAO,CACnD,EACG,CAAC,cACPX,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,QAAQ,CACXD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,YAAY,CAAI,CAAC,QAEhC,EAAM,CAAC,CACNpB,IAAI,cACHL,KAAA,CAAAE,SAAA,EAAAsB,QAAA,eACExB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,UAAU,CACbD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,oBAAoB,CAAI,CAAC,UAExC,EAAM,CAAC,cACPzB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,WAAW,CACdD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,cAAc,CAAI,CAAC,WAElC,EAAM,CAAC,cACPzB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,SAAS,CACZD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,YAAY,CAAI,CAAC,SAEhC,EAAM,CAAC,cACPzB,KAAA,WACEyB,SAAS,CAAC,4BAA4B,CACtCE,OAAO,CAAEA,CAAA,GAAM,CAAErB,MAAM,CAAC,CAAC,CAAEI,iBAAiB,CAAC,KAAK,CAAC,CAAE,CAAE,CAAAc,QAAA,eAEvD1B,IAAA,MAAG2B,SAAS,CAAC,qBAAqB,CAAI,CAAC,SAEzC,EAAQ,CAAC,EACT,CAAC,cAEHzB,KAAA,CAACN,IAAI,EACHgC,EAAE,CAAC,QAAQ,CACXD,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAc,QAAA,eAExC1B,IAAA,MAAG2B,SAAS,CAAC,oBAAoB,CAAI,CAAC,QAExC,EAAM,CACP,EACE,CAAC,CACH,CACN,cAED3B,IAAA,UAAOD,GAAG,MAAA2B,QAAA,o4KAkOD,CAAC,EACJ,CAAC,CAEb,CAEA,cAAe,CAAArB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}