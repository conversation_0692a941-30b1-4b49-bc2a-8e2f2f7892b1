{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useState,useEffect,useContext}from'react';import{AuthContext}from'./AuthContext';import{jsx as _jsx}from\"react/jsx-runtime\";export const UserStatsContext=/*#__PURE__*/createContext();export function UserStatsProvider(_ref){let{children}=_ref;const{user}=useContext(AuthContext);const[userStats,setUserStats]=useState({totalSpent:0,totalOrders:0,loyaltyPoints:0,ecoImpact:{co2Saved:12.5,ecoProducts:8,treesPlanted:3}});const[orders,setOrders]=useState([{id:'ORD-001',date:'2024-01-15',status:'Delivered',total:89.97,items:[{name:'Eco-Friendly Water Bottle',quantity:2,price:29.99,imageUrl:'https://images.unsplash.com/photo-*************-7111542de6e8?w=100'},{name:'Organic Cotton T-Shirt',quantity:1,price:24.99,imageUrl:'https://images.unsplash.com/photo-*************-6864f9cf17ab?w=100'}],shippingAddress:'123 Main St, City, State 12345',trackingNumber:'TRK123456789'},{id:'ORD-002',date:'2024-01-08',status:'Shipped',total:49.99,items:[{name:'Solar Power Bank',quantity:1,price:49.99,imageUrl:'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=100'}],shippingAddress:'123 Main St, City, State 12345',trackingNumber:'TRK987654321'},{id:'ORD-003',date:'2024-01-01',status:'Processing',total:32.98,items:[{name:'Bamboo Phone Case',quantity:1,price:19.99,imageUrl:'https://images.unsplash.com/photo-**********-08538906a9f8?w=100'},{name:'Recycled Notebook',quantity:1,price:12.99,imageUrl:'https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=100'}],shippingAddress:'123 Main St, City, State 12345',trackingNumber:null}]);// Calculate stats from orders\nuseEffect(()=>{const totalSpent=orders.reduce((sum,order)=>sum+order.total,0);const totalOrders=orders.length;const loyaltyPoints=Math.floor(totalSpent);// 1 point per dollar\nsetUserStats(prev=>_objectSpread(_objectSpread({},prev),{},{totalSpent,totalOrders,loyaltyPoints}));},[orders]);const addOrder=newOrder=>{const order=_objectSpread(_objectSpread({id:\"ORD-\".concat(Date.now()),date:new Date().toISOString().split('T')[0],status:'Processing'},newOrder),{},{trackingNumber:null});setOrders(prev=>[order,...prev]);// Update eco impact based on eco-friendly products\nconst ecoItems=newOrder.items.filter(item=>item.isEcoFriendly);if(ecoItems.length>0){setUserStats(prev=>_objectSpread(_objectSpread({},prev),{},{ecoImpact:_objectSpread(_objectSpread({},prev.ecoImpact),{},{co2Saved:prev.ecoImpact.co2Saved+ecoItems.length*2.5,ecoProducts:prev.ecoImpact.ecoProducts+ecoItems.length,treesPlanted:prev.ecoImpact.treesPlanted+Math.floor(ecoItems.length/3)})}));}};const updateOrderStatus=function(orderId,newStatus){let trackingNumber=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;setOrders(prev=>prev.map(order=>order.id===orderId?_objectSpread(_objectSpread({},order),{},{status:newStatus,trackingNumber}):order));};const redeemReward=pointsCost=>{setUserStats(prev=>_objectSpread(_objectSpread({},prev),{},{loyaltyPoints:Math.max(0,prev.loyaltyPoints-pointsCost)}));};return/*#__PURE__*/_jsx(UserStatsContext.Provider,{value:{userStats,orders,addOrder,updateOrderStatus,redeemReward,setUserStats},children:children});}", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useContext", "AuthContext", "jsx", "_jsx", "UserStatsContext", "UserStatsProvider", "_ref", "children", "user", "userStats", "setUserStats", "totalSpent", "totalOrders", "loyaltyPoints", "ecoImpact", "co2Saved", "ecoProducts", "treesPlanted", "orders", "setOrders", "id", "date", "status", "total", "items", "name", "quantity", "price", "imageUrl", "shippingAddress", "trackingNumber", "reduce", "sum", "order", "length", "Math", "floor", "prev", "_objectSpread", "addOrder", "newOrder", "concat", "Date", "now", "toISOString", "split", "ecoItems", "filter", "item", "isEcoFriendly", "updateOrderStatus", "orderId", "newStatus", "arguments", "undefined", "map", "redeemReward", "pointsCost", "max", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/context/UserStatsContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect, useContext } from 'react';\nimport { AuthContext } from './AuthContext';\n\nexport const UserStatsContext = createContext();\n\nexport function UserStatsProvider({ children }) {\n  const { user } = useContext(AuthContext);\n  const [userStats, setUserStats] = useState({\n    totalSpent: 0,\n    totalOrders: 0,\n    loyaltyPoints: 0,\n    ecoImpact: {\n      co2Saved: 12.5,\n      ecoProducts: 8,\n      treesPlanted: 3\n    }\n  });\n\n  const [orders, setOrders] = useState([\n    {\n      id: 'ORD-001',\n      date: '2024-01-15',\n      status: 'Delivered',\n      total: 89.97,\n      items: [\n        { \n          name: 'Eco-Friendly Water Bottle', \n          quantity: 2, \n          price: 29.99,\n          imageUrl: 'https://images.unsplash.com/photo-*************-7111542de6e8?w=100'\n        },\n        { \n          name: 'Organic Cotton T-Shirt', \n          quantity: 1, \n          price: 24.99,\n          imageUrl: 'https://images.unsplash.com/photo-*************-6864f9cf17ab?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: 'TRK123456789'\n    },\n    {\n      id: 'ORD-002',\n      date: '2024-01-08',\n      status: 'Shipped',\n      total: 49.99,\n      items: [\n        { \n          name: 'Solar Power Bank', \n          quantity: 1, \n          price: 49.99,\n          imageUrl: 'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: 'TRK987654321'\n    },\n    {\n      id: 'ORD-003',\n      date: '2024-01-01',\n      status: 'Processing',\n      total: 32.98,\n      items: [\n        { \n          name: 'Bamboo Phone Case', \n          quantity: 1, \n          price: 19.99,\n          imageUrl: 'https://images.unsplash.com/photo-**********-08538906a9f8?w=100'\n        },\n        { \n          name: 'Recycled Notebook', \n          quantity: 1, \n          price: 12.99,\n          imageUrl: 'https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=100'\n        }\n      ],\n      shippingAddress: '123 Main St, City, State 12345',\n      trackingNumber: null\n    }\n  ]);\n\n  // Calculate stats from orders\n  useEffect(() => {\n    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);\n    const totalOrders = orders.length;\n    const loyaltyPoints = Math.floor(totalSpent); // 1 point per dollar\n\n    setUserStats(prev => ({\n      ...prev,\n      totalSpent,\n      totalOrders,\n      loyaltyPoints\n    }));\n  }, [orders]);\n\n  const addOrder = (newOrder) => {\n    const order = {\n      id: `ORD-${Date.now()}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'Processing',\n      ...newOrder,\n      trackingNumber: null\n    };\n    \n    setOrders(prev => [order, ...prev]);\n    \n    // Update eco impact based on eco-friendly products\n    const ecoItems = newOrder.items.filter(item => item.isEcoFriendly);\n    if (ecoItems.length > 0) {\n      setUserStats(prev => ({\n        ...prev,\n        ecoImpact: {\n          ...prev.ecoImpact,\n          co2Saved: prev.ecoImpact.co2Saved + (ecoItems.length * 2.5),\n          ecoProducts: prev.ecoImpact.ecoProducts + ecoItems.length,\n          treesPlanted: prev.ecoImpact.treesPlanted + Math.floor(ecoItems.length / 3)\n        }\n      }));\n    }\n  };\n\n  const updateOrderStatus = (orderId, newStatus, trackingNumber = null) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: newStatus, trackingNumber }\n        : order\n    ));\n  };\n\n  const redeemReward = (pointsCost) => {\n    setUserStats(prev => ({\n      ...prev,\n      loyaltyPoints: Math.max(0, prev.loyaltyPoints - pointsCost)\n    }));\n  };\n\n  return (\n    <UserStatsContext.Provider value={{ \n      userStats, \n      orders, \n      addOrder, \n      updateOrderStatus,\n      redeemReward,\n      setUserStats\n    }}>\n      {children}\n    </UserStatsContext.Provider>\n  );\n}\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CAC7E,OAASC,WAAW,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE5C,MAAO,MAAM,CAAAC,gBAAgB,cAAGP,aAAa,CAAC,CAAC,CAE/C,MAAO,SAAS,CAAAQ,iBAAiBA,CAAAC,IAAA,CAAe,IAAd,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC5C,KAAM,CAAEE,IAAK,CAAC,CAAGR,UAAU,CAACC,WAAW,CAAC,CACxC,KAAM,CAACQ,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,CACzCa,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CAAC,CACdC,aAAa,CAAE,CAAC,CAChBC,SAAS,CAAE,CACTC,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,CAAC,CACdC,YAAY,CAAE,CAChB,CACF,CAAC,CAAC,CAEF,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGrB,QAAQ,CAAC,CACnC,CACEsB,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,WAAW,CACnBC,KAAK,CAAE,KAAK,CACZC,KAAK,CAAE,CACL,CACEC,IAAI,CAAE,2BAA2B,CACjCC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,oEACZ,CAAC,CACD,CACEH,IAAI,CAAE,wBAAwB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,oEACZ,CAAC,CACF,CACDC,eAAe,CAAE,gCAAgC,CACjDC,cAAc,CAAE,cAClB,CAAC,CACD,CACEV,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,KAAK,CACZC,KAAK,CAAE,CACL,CACEC,IAAI,CAAE,kBAAkB,CACxBC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,oEACZ,CAAC,CACF,CACDC,eAAe,CAAE,gCAAgC,CACjDC,cAAc,CAAE,cAClB,CAAC,CACD,CACEV,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,YAAY,CACpBC,KAAK,CAAE,KAAK,CACZC,KAAK,CAAE,CACL,CACEC,IAAI,CAAE,mBAAmB,CACzBC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,iEACZ,CAAC,CACD,CACEH,IAAI,CAAE,mBAAmB,CACzBC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,iEACZ,CAAC,CACF,CACDC,eAAe,CAAE,gCAAgC,CACjDC,cAAc,CAAE,IAClB,CAAC,CACF,CAAC,CAEF;AACA/B,SAAS,CAAC,IAAM,CACd,KAAM,CAAAY,UAAU,CAAGO,MAAM,CAACa,MAAM,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAKD,GAAG,CAAGC,KAAK,CAACV,KAAK,CAAE,CAAC,CAAC,CACtE,KAAM,CAAAX,WAAW,CAAGM,MAAM,CAACgB,MAAM,CACjC,KAAM,CAAArB,aAAa,CAAGsB,IAAI,CAACC,KAAK,CAACzB,UAAU,CAAC,CAAE;AAE9CD,YAAY,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACP1B,UAAU,CACVC,WAAW,CACXC,aAAa,EACb,CAAC,CACL,CAAC,CAAE,CAACK,MAAM,CAAC,CAAC,CAEZ,KAAM,CAAAqB,QAAQ,CAAIC,QAAQ,EAAK,CAC7B,KAAM,CAAAP,KAAK,CAAAK,aAAA,CAAAA,aAAA,EACTlB,EAAE,QAAAqB,MAAA,CAASC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACvBtB,IAAI,CAAE,GAAI,CAAAqB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5CvB,MAAM,CAAE,YAAY,EACjBkB,QAAQ,MACXV,cAAc,CAAE,IAAI,EACrB,CAEDX,SAAS,CAACkB,IAAI,EAAI,CAACJ,KAAK,CAAE,GAAGI,IAAI,CAAC,CAAC,CAEnC;AACA,KAAM,CAAAS,QAAQ,CAAGN,QAAQ,CAAChB,KAAK,CAACuB,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,aAAa,CAAC,CAClE,GAAIH,QAAQ,CAACZ,MAAM,CAAG,CAAC,CAAE,CACvBxB,YAAY,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPvB,SAAS,CAAAwB,aAAA,CAAAA,aAAA,IACJD,IAAI,CAACvB,SAAS,MACjBC,QAAQ,CAAEsB,IAAI,CAACvB,SAAS,CAACC,QAAQ,CAAI+B,QAAQ,CAACZ,MAAM,CAAG,GAAI,CAC3DlB,WAAW,CAAEqB,IAAI,CAACvB,SAAS,CAACE,WAAW,CAAG8B,QAAQ,CAACZ,MAAM,CACzDjB,YAAY,CAAEoB,IAAI,CAACvB,SAAS,CAACG,YAAY,CAAGkB,IAAI,CAACC,KAAK,CAACU,QAAQ,CAACZ,MAAM,CAAG,CAAC,CAAC,EAC5E,EACD,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAG,QAAAA,CAACC,OAAO,CAAEC,SAAS,CAA4B,IAA1B,CAAAtB,cAAc,CAAAuB,SAAA,CAAAnB,MAAA,IAAAmB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,IAAI,CAClElC,SAAS,CAACkB,IAAI,EAAIA,IAAI,CAACkB,GAAG,CAACtB,KAAK,EAC9BA,KAAK,CAACb,EAAE,GAAK+B,OAAO,CAAAb,aAAA,CAAAA,aAAA,IACXL,KAAK,MAAEX,MAAM,CAAE8B,SAAS,CAAEtB,cAAc,GAC7CG,KACN,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAuB,YAAY,CAAIC,UAAU,EAAK,CACnC/C,YAAY,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPxB,aAAa,CAAEsB,IAAI,CAACuB,GAAG,CAAC,CAAC,CAAErB,IAAI,CAACxB,aAAa,CAAG4C,UAAU,CAAC,EAC3D,CAAC,CACL,CAAC,CAED,mBACEtD,IAAA,CAACC,gBAAgB,CAACuD,QAAQ,EAACC,KAAK,CAAE,CAChCnD,SAAS,CACTS,MAAM,CACNqB,QAAQ,CACRW,iBAAiB,CACjBM,YAAY,CACZ9C,YACF,CAAE,CAAAH,QAAA,CACCA,QAAQ,CACgB,CAAC,CAEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}