[{"C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx": "3", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx": "4", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx": "5", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx": "7", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx": "9", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx": "11", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx": "12", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx": "13", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx": "14", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx": "15", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx": "16", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx": "17", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx": "18", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx": "19", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx": "21", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx": "22", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx": "23", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx": "24", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx": "25", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\currency.js": "26", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\SignOutPage.jsx": "27", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\orderUtils.js": "28"}, {"size": 629, "mtime": 1751610824075, "results": "29", "hashOfConfig": "30"}, {"size": 1541, "mtime": 1751612761706, "results": "31", "hashOfConfig": "30"}, {"size": 785, "mtime": 1750401626266, "results": "32", "hashOfConfig": "30"}, {"size": 1011, "mtime": 1751609521288, "results": "33", "hashOfConfig": "30"}, {"size": 8962, "mtime": 1751613355263, "results": "34", "hashOfConfig": "30"}, {"size": 24829, "mtime": 1751612566703, "results": "35", "hashOfConfig": "30"}, {"size": 14493, "mtime": 1751612410320, "results": "36", "hashOfConfig": "30"}, {"size": 37803, "mtime": 1751612660644, "results": "37", "hashOfConfig": "30"}, {"size": 333, "mtime": 1751604645162, "results": "38", "hashOfConfig": "30"}, {"size": 11819, "mtime": 1751612792018, "results": "39", "hashOfConfig": "30"}, {"size": 511, "mtime": 1750401414236, "results": "40", "hashOfConfig": "30"}, {"size": 5685, "mtime": 1751612368423, "results": "41", "hashOfConfig": "30"}, {"size": 1134, "mtime": 1751607106712, "results": "42", "hashOfConfig": "30"}, {"size": 8471, "mtime": 1751607150163, "results": "43", "hashOfConfig": "30"}, {"size": 3186, "mtime": 1751612817893, "results": "44", "hashOfConfig": "30"}, {"size": 10017, "mtime": 1751607306161, "results": "45", "hashOfConfig": "30"}, {"size": 9098, "mtime": 1751607232896, "results": "46", "hashOfConfig": "30"}, {"size": 12542, "mtime": 1751607408683, "results": "47", "hashOfConfig": "30"}, {"size": 17810, "mtime": 1751607757632, "results": "48", "hashOfConfig": "30"}, {"size": 3739, "mtime": 1751607658570, "results": "49", "hashOfConfig": "30"}, {"size": 3209, "mtime": 1751607636355, "results": "50", "hashOfConfig": "30"}, {"size": 16377, "mtime": 1751609832416, "results": "51", "hashOfConfig": "30"}, {"size": 3930, "mtime": 1751612624098, "results": "52", "hashOfConfig": "30"}, {"size": 8503, "mtime": 1751610533161, "results": "53", "hashOfConfig": "30"}, {"size": 15824, "mtime": 1751613186800, "results": "54", "hashOfConfig": "30"}, {"size": 1302, "mtime": 1751612316023, "results": "55", "hashOfConfig": "30"}, {"size": 9824, "mtime": 1751612719579, "results": "56", "hashOfConfig": "30"}, {"size": 11410, "mtime": 1751613158691, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c272uv", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx", ["142", "143"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx", ["144", "145"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx", ["146"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx", ["147", "148"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx", ["149"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\SignOutPage.jsx", ["150"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\orderUtils.js", [], [], {"ruleId": "151", "severity": 1, "message": "152", "line": 17, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 17, "endColumn": 26}, {"ruleId": "151", "severity": 1, "message": "155", "line": 17, "column": 28, "nodeType": "153", "messageId": "154", "endLine": 17, "endColumn": 47}, {"ruleId": "151", "severity": 1, "message": "156", "line": 13, "column": 18, "nodeType": "153", "messageId": "154", "endLine": 13, "endColumn": 22}, {"ruleId": "157", "severity": 1, "message": "158", "line": 89, "column": 6, "nodeType": "159", "endLine": 89, "endColumn": 13, "suggestions": "160"}, {"ruleId": "151", "severity": 1, "message": "161", "line": 8, "column": 17, "nodeType": "153", "messageId": "154", "endLine": 8, "endColumn": 23}, {"ruleId": "151", "severity": 1, "message": "162", "line": 4, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 4, "endColumn": 23}, {"ruleId": "151", "severity": 1, "message": "163", "line": 8, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 8, "endColumn": 20}, {"ruleId": "151", "severity": 1, "message": "156", "line": 7, "column": 11, "nodeType": "153", "messageId": "154", "endLine": 7, "endColumn": 15}, {"ruleId": "151", "severity": 1, "message": "156", "line": 6, "column": 19, "nodeType": "153", "messageId": "154", "endLine": 6, "endColumn": 23}, "no-unused-vars", "'showCheckoutForm' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowCheckoutForm' is assigned a value but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["164"], "'logout' is assigned a value but never used.", "'selectedImage' is assigned a value but never used.", "'isResizing' is assigned a value but never used.", {"desc": "165", "fix": "166"}, "Update the dependencies array to be: [fetchProfile, token]", {"range": "167", "text": "168"}, [2860, 2867], "[fetchProfile, token]"]