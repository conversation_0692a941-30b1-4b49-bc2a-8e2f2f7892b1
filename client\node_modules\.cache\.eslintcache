[{"C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx": "3", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx": "4", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx": "5", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx": "7", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx": "9", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx": "11", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx": "12", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx": "13", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx": "14", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx": "15", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx": "16", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx": "17", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx": "18", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx": "19", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx": "21", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx": "22", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx": "23", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx": "24", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx": "25", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\currency.js": "26", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\SignOutPage.jsx": "27"}, {"size": 629, "mtime": 1751610824075, "results": "28", "hashOfConfig": "29"}, {"size": 1541, "mtime": 1751612761706, "results": "30", "hashOfConfig": "29"}, {"size": 785, "mtime": 1750401626266, "results": "31", "hashOfConfig": "29"}, {"size": 1011, "mtime": 1751609521288, "results": "32", "hashOfConfig": "29"}, {"size": 5994, "mtime": 1751607181188, "results": "33", "hashOfConfig": "29"}, {"size": 24829, "mtime": 1751612566703, "results": "34", "hashOfConfig": "29"}, {"size": 14493, "mtime": 1751612410320, "results": "35", "hashOfConfig": "29"}, {"size": 37803, "mtime": 1751612660644, "results": "36", "hashOfConfig": "29"}, {"size": 333, "mtime": 1751604645162, "results": "37", "hashOfConfig": "29"}, {"size": 11819, "mtime": 1751612792018, "results": "38", "hashOfConfig": "29"}, {"size": 511, "mtime": 1750401414236, "results": "39", "hashOfConfig": "29"}, {"size": 5685, "mtime": 1751612368423, "results": "40", "hashOfConfig": "29"}, {"size": 1134, "mtime": 1751607106712, "results": "41", "hashOfConfig": "29"}, {"size": 8471, "mtime": 1751607150163, "results": "42", "hashOfConfig": "29"}, {"size": 3186, "mtime": 1751612817893, "results": "43", "hashOfConfig": "29"}, {"size": 10017, "mtime": 1751607306161, "results": "44", "hashOfConfig": "29"}, {"size": 9098, "mtime": 1751607232896, "results": "45", "hashOfConfig": "29"}, {"size": 12542, "mtime": 1751607408683, "results": "46", "hashOfConfig": "29"}, {"size": 17810, "mtime": 1751607757632, "results": "47", "hashOfConfig": "29"}, {"size": 3739, "mtime": 1751607658570, "results": "48", "hashOfConfig": "29"}, {"size": 3209, "mtime": 1751607636355, "results": "49", "hashOfConfig": "29"}, {"size": 16377, "mtime": 1751609832416, "results": "50", "hashOfConfig": "29"}, {"size": 3930, "mtime": 1751612624098, "results": "51", "hashOfConfig": "29"}, {"size": 8503, "mtime": 1751610533161, "results": "52", "hashOfConfig": "29"}, {"size": 15627, "mtime": 1751610489537, "results": "53", "hashOfConfig": "29"}, {"size": 1302, "mtime": 1751612316023, "results": "54", "hashOfConfig": "29"}, {"size": 9824, "mtime": 1751612719579, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c272uv", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx", ["137", "138"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx", ["139", "140"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx", ["141"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx", ["142", "143"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx", ["144"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\SignOutPage.jsx", ["145"], [], {"ruleId": "146", "severity": 1, "message": "147", "line": 17, "column": 10, "nodeType": "148", "messageId": "149", "endLine": 17, "endColumn": 26}, {"ruleId": "146", "severity": 1, "message": "150", "line": 17, "column": 28, "nodeType": "148", "messageId": "149", "endLine": 17, "endColumn": 47}, {"ruleId": "146", "severity": 1, "message": "151", "line": 13, "column": 18, "nodeType": "148", "messageId": "149", "endLine": 13, "endColumn": 22}, {"ruleId": "152", "severity": 1, "message": "153", "line": 89, "column": 6, "nodeType": "154", "endLine": 89, "endColumn": 13, "suggestions": "155"}, {"ruleId": "146", "severity": 1, "message": "156", "line": 8, "column": 17, "nodeType": "148", "messageId": "149", "endLine": 8, "endColumn": 23}, {"ruleId": "146", "severity": 1, "message": "157", "line": 4, "column": 10, "nodeType": "148", "messageId": "149", "endLine": 4, "endColumn": 23}, {"ruleId": "146", "severity": 1, "message": "158", "line": 8, "column": 10, "nodeType": "148", "messageId": "149", "endLine": 8, "endColumn": 20}, {"ruleId": "146", "severity": 1, "message": "151", "line": 7, "column": 11, "nodeType": "148", "messageId": "149", "endLine": 7, "endColumn": 15}, {"ruleId": "146", "severity": 1, "message": "151", "line": 6, "column": 19, "nodeType": "148", "messageId": "149", "endLine": 6, "endColumn": 23}, "no-unused-vars", "'showCheckoutForm' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowCheckoutForm' is assigned a value but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["159"], "'logout' is assigned a value but never used.", "'selectedImage' is assigned a value but never used.", "'isResizing' is assigned a value but never used.", {"desc": "160", "fix": "161"}, "Update the dependencies array to be: [fetchProfile, token]", {"range": "162", "text": "163"}, [2860, 2867], "[fetchProfile, token]"]