[{"C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx": "3", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx": "4", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx": "5", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx": "7", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx": "9", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx": "11", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx": "12", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx": "13", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx": "14", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx": "15", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx": "16", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx": "17", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx": "18", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx": "19", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx": "21", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx": "22", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx": "23", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx": "24", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx": "25"}, {"size": 629, "mtime": 1751610824075, "results": "26", "hashOfConfig": "27"}, {"size": 1428, "mtime": 1751687627329, "results": "28", "hashOfConfig": "27"}, {"size": 785, "mtime": 1750401626266, "results": "29", "hashOfConfig": "27"}, {"size": 1011, "mtime": 1751609521288, "results": "30", "hashOfConfig": "27"}, {"size": 270, "mtime": 1751955045964, "results": "31", "hashOfConfig": "27"}, {"size": 24702, "mtime": 1751687627496, "results": "32", "hashOfConfig": "27"}, {"size": 14422, "mtime": 1751687627080, "results": "33", "hashOfConfig": "27"}, {"size": 37746, "mtime": 1751687626674, "results": "34", "hashOfConfig": "27"}, {"size": 333, "mtime": 1751604645162, "results": "35", "hashOfConfig": "27"}, {"size": 11821, "mtime": 1751687627084, "results": "36", "hashOfConfig": "27"}, {"size": 511, "mtime": 1750401414236, "results": "37", "hashOfConfig": "27"}, {"size": 5617, "mtime": 1751687627085, "results": "38", "hashOfConfig": "27"}, {"size": 1134, "mtime": 1751607106712, "results": "39", "hashOfConfig": "27"}, {"size": 8471, "mtime": 1751607150163, "results": "40", "hashOfConfig": "27"}, {"size": 3193, "mtime": 1751687627323, "results": "41", "hashOfConfig": "27"}, {"size": 10017, "mtime": 1751607306161, "results": "42", "hashOfConfig": "27"}, {"size": 9098, "mtime": 1751607232896, "results": "43", "hashOfConfig": "27"}, {"size": 12542, "mtime": 1751607408683, "results": "44", "hashOfConfig": "27"}, {"size": 17810, "mtime": 1751607757632, "results": "45", "hashOfConfig": "27"}, {"size": 3739, "mtime": 1751607658570, "results": "46", "hashOfConfig": "27"}, {"size": 3209, "mtime": 1751607636355, "results": "47", "hashOfConfig": "27"}, {"size": 16377, "mtime": 1751609832416, "results": "48", "hashOfConfig": "27"}, {"size": 3954, "mtime": 1751687627504, "results": "49", "hashOfConfig": "27"}, {"size": 8503, "mtime": 1751610533161, "results": "50", "hashOfConfig": "27"}, {"size": 15627, "mtime": 1751687627508, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c272uv", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx", ["127"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx", ["128", "129"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx", ["130", "131"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ImageUpload.jsx", ["132", "133"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\UserStatsContext.jsx", ["134"], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\RewardCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\OrderCard.jsx", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "135", "line": 1, "column": 0, "nodeType": null}, {"ruleId": "136", "severity": 1, "message": "137", "line": 16, "column": 10, "nodeType": "138", "messageId": "139", "endLine": 16, "endColumn": 26}, {"ruleId": "136", "severity": 1, "message": "140", "line": 16, "column": 28, "nodeType": "138", "messageId": "139", "endLine": 16, "endColumn": 47}, {"ruleId": "136", "severity": 1, "message": "141", "line": 12, "column": 18, "nodeType": "138", "messageId": "139", "endLine": 12, "endColumn": 22}, {"ruleId": "142", "severity": 1, "message": "143", "line": 88, "column": 6, "nodeType": "144", "endLine": 88, "endColumn": 13, "suggestions": "145"}, {"ruleId": "136", "severity": 1, "message": "146", "line": 4, "column": 10, "nodeType": "138", "messageId": "139", "endLine": 4, "endColumn": 23}, {"ruleId": "136", "severity": 1, "message": "147", "line": 8, "column": 10, "nodeType": "138", "messageId": "139", "endLine": 8, "endColumn": 20}, {"ruleId": "136", "severity": 1, "message": "141", "line": 7, "column": 11, "nodeType": "138", "messageId": "139", "endLine": 7, "endColumn": 15}, "Parsing error: Unexpected character '�'. (1:0)", "no-unused-vars", "'showCheckoutForm' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowCheckoutForm' is assigned a value but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["148"], "'selectedImage' is assigned a value but never used.", "'isResizing' is assigned a value but never used.", {"desc": "149", "fix": "150"}, "Update the dependencies array to be: [fetchProfile, token]", {"range": "151", "text": "152"}, [2807, 2814], "[fetchProfile, token]"]