[{"C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx": "3", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx": "4", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx": "5", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx": "7", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx": "9", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx": "11", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx": "12", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx": "13", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx": "14", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx": "15", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx": "16", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx": "17", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx": "18", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx": "19", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx": "21"}, {"size": 503, "mtime": 1751607339292, "results": "22", "hashOfConfig": "23"}, {"size": 1428, "mtime": 1751607782860, "results": "24", "hashOfConfig": "23"}, {"size": 785, "mtime": 1750401626266, "results": "25", "hashOfConfig": "23"}, {"size": 796, "mtime": 1750401716848, "results": "26", "hashOfConfig": "23"}, {"size": 5994, "mtime": 1751607181188, "results": "27", "hashOfConfig": "23"}, {"size": 1433, "mtime": 1750402302509, "results": "28", "hashOfConfig": "23"}, {"size": 14422, "mtime": 1751607502277, "results": "29", "hashOfConfig": "23"}, {"size": 704, "mtime": 1750402519022, "results": "30", "hashOfConfig": "23"}, {"size": 333, "mtime": 1751604645162, "results": "31", "hashOfConfig": "23"}, {"size": 11821, "mtime": 1751607828734, "results": "32", "hashOfConfig": "23"}, {"size": 511, "mtime": 1750401414236, "results": "33", "hashOfConfig": "23"}, {"size": 5617, "mtime": 1751607085797, "results": "34", "hashOfConfig": "23"}, {"size": 1134, "mtime": 1751607106712, "results": "35", "hashOfConfig": "23"}, {"size": 8471, "mtime": 1751607150163, "results": "36", "hashOfConfig": "23"}, {"size": 3193, "mtime": 1751607256901, "results": "37", "hashOfConfig": "23"}, {"size": 10017, "mtime": 1751607306161, "results": "38", "hashOfConfig": "23"}, {"size": 9098, "mtime": 1751607232896, "results": "39", "hashOfConfig": "23"}, {"size": 12542, "mtime": 1751607408683, "results": "40", "hashOfConfig": "23"}, {"size": 17810, "mtime": 1751607757632, "results": "41", "hashOfConfig": "23"}, {"size": 3739, "mtime": 1751607658570, "results": "42", "hashOfConfig": "23"}, {"size": 3209, "mtime": 1751607636355, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c272uv", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\CartContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\CartPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProductPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\ProfilePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\context\\WishlistContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\SearchAndFilter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\LoginPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\WishlistPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\AuthModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ProductReviews.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\pages\\AdminPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\ErrorMessage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\exam\\e-commerce website\\ecommerce-app\\client\\src\\components\\LoadingSpinner.jsx", [], []]