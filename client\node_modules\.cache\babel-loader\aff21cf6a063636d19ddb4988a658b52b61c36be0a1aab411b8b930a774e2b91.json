{"ast": null, "code": "import { formatCurrency } from './currency';\n\n// Generate and download invoice PDF\nexport const downloadInvoice = order => {\n  // Create invoice content\n  const invoiceContent = generateInvoiceHTML(order);\n\n  // Create a new window for printing\n  const printWindow = window.open('', '_blank');\n  printWindow.document.write(invoiceContent);\n  printWindow.document.close();\n\n  // Wait for content to load then print\n  printWindow.onload = () => {\n    printWindow.print();\n    // Close window after printing (optional)\n    setTimeout(() => {\n      printWindow.close();\n    }, 1000);\n  };\n};\n\n// Generate invoice HTML content\nconst generateInvoiceHTML = order => {\n  const subtotal = order.items.reduce((sum, item) => sum + item.price * item.quantity, 0);\n  const shipping = subtotal > 4000 ? 0 : 199;\n  const gst = subtotal * 0.18;\n  const total = subtotal + shipping + gst;\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Invoice - Order #${order.id}</title>\n      <style>\n        body {\n          font-family: Arial, sans-serif;\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 20px;\n          color: #333;\n        }\n        .header {\n          text-align: center;\n          border-bottom: 2px solid #2563eb;\n          padding-bottom: 20px;\n          margin-bottom: 30px;\n        }\n        .company-name {\n          font-size: 28px;\n          font-weight: bold;\n          color: #2563eb;\n          margin-bottom: 5px;\n        }\n        .invoice-title {\n          font-size: 24px;\n          margin: 20px 0;\n        }\n        .order-info {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 30px;\n        }\n        .info-section {\n          flex: 1;\n        }\n        .info-section h3 {\n          color: #2563eb;\n          border-bottom: 1px solid #e5e7eb;\n          padding-bottom: 5px;\n        }\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin: 20px 0;\n        }\n        .items-table th,\n        .items-table td {\n          border: 1px solid #e5e7eb;\n          padding: 12px;\n          text-align: left;\n        }\n        .items-table th {\n          background-color: #f3f4f6;\n          font-weight: bold;\n        }\n        .total-section {\n          margin-top: 30px;\n          text-align: right;\n        }\n        .total-row {\n          display: flex;\n          justify-content: space-between;\n          padding: 5px 0;\n          border-bottom: 1px solid #e5e7eb;\n        }\n        .total-row.final {\n          font-weight: bold;\n          font-size: 18px;\n          border-bottom: 2px solid #2563eb;\n          color: #2563eb;\n        }\n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #6b7280;\n          border-top: 1px solid #e5e7eb;\n          padding-top: 20px;\n        }\n        .status-badge {\n          display: inline-block;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: bold;\n          color: white;\n          background-color: ${getStatusColor(order.status)};\n        }\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"company-name\">🌱 EcoStore</div>\n        <div>Sustainable Shopping for a Better Tomorrow</div>\n        <div class=\"invoice-title\">INVOICE</div>\n      </div>\n\n      <div class=\"order-info\">\n        <div class=\"info-section\">\n          <h3>Order Details</h3>\n          <p><strong>Order ID:</strong> ${order.id}</p>\n          <p><strong>Order Date:</strong> ${new Date(order.date).toLocaleDateString('en-IN')}</p>\n          <p><strong>Status:</strong> <span class=\"status-badge\">${order.status}</span></p>\n          ${order.trackingNumber ? `<p><strong>Tracking:</strong> ${order.trackingNumber}</p>` : ''}\n        </div>\n        \n        <div class=\"info-section\">\n          <h3>Shipping Address</h3>\n          <p>${order.shippingAddress}</p>\n        </div>\n      </div>\n\n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th>Item</th>\n            <th>Quantity</th>\n            <th>Unit Price</th>\n            <th>Total</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${order.items.map(item => `\n            <tr>\n              <td>${item.name}</td>\n              <td>${item.quantity}</td>\n              <td>${formatCurrency(item.price)}</td>\n              <td>${formatCurrency(item.price * item.quantity)}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n\n      <div class=\"total-section\">\n        <div class=\"total-row\">\n          <span>Subtotal:</span>\n          <span>${formatCurrency(subtotal)}</span>\n        </div>\n        <div class=\"total-row\">\n          <span>Shipping:</span>\n          <span>${shipping === 0 ? 'FREE' : formatCurrency(shipping)}</span>\n        </div>\n        <div class=\"total-row\">\n          <span>GST (18%):</span>\n          <span>${formatCurrency(gst)}</span>\n        </div>\n        <div class=\"total-row final\">\n          <span>Total Amount:</span>\n          <span>${formatCurrency(total)}</span>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <p>Thank you for choosing EcoStore!</p>\n        <p>For any queries, contact <NAME_EMAIL> | +91-1234567890</p>\n        <p>This is a computer-generated invoice.</p>\n      </div>\n    </body>\n    </html>\n  `;\n};\n\n// Get status color for invoice\nconst getStatusColor = status => {\n  switch (status.toLowerCase()) {\n    case 'delivered':\n      return '#10b981';\n    case 'shipped':\n      return '#3b82f6';\n    case 'processing':\n      return '#f59e0b';\n    case 'cancelled':\n      return '#ef4444';\n    default:\n      return '#6b7280';\n  }\n};\n\n// Open order tracking modal/page\nexport const trackOrder = order => {\n  if (!order.trackingNumber) {\n    alert('Tracking number not available for this order yet.');\n    return;\n  }\n\n  // Create tracking modal content\n  const trackingContent = generateTrackingHTML(order);\n\n  // Create modal window\n  const trackingWindow = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');\n  trackingWindow.document.write(trackingContent);\n  trackingWindow.document.close();\n};\n\n// Generate tracking HTML content\nconst generateTrackingHTML = order => {\n  const trackingEvents = generateTrackingEvents(order);\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Track Order #${order.id}</title>\n      <style>\n        body {\n          font-family: Arial, sans-serif;\n          max-width: 600px;\n          margin: 0 auto;\n          padding: 20px;\n          background-color: #f9fafb;\n        }\n        .tracking-header {\n          background: linear-gradient(135deg, #2563eb, #1d4ed8);\n          color: white;\n          padding: 20px;\n          border-radius: 10px;\n          text-align: center;\n          margin-bottom: 20px;\n        }\n        .tracking-number {\n          font-size: 18px;\n          font-weight: bold;\n          margin-top: 10px;\n        }\n        .timeline {\n          background: white;\n          border-radius: 10px;\n          padding: 20px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .timeline-item {\n          display: flex;\n          margin-bottom: 20px;\n          position: relative;\n        }\n        .timeline-item:not(:last-child)::after {\n          content: '';\n          position: absolute;\n          left: 20px;\n          top: 40px;\n          width: 2px;\n          height: 40px;\n          background: #e5e7eb;\n        }\n        .timeline-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 15px;\n          flex-shrink: 0;\n        }\n        .timeline-icon.completed {\n          background: #10b981;\n          color: white;\n        }\n        .timeline-icon.current {\n          background: #3b82f6;\n          color: white;\n        }\n        .timeline-icon.pending {\n          background: #e5e7eb;\n          color: #6b7280;\n        }\n        .timeline-content h4 {\n          margin: 0 0 5px 0;\n          color: #1f2937;\n        }\n        .timeline-content p {\n          margin: 0;\n          color: #6b7280;\n          font-size: 14px;\n        }\n        .estimated-delivery {\n          background: #eff6ff;\n          border: 1px solid #bfdbfe;\n          border-radius: 8px;\n          padding: 15px;\n          margin-top: 20px;\n          text-align: center;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"tracking-header\">\n        <h2>Order Tracking</h2>\n        <div>Order #${order.id}</div>\n        <div class=\"tracking-number\">Tracking: ${order.trackingNumber}</div>\n      </div>\n\n      <div class=\"timeline\">\n        ${trackingEvents.map(event => `\n          <div class=\"timeline-item\">\n            <div class=\"timeline-icon ${event.status}\">\n              <i class=\"fas ${event.icon}\"></i>\n            </div>\n            <div class=\"timeline-content\">\n              <h4>${event.title}</h4>\n              <p>${event.description}</p>\n              <p><strong>${event.date}</strong></p>\n            </div>\n          </div>\n        `).join('')}\n      </div>\n\n      ${order.status !== 'Delivered' ? `\n        <div class=\"estimated-delivery\">\n          <strong>Estimated Delivery:</strong> ${getEstimatedDelivery(order)}\n        </div>\n      ` : ''}\n\n      <script>\n        // Add Font Awesome for icons\n        const link = document.createElement('link');\n        link.rel = 'stylesheet';\n        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';\n        document.head.appendChild(link);\n      </script>\n    </body>\n    </html>\n  `;\n};\n\n// Generate tracking events based on order status\nconst generateTrackingEvents = order => {\n  const orderDate = new Date(order.date);\n  const events = [{\n    title: 'Order Placed',\n    description: 'Your order has been successfully placed',\n    date: orderDate.toLocaleDateString('en-IN'),\n    icon: 'fa-shopping-cart',\n    status: 'completed'\n  }];\n  if (['Shipped', 'Delivered'].includes(order.status)) {\n    const shippedDate = new Date(orderDate.getTime() + 24 * 60 * 60 * 1000);\n    events.push({\n      title: 'Order Shipped',\n      description: 'Your order is on its way',\n      date: shippedDate.toLocaleDateString('en-IN'),\n      icon: 'fa-truck',\n      status: order.status === 'Delivered' ? 'completed' : 'current'\n    });\n  }\n  if (order.status === 'Delivered') {\n    const deliveredDate = new Date(orderDate.getTime() + 3 * 24 * 60 * 60 * 1000);\n    events.push({\n      title: 'Delivered',\n      description: 'Package delivered successfully',\n      date: deliveredDate.toLocaleDateString('en-IN'),\n      icon: 'fa-home',\n      status: 'completed'\n    });\n  } else if (order.status === 'Processing') {\n    events.push({\n      title: 'Processing',\n      description: 'Your order is being prepared',\n      date: 'In progress',\n      icon: 'fa-cog',\n      status: 'current'\n    });\n  }\n  return events;\n};\n\n// Get estimated delivery date\nconst getEstimatedDelivery = order => {\n  const orderDate = new Date(order.date);\n  const estimatedDate = new Date(orderDate.getTime() + 5 * 24 * 60 * 60 * 1000);\n  return estimatedDate.toLocaleDateString('en-IN');\n};", "map": {"version": 3, "names": ["formatCurrency", "downloadInvoice", "order", "invoiceContent", "generateInvoiceHTML", "printWindow", "window", "open", "document", "write", "close", "onload", "print", "setTimeout", "subtotal", "items", "reduce", "sum", "item", "price", "quantity", "shipping", "gst", "total", "id", "getStatusColor", "status", "Date", "date", "toLocaleDateString", "trackingNumber", "shippingAddress", "map", "name", "join", "toLowerCase", "trackOrder", "alert", "trackingContent", "generateTrackingHTML", "trackingWindow", "trackingEvents", "generateTrackingEvents", "event", "icon", "title", "description", "getEstimatedDelivery", "orderDate", "events", "includes", "shippedDate", "getTime", "push", "deliveredDate", "estimatedDate"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/utils/orderUtils.js"], "sourcesContent": ["import { formatCurrency } from './currency';\n\n// Generate and download invoice PDF\nexport const downloadInvoice = (order) => {\n  // Create invoice content\n  const invoiceContent = generateInvoiceHTML(order);\n  \n  // Create a new window for printing\n  const printWindow = window.open('', '_blank');\n  printWindow.document.write(invoiceContent);\n  printWindow.document.close();\n  \n  // Wait for content to load then print\n  printWindow.onload = () => {\n    printWindow.print();\n    // Close window after printing (optional)\n    setTimeout(() => {\n      printWindow.close();\n    }, 1000);\n  };\n};\n\n// Generate invoice HTML content\nconst generateInvoiceHTML = (order) => {\n  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n  const shipping = subtotal > 4000 ? 0 : 199;\n  const gst = subtotal * 0.18;\n  const total = subtotal + shipping + gst;\n\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Invoice - Order #${order.id}</title>\n      <style>\n        body {\n          font-family: Arial, sans-serif;\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 20px;\n          color: #333;\n        }\n        .header {\n          text-align: center;\n          border-bottom: 2px solid #2563eb;\n          padding-bottom: 20px;\n          margin-bottom: 30px;\n        }\n        .company-name {\n          font-size: 28px;\n          font-weight: bold;\n          color: #2563eb;\n          margin-bottom: 5px;\n        }\n        .invoice-title {\n          font-size: 24px;\n          margin: 20px 0;\n        }\n        .order-info {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 30px;\n        }\n        .info-section {\n          flex: 1;\n        }\n        .info-section h3 {\n          color: #2563eb;\n          border-bottom: 1px solid #e5e7eb;\n          padding-bottom: 5px;\n        }\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin: 20px 0;\n        }\n        .items-table th,\n        .items-table td {\n          border: 1px solid #e5e7eb;\n          padding: 12px;\n          text-align: left;\n        }\n        .items-table th {\n          background-color: #f3f4f6;\n          font-weight: bold;\n        }\n        .total-section {\n          margin-top: 30px;\n          text-align: right;\n        }\n        .total-row {\n          display: flex;\n          justify-content: space-between;\n          padding: 5px 0;\n          border-bottom: 1px solid #e5e7eb;\n        }\n        .total-row.final {\n          font-weight: bold;\n          font-size: 18px;\n          border-bottom: 2px solid #2563eb;\n          color: #2563eb;\n        }\n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #6b7280;\n          border-top: 1px solid #e5e7eb;\n          padding-top: 20px;\n        }\n        .status-badge {\n          display: inline-block;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: bold;\n          color: white;\n          background-color: ${getStatusColor(order.status)};\n        }\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"company-name\">🌱 EcoStore</div>\n        <div>Sustainable Shopping for a Better Tomorrow</div>\n        <div class=\"invoice-title\">INVOICE</div>\n      </div>\n\n      <div class=\"order-info\">\n        <div class=\"info-section\">\n          <h3>Order Details</h3>\n          <p><strong>Order ID:</strong> ${order.id}</p>\n          <p><strong>Order Date:</strong> ${new Date(order.date).toLocaleDateString('en-IN')}</p>\n          <p><strong>Status:</strong> <span class=\"status-badge\">${order.status}</span></p>\n          ${order.trackingNumber ? `<p><strong>Tracking:</strong> ${order.trackingNumber}</p>` : ''}\n        </div>\n        \n        <div class=\"info-section\">\n          <h3>Shipping Address</h3>\n          <p>${order.shippingAddress}</p>\n        </div>\n      </div>\n\n      <table class=\"items-table\">\n        <thead>\n          <tr>\n            <th>Item</th>\n            <th>Quantity</th>\n            <th>Unit Price</th>\n            <th>Total</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${order.items.map(item => `\n            <tr>\n              <td>${item.name}</td>\n              <td>${item.quantity}</td>\n              <td>${formatCurrency(item.price)}</td>\n              <td>${formatCurrency(item.price * item.quantity)}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n\n      <div class=\"total-section\">\n        <div class=\"total-row\">\n          <span>Subtotal:</span>\n          <span>${formatCurrency(subtotal)}</span>\n        </div>\n        <div class=\"total-row\">\n          <span>Shipping:</span>\n          <span>${shipping === 0 ? 'FREE' : formatCurrency(shipping)}</span>\n        </div>\n        <div class=\"total-row\">\n          <span>GST (18%):</span>\n          <span>${formatCurrency(gst)}</span>\n        </div>\n        <div class=\"total-row final\">\n          <span>Total Amount:</span>\n          <span>${formatCurrency(total)}</span>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <p>Thank you for choosing EcoStore!</p>\n        <p>For any queries, contact <NAME_EMAIL> | +91-1234567890</p>\n        <p>This is a computer-generated invoice.</p>\n      </div>\n    </body>\n    </html>\n  `;\n};\n\n// Get status color for invoice\nconst getStatusColor = (status) => {\n  switch (status.toLowerCase()) {\n    case 'delivered': return '#10b981';\n    case 'shipped': return '#3b82f6';\n    case 'processing': return '#f59e0b';\n    case 'cancelled': return '#ef4444';\n    default: return '#6b7280';\n  }\n};\n\n// Open order tracking modal/page\nexport const trackOrder = (order) => {\n  if (!order.trackingNumber) {\n    alert('Tracking number not available for this order yet.');\n    return;\n  }\n\n  // Create tracking modal content\n  const trackingContent = generateTrackingHTML(order);\n  \n  // Create modal window\n  const trackingWindow = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');\n  trackingWindow.document.write(trackingContent);\n  trackingWindow.document.close();\n};\n\n// Generate tracking HTML content\nconst generateTrackingHTML = (order) => {\n  const trackingEvents = generateTrackingEvents(order);\n  \n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Track Order #${order.id}</title>\n      <style>\n        body {\n          font-family: Arial, sans-serif;\n          max-width: 600px;\n          margin: 0 auto;\n          padding: 20px;\n          background-color: #f9fafb;\n        }\n        .tracking-header {\n          background: linear-gradient(135deg, #2563eb, #1d4ed8);\n          color: white;\n          padding: 20px;\n          border-radius: 10px;\n          text-align: center;\n          margin-bottom: 20px;\n        }\n        .tracking-number {\n          font-size: 18px;\n          font-weight: bold;\n          margin-top: 10px;\n        }\n        .timeline {\n          background: white;\n          border-radius: 10px;\n          padding: 20px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .timeline-item {\n          display: flex;\n          margin-bottom: 20px;\n          position: relative;\n        }\n        .timeline-item:not(:last-child)::after {\n          content: '';\n          position: absolute;\n          left: 20px;\n          top: 40px;\n          width: 2px;\n          height: 40px;\n          background: #e5e7eb;\n        }\n        .timeline-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 15px;\n          flex-shrink: 0;\n        }\n        .timeline-icon.completed {\n          background: #10b981;\n          color: white;\n        }\n        .timeline-icon.current {\n          background: #3b82f6;\n          color: white;\n        }\n        .timeline-icon.pending {\n          background: #e5e7eb;\n          color: #6b7280;\n        }\n        .timeline-content h4 {\n          margin: 0 0 5px 0;\n          color: #1f2937;\n        }\n        .timeline-content p {\n          margin: 0;\n          color: #6b7280;\n          font-size: 14px;\n        }\n        .estimated-delivery {\n          background: #eff6ff;\n          border: 1px solid #bfdbfe;\n          border-radius: 8px;\n          padding: 15px;\n          margin-top: 20px;\n          text-align: center;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"tracking-header\">\n        <h2>Order Tracking</h2>\n        <div>Order #${order.id}</div>\n        <div class=\"tracking-number\">Tracking: ${order.trackingNumber}</div>\n      </div>\n\n      <div class=\"timeline\">\n        ${trackingEvents.map(event => `\n          <div class=\"timeline-item\">\n            <div class=\"timeline-icon ${event.status}\">\n              <i class=\"fas ${event.icon}\"></i>\n            </div>\n            <div class=\"timeline-content\">\n              <h4>${event.title}</h4>\n              <p>${event.description}</p>\n              <p><strong>${event.date}</strong></p>\n            </div>\n          </div>\n        `).join('')}\n      </div>\n\n      ${order.status !== 'Delivered' ? `\n        <div class=\"estimated-delivery\">\n          <strong>Estimated Delivery:</strong> ${getEstimatedDelivery(order)}\n        </div>\n      ` : ''}\n\n      <script>\n        // Add Font Awesome for icons\n        const link = document.createElement('link');\n        link.rel = 'stylesheet';\n        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';\n        document.head.appendChild(link);\n      </script>\n    </body>\n    </html>\n  `;\n};\n\n// Generate tracking events based on order status\nconst generateTrackingEvents = (order) => {\n  const orderDate = new Date(order.date);\n  const events = [\n    {\n      title: 'Order Placed',\n      description: 'Your order has been successfully placed',\n      date: orderDate.toLocaleDateString('en-IN'),\n      icon: 'fa-shopping-cart',\n      status: 'completed'\n    }\n  ];\n\n  if (['Shipped', 'Delivered'].includes(order.status)) {\n    const shippedDate = new Date(orderDate.getTime() + 24 * 60 * 60 * 1000);\n    events.push({\n      title: 'Order Shipped',\n      description: 'Your order is on its way',\n      date: shippedDate.toLocaleDateString('en-IN'),\n      icon: 'fa-truck',\n      status: order.status === 'Delivered' ? 'completed' : 'current'\n    });\n  }\n\n  if (order.status === 'Delivered') {\n    const deliveredDate = new Date(orderDate.getTime() + 3 * 24 * 60 * 60 * 1000);\n    events.push({\n      title: 'Delivered',\n      description: 'Package delivered successfully',\n      date: deliveredDate.toLocaleDateString('en-IN'),\n      icon: 'fa-home',\n      status: 'completed'\n    });\n  } else if (order.status === 'Processing') {\n    events.push({\n      title: 'Processing',\n      description: 'Your order is being prepared',\n      date: 'In progress',\n      icon: 'fa-cog',\n      status: 'current'\n    });\n  }\n\n  return events;\n};\n\n// Get estimated delivery date\nconst getEstimatedDelivery = (order) => {\n  const orderDate = new Date(order.date);\n  const estimatedDate = new Date(orderDate.getTime() + 5 * 24 * 60 * 60 * 1000);\n  return estimatedDate.toLocaleDateString('en-IN');\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,YAAY;;AAE3C;AACA,OAAO,MAAMC,eAAe,GAAIC,KAAK,IAAK;EACxC;EACA,MAAMC,cAAc,GAAGC,mBAAmB,CAACF,KAAK,CAAC;;EAEjD;EACA,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EAC7CF,WAAW,CAACG,QAAQ,CAACC,KAAK,CAACN,cAAc,CAAC;EAC1CE,WAAW,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;;EAE5B;EACAL,WAAW,CAACM,MAAM,GAAG,MAAM;IACzBN,WAAW,CAACO,KAAK,CAAC,CAAC;IACnB;IACAC,UAAU,CAAC,MAAM;MACfR,WAAW,CAACK,KAAK,CAAC,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;AACH,CAAC;;AAED;AACA,MAAMN,mBAAmB,GAAIF,KAAK,IAAK;EACrC,MAAMY,QAAQ,GAAGZ,KAAK,CAACa,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAIC,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACE,QAAS,EAAE,CAAC,CAAC;EACzF,MAAMC,QAAQ,GAAGP,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG;EAC1C,MAAMQ,GAAG,GAAGR,QAAQ,GAAG,IAAI;EAC3B,MAAMS,KAAK,GAAGT,QAAQ,GAAGO,QAAQ,GAAGC,GAAG;EAEvC,OAAO;AACT;AACA;AACA;AACA,gCAAgCpB,KAAK,CAACsB,EAAE;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BC,cAAc,CAACvB,KAAK,CAACwB,MAAM,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0CxB,KAAK,CAACsB,EAAE;AAClD,4CAA4C,IAAIG,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;AAC5F,mEAAmE3B,KAAK,CAACwB,MAAM;AAC/E,YAAYxB,KAAK,CAAC4B,cAAc,GAAG,iCAAiC5B,KAAK,CAAC4B,cAAc,MAAM,GAAG,EAAE;AACnG;AACA;AACA;AACA;AACA,eAAe5B,KAAK,CAAC6B,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY7B,KAAK,CAACa,KAAK,CAACiB,GAAG,CAACd,IAAI,IAAI;AACpC;AACA,oBAAoBA,IAAI,CAACe,IAAI;AAC7B,oBAAoBf,IAAI,CAACE,QAAQ;AACjC,oBAAoBpB,cAAc,CAACkB,IAAI,CAACC,KAAK,CAAC;AAC9C,oBAAoBnB,cAAc,CAACkB,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACE,QAAQ,CAAC;AAC9D;AACA,WAAW,CAAC,CAACc,IAAI,CAAC,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBlC,cAAc,CAACc,QAAQ,CAAC;AAC1C;AACA;AACA;AACA,kBAAkBO,QAAQ,KAAK,CAAC,GAAG,MAAM,GAAGrB,cAAc,CAACqB,QAAQ,CAAC;AACpE;AACA;AACA;AACA,kBAAkBrB,cAAc,CAACsB,GAAG,CAAC;AACrC;AACA;AACA;AACA,kBAAkBtB,cAAc,CAACuB,KAAK,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,QAAQA,MAAM,CAACS,WAAW,CAAC,CAAC;IAC1B,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC,KAAK,YAAY;MAAE,OAAO,SAAS;IACnC,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAIlC,KAAK,IAAK;EACnC,IAAI,CAACA,KAAK,CAAC4B,cAAc,EAAE;IACzBO,KAAK,CAAC,mDAAmD,CAAC;IAC1D;EACF;;EAEA;EACA,MAAMC,eAAe,GAAGC,oBAAoB,CAACrC,KAAK,CAAC;;EAEnD;EACA,MAAMsC,cAAc,GAAGlC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,qCAAqC,CAAC;EACvFiC,cAAc,CAAChC,QAAQ,CAACC,KAAK,CAAC6B,eAAe,CAAC;EAC9CE,cAAc,CAAChC,QAAQ,CAACE,KAAK,CAAC,CAAC;AACjC,CAAC;;AAED;AACA,MAAM6B,oBAAoB,GAAIrC,KAAK,IAAK;EACtC,MAAMuC,cAAc,GAAGC,sBAAsB,CAACxC,KAAK,CAAC;EAEpD,OAAO;AACT;AACA;AACA;AACA,4BAA4BA,KAAK,CAACsB,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBtB,KAAK,CAACsB,EAAE;AAC9B,iDAAiDtB,KAAK,CAAC4B,cAAc;AACrE;AACA;AACA;AACA,UAAUW,cAAc,CAACT,GAAG,CAACW,KAAK,IAAI;AACtC;AACA,wCAAwCA,KAAK,CAACjB,MAAM;AACpD,8BAA8BiB,KAAK,CAACC,IAAI;AACxC;AACA;AACA,oBAAoBD,KAAK,CAACE,KAAK;AAC/B,mBAAmBF,KAAK,CAACG,WAAW;AACpC,2BAA2BH,KAAK,CAACf,IAAI;AACrC;AACA;AACA,SAAS,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC;AACnB;AACA;AACA,QAAQhC,KAAK,CAACwB,MAAM,KAAK,WAAW,GAAG;AACvC;AACA,iDAAiDqB,oBAAoB,CAAC7C,KAAK,CAAC;AAC5E;AACA,OAAO,GAAG,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA,MAAMwC,sBAAsB,GAAIxC,KAAK,IAAK;EACxC,MAAM8C,SAAS,GAAG,IAAIrB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAC;EACtC,MAAMqB,MAAM,GAAG,CACb;IACEJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,yCAAyC;IACtDlB,IAAI,EAAEoB,SAAS,CAACnB,kBAAkB,CAAC,OAAO,CAAC;IAC3Ce,IAAI,EAAE,kBAAkB;IACxBlB,MAAM,EAAE;EACV,CAAC,CACF;EAED,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACwB,QAAQ,CAAChD,KAAK,CAACwB,MAAM,CAAC,EAAE;IACnD,MAAMyB,WAAW,GAAG,IAAIxB,IAAI,CAACqB,SAAS,CAACI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvEH,MAAM,CAACI,IAAI,CAAC;MACVR,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,0BAA0B;MACvClB,IAAI,EAAEuB,WAAW,CAACtB,kBAAkB,CAAC,OAAO,CAAC;MAC7Ce,IAAI,EAAE,UAAU;MAChBlB,MAAM,EAAExB,KAAK,CAACwB,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG;IACvD,CAAC,CAAC;EACJ;EAEA,IAAIxB,KAAK,CAACwB,MAAM,KAAK,WAAW,EAAE;IAChC,MAAM4B,aAAa,GAAG,IAAI3B,IAAI,CAACqB,SAAS,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAC7EH,MAAM,CAACI,IAAI,CAAC;MACVR,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gCAAgC;MAC7ClB,IAAI,EAAE0B,aAAa,CAACzB,kBAAkB,CAAC,OAAO,CAAC;MAC/Ce,IAAI,EAAE,SAAS;MACflB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIxB,KAAK,CAACwB,MAAM,KAAK,YAAY,EAAE;IACxCuB,MAAM,CAACI,IAAI,CAAC;MACVR,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,8BAA8B;MAC3ClB,IAAI,EAAE,aAAa;MACnBgB,IAAI,EAAE,QAAQ;MACdlB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,OAAOuB,MAAM;AACf,CAAC;;AAED;AACA,MAAMF,oBAAoB,GAAI7C,KAAK,IAAK;EACtC,MAAM8C,SAAS,GAAG,IAAIrB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAC;EACtC,MAAM2B,aAAa,GAAG,IAAI5B,IAAI,CAACqB,SAAS,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC7E,OAAOG,aAAa,CAAC1B,kBAAkB,CAAC,OAAO,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}