{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\OrderCard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OrderCard({\n  order,\n  onReorder,\n  onReview\n}) {\n  _s();\n  const [showDetails, setShowDetails] = useState(false);\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'delivered':\n        return '#10b981';\n      case 'shipped':\n        return '#3b82f6';\n      case 'processing':\n        return '#f59e0b';\n      case 'cancelled':\n        return '#ef4444';\n      default:\n        return '#6b7280';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status.toLowerCase()) {\n      case 'delivered':\n        return 'fas fa-check-circle';\n      case 'shipped':\n        return 'fas fa-truck';\n      case 'processing':\n        return 'fas fa-clock';\n      case 'cancelled':\n        return 'fas fa-times-circle';\n      default:\n        return 'fas fa-question-circle';\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-main-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-id-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [\"Order #\", order.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"order-date\",\n            children: formatDate(order.date)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-status-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-badge\",\n            style: {\n              backgroundColor: getStatusColor(order.status)\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: getStatusIcon(order.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), order.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-total\",\n            children: [\"$\", order.total.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"toggle-details-btn\",\n        onClick: () => setShowDetails(!showDetails),\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: `fas fa-chevron-${showDetails ? 'up' : 'down'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-items-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"items-images\",\n        children: [order.items.slice(0, 3).map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"item-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: item.imageUrl,\n            alt: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), item.quantity > 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"quantity-badge\",\n            children: item.quantity\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)), order.items.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"more-items\",\n          children: [\"+\", order.items.length - 3]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"items-summary\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"items-count\",\n          children: [order.items.length, \" item\", order.items.length !== 1 ? 's' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), order.trackingNumber && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tracking-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-truck\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Tracking: \", order.trackingNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-timeline\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timeline-item completed\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shopping-cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Order Placed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatDate(order.date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `timeline-item ${['shipped', 'delivered'].includes(order.status.toLowerCase()) ? 'completed' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-truck\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Shipped\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: order.status === 'Shipped' || order.status === 'Delivered' ? 'In transit' : 'Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `timeline-item ${order.status.toLowerCase() === 'delivered' ? 'completed' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Delivered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: order.status === 'Delivered' ? 'Package delivered' : 'Estimated delivery'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-items-detailed\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Items in this order:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), order.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detailed-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: item.imageUrl,\n            alt: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"item-price\",\n              children: [\"$\", item.price, \" \\xD7 \", item.quantity, \" = $\", (item.price * item.quantity).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/product/${item.id}`,\n            className: \"view-product-btn\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-eye\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"shipping-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Shipping Address:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: order.shippingAddress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onReorder(order),\n        className: \"action-btn secondary\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-redo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), \"Reorder\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), order.trackingNumber && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-btn secondary\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-truck\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), \"Track Package\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this), order.status === 'Delivered' && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onReview(order),\n        className: \"action-btn primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-star\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this), \"Write Review\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-btn secondary\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-download\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), \"Invoice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .order-card {\n          background: white;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n          transition: all 0.3s ease;\n          margin-bottom: 1.5rem;\n        }\n\n        .order-card:hover {\n          box-shadow: var(--shadow-lg);\n          transform: translateY(-2px);\n        }\n\n        .order-header {\n          padding: 1.5rem;\n          border-bottom: 1px solid var(--border-color);\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .order-main-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          flex: 1;\n        }\n\n        .order-id-section h4 {\n          margin: 0 0 0.25rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .order-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-status-section {\n          text-align: right;\n        }\n\n        .status-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          color: white;\n          font-size: 0.875rem;\n          font-weight: 600;\n          margin-bottom: 0.5rem;\n        }\n\n        .order-total {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n        }\n\n        .toggle-details-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 50%;\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-left: 1rem;\n        }\n\n        .toggle-details-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .order-items-preview {\n          padding: 1.5rem;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: var(--bg-secondary);\n        }\n\n        .items-images {\n          display: flex;\n          gap: 0.5rem;\n          align-items: center;\n        }\n\n        .item-image {\n          position: relative;\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          overflow: hidden;\n          border: 2px solid white;\n          box-shadow: var(--shadow-sm);\n        }\n\n        .item-image img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .quantity-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          width: 20px;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .more-items {\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          background: var(--text-secondary);\n          color: white;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .items-summary {\n          text-align: right;\n        }\n\n        .items-count {\n          display: block;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin-bottom: 0.25rem;\n        }\n\n        .tracking-info {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          color: var(--primary-color);\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        .order-details {\n          padding: 1.5rem;\n          border-top: 1px solid var(--border-color);\n          background: #fafbfc;\n        }\n\n        .order-timeline {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 2rem;\n          position: relative;\n        }\n\n        .order-timeline::before {\n          content: '';\n          position: absolute;\n          top: 20px;\n          left: 20px;\n          right: 20px;\n          height: 2px;\n          background: var(--border-color);\n          z-index: 1;\n        }\n\n        .timeline-item {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          flex: 1;\n          position: relative;\n          z-index: 2;\n        }\n\n        .timeline-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: var(--bg-secondary);\n          border: 2px solid var(--border-color);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 0.75rem;\n          transition: all 0.3s ease;\n        }\n\n        .timeline-item.completed .timeline-icon {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .timeline-content h5 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .timeline-content span {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .order-items-detailed h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .detailed-item {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 0.75rem;\n          background: white;\n          border-radius: 0.5rem;\n          margin-bottom: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .detailed-item img {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .item-info {\n          flex: 1;\n        }\n\n        .item-info h6 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .item-price {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .view-product-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 0.375rem;\n          width: 36px;\n          height: 36px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: var(--text-secondary);\n          text-decoration: none;\n          transition: all 0.2s;\n        }\n\n        .view-product-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .shipping-info {\n          margin-top: 1.5rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .shipping-info h5 {\n          margin: 0 0 0.5rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .shipping-info p {\n          margin: 0;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-actions {\n          padding: 1rem 1.5rem;\n          background: var(--bg-secondary);\n          border-top: 1px solid var(--border-color);\n          display: flex;\n          gap: 0.75rem;\n          flex-wrap: wrap;\n        }\n\n        .action-btn {\n          background: white;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem 1rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-size: 0.875rem;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n          text-decoration: none;\n          color: var(--text-primary);\n        }\n\n        .action-btn:hover {\n          background: var(--bg-secondary);\n          transform: translateY(-1px);\n        }\n\n        .action-btn.primary {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .action-btn.primary:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .order-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-main-info {\n            width: 100%;\n          }\n\n          .order-items-preview {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-timeline {\n            flex-direction: column;\n            gap: 1rem;\n          }\n\n          .order-timeline::before {\n            display: none;\n          }\n\n          .timeline-item {\n            flex-direction: row;\n            text-align: left;\n          }\n\n          .timeline-icon {\n            margin-bottom: 0;\n            margin-right: 1rem;\n          }\n\n          .order-actions {\n            flex-direction: column;\n          }\n\n          .action-btn {\n            justify-content: center;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(OrderCard, \"n2rC7YX8Mzz154E9USQBvseY7a0=\");\n_c = OrderCard;\nexport default OrderCard;\nvar _c;\n$RefreshReg$(_c, \"OrderCard\");", "map": {"version": 3, "names": ["React", "useState", "Link", "jsxDEV", "_jsxDEV", "OrderCard", "order", "onReorder", "onReview", "_s", "showDetails", "setShowDetails", "getStatusColor", "status", "toLowerCase", "getStatusIcon", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "date", "style", "backgroundColor", "total", "toFixed", "onClick", "items", "slice", "map", "item", "index", "src", "imageUrl", "alt", "name", "quantity", "length", "trackingNumber", "includes", "price", "to", "shippingAddress", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/OrderCard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\n\nfunction OrderCard({ order, onReorder, onReview }) {\n  const [showDetails, setShowDetails] = useState(false);\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'delivered': return '#10b981';\n      case 'shipped': return '#3b82f6';\n      case 'processing': return '#f59e0b';\n      case 'cancelled': return '#ef4444';\n      default: return '#6b7280';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status.toLowerCase()) {\n      case 'delivered': return 'fas fa-check-circle';\n      case 'shipped': return 'fas fa-truck';\n      case 'processing': return 'fas fa-clock';\n      case 'cancelled': return 'fas fa-times-circle';\n      default: return 'fas fa-question-circle';\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <div className=\"order-card\">\n      <div className=\"order-header\">\n        <div className=\"order-main-info\">\n          <div className=\"order-id-section\">\n            <h4>Order #{order.id}</h4>\n            <span className=\"order-date\">{formatDate(order.date)}</span>\n          </div>\n          <div className=\"order-status-section\">\n            <div \n              className=\"status-badge\"\n              style={{ backgroundColor: getStatusColor(order.status) }}\n            >\n              <i className={getStatusIcon(order.status)}></i>\n              {order.status}\n            </div>\n            <div className=\"order-total\">${order.total.toFixed(2)}</div>\n          </div>\n        </div>\n        \n        <button \n          className=\"toggle-details-btn\"\n          onClick={() => setShowDetails(!showDetails)}\n        >\n          <i className={`fas fa-chevron-${showDetails ? 'up' : 'down'}`}></i>\n        </button>\n      </div>\n\n      <div className=\"order-items-preview\">\n        <div className=\"items-images\">\n          {order.items.slice(0, 3).map((item, index) => (\n            <div key={index} className=\"item-image\">\n              <img src={item.imageUrl} alt={item.name} />\n              {item.quantity > 1 && (\n                <span className=\"quantity-badge\">{item.quantity}</span>\n              )}\n            </div>\n          ))}\n          {order.items.length > 3 && (\n            <div className=\"more-items\">\n              +{order.items.length - 3}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"items-summary\">\n          <span className=\"items-count\">\n            {order.items.length} item{order.items.length !== 1 ? 's' : ''}\n          </span>\n          {order.trackingNumber && (\n            <div className=\"tracking-info\">\n              <i className=\"fas fa-truck\"></i>\n              <span>Tracking: {order.trackingNumber}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {showDetails && (\n        <div className=\"order-details\">\n          <div className=\"order-timeline\">\n            <div className=\"timeline-item completed\">\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-shopping-cart\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Order Placed</h5>\n                <span>{formatDate(order.date)}</span>\n              </div>\n            </div>\n            \n            <div className={`timeline-item ${['shipped', 'delivered'].includes(order.status.toLowerCase()) ? 'completed' : ''}`}>\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-truck\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Shipped</h5>\n                <span>{order.status === 'Shipped' || order.status === 'Delivered' ? 'In transit' : 'Pending'}</span>\n              </div>\n            </div>\n            \n            <div className={`timeline-item ${order.status.toLowerCase() === 'delivered' ? 'completed' : ''}`}>\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-home\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Delivered</h5>\n                <span>{order.status === 'Delivered' ? 'Package delivered' : 'Estimated delivery'}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"order-items-detailed\">\n            <h5>Items in this order:</h5>\n            {order.items.map((item, index) => (\n              <div key={index} className=\"detailed-item\">\n                <img src={item.imageUrl} alt={item.name} />\n                <div className=\"item-info\">\n                  <h6>{item.name}</h6>\n                  <span className=\"item-price\">\n                    ${item.price} × {item.quantity} = ${(item.price * item.quantity).toFixed(2)}\n                  </span>\n                </div>\n                <Link to={`/product/${item.id}`} className=\"view-product-btn\">\n                  <i className=\"fas fa-eye\"></i>\n                </Link>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"shipping-info\">\n            <h5>Shipping Address:</h5>\n            <p>{order.shippingAddress}</p>\n          </div>\n        </div>\n      )}\n\n      <div className=\"order-actions\">\n        <button onClick={() => onReorder(order)} className=\"action-btn secondary\">\n          <i className=\"fas fa-redo\"></i>\n          Reorder\n        </button>\n        \n        {order.trackingNumber && (\n          <button className=\"action-btn secondary\">\n            <i className=\"fas fa-truck\"></i>\n            Track Package\n          </button>\n        )}\n        \n        {order.status === 'Delivered' && (\n          <button onClick={() => onReview(order)} className=\"action-btn primary\">\n            <i className=\"fas fa-star\"></i>\n            Write Review\n          </button>\n        )}\n        \n        <button className=\"action-btn secondary\">\n          <i className=\"fas fa-download\"></i>\n          Invoice\n        </button>\n      </div>\n\n      <style jsx>{`\n        .order-card {\n          background: white;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n          transition: all 0.3s ease;\n          margin-bottom: 1.5rem;\n        }\n\n        .order-card:hover {\n          box-shadow: var(--shadow-lg);\n          transform: translateY(-2px);\n        }\n\n        .order-header {\n          padding: 1.5rem;\n          border-bottom: 1px solid var(--border-color);\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .order-main-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          flex: 1;\n        }\n\n        .order-id-section h4 {\n          margin: 0 0 0.25rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .order-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-status-section {\n          text-align: right;\n        }\n\n        .status-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          color: white;\n          font-size: 0.875rem;\n          font-weight: 600;\n          margin-bottom: 0.5rem;\n        }\n\n        .order-total {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n        }\n\n        .toggle-details-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 50%;\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-left: 1rem;\n        }\n\n        .toggle-details-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .order-items-preview {\n          padding: 1.5rem;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: var(--bg-secondary);\n        }\n\n        .items-images {\n          display: flex;\n          gap: 0.5rem;\n          align-items: center;\n        }\n\n        .item-image {\n          position: relative;\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          overflow: hidden;\n          border: 2px solid white;\n          box-shadow: var(--shadow-sm);\n        }\n\n        .item-image img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .quantity-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          width: 20px;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .more-items {\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          background: var(--text-secondary);\n          color: white;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .items-summary {\n          text-align: right;\n        }\n\n        .items-count {\n          display: block;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin-bottom: 0.25rem;\n        }\n\n        .tracking-info {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          color: var(--primary-color);\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        .order-details {\n          padding: 1.5rem;\n          border-top: 1px solid var(--border-color);\n          background: #fafbfc;\n        }\n\n        .order-timeline {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 2rem;\n          position: relative;\n        }\n\n        .order-timeline::before {\n          content: '';\n          position: absolute;\n          top: 20px;\n          left: 20px;\n          right: 20px;\n          height: 2px;\n          background: var(--border-color);\n          z-index: 1;\n        }\n\n        .timeline-item {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          flex: 1;\n          position: relative;\n          z-index: 2;\n        }\n\n        .timeline-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: var(--bg-secondary);\n          border: 2px solid var(--border-color);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 0.75rem;\n          transition: all 0.3s ease;\n        }\n\n        .timeline-item.completed .timeline-icon {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .timeline-content h5 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .timeline-content span {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .order-items-detailed h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .detailed-item {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 0.75rem;\n          background: white;\n          border-radius: 0.5rem;\n          margin-bottom: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .detailed-item img {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .item-info {\n          flex: 1;\n        }\n\n        .item-info h6 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .item-price {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .view-product-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 0.375rem;\n          width: 36px;\n          height: 36px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: var(--text-secondary);\n          text-decoration: none;\n          transition: all 0.2s;\n        }\n\n        .view-product-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .shipping-info {\n          margin-top: 1.5rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .shipping-info h5 {\n          margin: 0 0 0.5rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .shipping-info p {\n          margin: 0;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-actions {\n          padding: 1rem 1.5rem;\n          background: var(--bg-secondary);\n          border-top: 1px solid var(--border-color);\n          display: flex;\n          gap: 0.75rem;\n          flex-wrap: wrap;\n        }\n\n        .action-btn {\n          background: white;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem 1rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-size: 0.875rem;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n          text-decoration: none;\n          color: var(--text-primary);\n        }\n\n        .action-btn:hover {\n          background: var(--bg-secondary);\n          transform: translateY(-1px);\n        }\n\n        .action-btn.primary {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .action-btn.primary:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .order-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-main-info {\n            width: 100%;\n          }\n\n          .order-items-preview {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-timeline {\n            flex-direction: column;\n            gap: 1rem;\n          }\n\n          .order-timeline::before {\n            display: none;\n          }\n\n          .timeline-item {\n            flex-direction: row;\n            text-align: left;\n          }\n\n          .timeline-icon {\n            margin-bottom: 0;\n            margin-right: 1rem;\n          }\n\n          .order-actions {\n            flex-direction: column;\n          }\n\n          .action-btn {\n            justify-content: center;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default OrderCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACjD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMW,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACC,WAAW,CAAC,CAAC;MAC1B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,aAAa,GAAIF,MAAM,IAAK;IAChC,QAAQA,MAAM,CAACC,WAAW,CAAC,CAAC;MAC1B,KAAK,WAAW;QAAE,OAAO,qBAAqB;MAC9C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,YAAY;QAAE,OAAO,cAAc;MACxC,KAAK,WAAW;QAAE,OAAO,qBAAqB;MAC9C;QAAS,OAAO,wBAAwB;IAC1C;EACF,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBpB,OAAA;MAAKmB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BpB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BpB,OAAA;UAAKmB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BpB,OAAA;YAAAoB,QAAA,GAAI,SAAO,EAAClB,KAAK,CAACmB,EAAE;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BzB,OAAA;YAAMmB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAER,UAAU,CAACV,KAAK,CAACwB,IAAI;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNzB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCpB,OAAA;YACEmB,SAAS,EAAC,cAAc;YACxBQ,KAAK,EAAE;cAAEC,eAAe,EAAEpB,cAAc,CAACN,KAAK,CAACO,MAAM;YAAE,CAAE;YAAAW,QAAA,gBAEzDpB,OAAA;cAAGmB,SAAS,EAAER,aAAa,CAACT,KAAK,CAACO,MAAM;YAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC9CvB,KAAK,CAACO,MAAM;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNzB,OAAA;YAAKmB,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,GAAC,EAAClB,KAAK,CAAC2B,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QACEmB,SAAS,EAAC,oBAAoB;QAC9BY,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAAC,CAACD,WAAW,CAAE;QAAAc,QAAA,eAE5CpB,OAAA;UAAGmB,SAAS,EAAE,kBAAkBb,WAAW,GAAG,IAAI,GAAG,MAAM;QAAG;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzB,OAAA;MAAKmB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCpB,OAAA;QAAKmB,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1BlB,KAAK,CAAC8B,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACvCpC,OAAA;UAAiBmB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACrCpB,OAAA;YAAKqC,GAAG,EAAEF,IAAI,CAACG,QAAS;YAACC,GAAG,EAAEJ,IAAI,CAACK;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1CU,IAAI,CAACM,QAAQ,GAAG,CAAC,iBAChBzC,OAAA;YAAMmB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEe,IAAI,CAACM;UAAQ;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACvD;QAAA,GAJOW,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACN,CAAC,EACDvB,KAAK,CAAC8B,KAAK,CAACU,MAAM,GAAG,CAAC,iBACrB1C,OAAA;UAAKmB,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,GACzB,EAAClB,KAAK,CAAC8B,KAAK,CAACU,MAAM,GAAG,CAAC;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENzB,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpB,OAAA;UAAMmB,SAAS,EAAC,aAAa;UAAAC,QAAA,GAC1BlB,KAAK,CAAC8B,KAAK,CAACU,MAAM,EAAC,OAAK,EAACxC,KAAK,CAAC8B,KAAK,CAACU,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,EACNvB,KAAK,CAACyC,cAAc,iBACnB3C,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpB,OAAA;YAAGmB,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCzB,OAAA;YAAAoB,QAAA,GAAM,YAAU,EAAClB,KAAK,CAACyC,cAAc;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELnB,WAAW,iBACVN,OAAA;MAAKmB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BpB,OAAA;QAAKmB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpB,OAAA;UAAKmB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCpB,OAAA;YAAKmB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpB,OAAA;cAAGmB,SAAS,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNzB,OAAA;YAAKmB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BpB,OAAA;cAAAoB,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBzB,OAAA;cAAAoB,QAAA,EAAOR,UAAU,CAACV,KAAK,CAACwB,IAAI;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzB,OAAA;UAAKmB,SAAS,EAAE,iBAAiB,CAAC,SAAS,EAAE,WAAW,CAAC,CAACyB,QAAQ,CAAC1C,KAAK,CAACO,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,EAAE,EAAG;UAAAU,QAAA,gBAClHpB,OAAA;YAAKmB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpB,OAAA;cAAGmB,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNzB,OAAA;YAAKmB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BpB,OAAA;cAAAoB,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBzB,OAAA;cAAAoB,QAAA,EAAOlB,KAAK,CAACO,MAAM,KAAK,SAAS,IAAIP,KAAK,CAACO,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzB,OAAA;UAAKmB,SAAS,EAAE,iBAAiBjB,KAAK,CAACO,MAAM,CAACC,WAAW,CAAC,CAAC,KAAK,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;UAAAU,QAAA,gBAC/FpB,OAAA;YAAKmB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpB,OAAA;cAAGmB,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNzB,OAAA;YAAKmB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BpB,OAAA;cAAAoB,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBzB,OAAA;cAAAoB,QAAA,EAAOlB,KAAK,CAACO,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG;YAAoB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKmB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCpB,OAAA;UAAAoB,QAAA,EAAI;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5BvB,KAAK,CAAC8B,KAAK,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BpC,OAAA;UAAiBmB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACxCpB,OAAA;YAAKqC,GAAG,EAAEF,IAAI,CAACG,QAAS;YAACC,GAAG,EAAEJ,IAAI,CAACK;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CzB,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpB,OAAA;cAAAoB,QAAA,EAAKe,IAAI,CAACK;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpBzB,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,GAC1B,EAACe,IAAI,CAACU,KAAK,EAAC,QAAG,EAACV,IAAI,CAACM,QAAQ,EAAC,MAAI,EAAC,CAACN,IAAI,CAACU,KAAK,GAAGV,IAAI,CAACM,QAAQ,EAAEX,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzB,OAAA,CAACF,IAAI;YAACgD,EAAE,EAAE,YAAYX,IAAI,CAACd,EAAE,EAAG;YAACF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC3DpB,OAAA;cAAGmB,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA,GAVCW,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzB,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpB,OAAA;UAAAoB,QAAA,EAAI;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BzB,OAAA;UAAAoB,QAAA,EAAIlB,KAAK,CAAC6C;QAAe;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzB,OAAA;MAAKmB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BpB,OAAA;QAAQ+B,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAACD,KAAK,CAAE;QAACiB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACvEpB,OAAA;UAAGmB,SAAS,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,WAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERvB,KAAK,CAACyC,cAAc,iBACnB3C,OAAA;QAAQmB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACtCpB,OAAA;UAAGmB,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,iBAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAEAvB,KAAK,CAACO,MAAM,KAAK,WAAW,iBAC3BT,OAAA;QAAQ+B,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAACF,KAAK,CAAE;QAACiB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACpEpB,OAAA;UAAGmB,SAAS,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eAEDzB,OAAA;QAAQmB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACtCpB,OAAA;UAAGmB,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,WAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzB,OAAA;MAAOgD,GAAG;MAAA5B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACpB,EAAA,CAzjBQJ,SAAS;AAAAgD,EAAA,GAAThD,SAAS;AA2jBlB,eAAeA,SAAS;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}