{"version": 3, "names": ["_core", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_template", "DefineAccessorHelper", "template", "expression", "ast", "_compact", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "assertVersion", "setComputedProperties", "assumption", "loose", "pushComputedProps", "pushComputedPropsLoose", "pushComputedPropsSpec", "buildDefineAccessor", "state", "obj", "prop", "type", "kind", "key", "computed", "t", "isIdentifier", "stringLiteral", "name", "fn", "getValue", "helper", "availableHelper", "addHelper", "file", "get", "id", "scope", "generateUidIdentifier", "push", "init", "set", "cloneNode", "callExpression", "isObjectProperty", "value", "isObjectMethod", "functionExpression", "params", "body", "generator", "async", "pushAssign", "objId", "expressionStatement", "assignmentExpression", "memberExpression", "isLiteral", "info", "computedProps", "initPropExpression", "length", "CHUNK_LENGTH_CAP", "currentChunk", "computedPropsChunks", "chunk", "single", "node", "toCom<PERSON><PERSON>ey", "visitor", "ObjectExpression", "exit", "path", "parent", "hasComputed", "properties", "initProps", "foundComputed", "isSpreadElement", "generateUidIdentifierBasedOnNode", "objectExpression", "variableDeclaration", "variableDeclarator", "replaceWith", "replaceWithMultiple"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginPass, Scope } from \"@babel/core\";\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport template from \"@babel/template\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\ntype PropertyInfo = {\n  scope: Scope;\n  objId: t.Identifier;\n  body: t.Statement[];\n  computedProps: t.ObjectMember[];\n  initPropExpression: t.ObjectExpression;\n  state: PluginPass;\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var DefineAccessorHelper = template.expression.ast`\n    function (type, obj, key, fn) {\n      var desc = { configurable: true, enumerable: true };\n      desc[type] = fn;\n      return Object.defineProperty(obj, key, desc);\n    }\n  `;\n  // @ts-expect-error undocumented _compact node property\n  DefineAccessorHelper._compact = true;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const setComputedProperties =\n    api.assumption(\"setComputedProperties\") ?? options.loose;\n\n  const pushComputedProps = setComputedProperties\n    ? pushComputedPropsLoose\n    : pushComputedPropsSpec;\n\n  function buildDefineAccessor(\n    state: PluginPass,\n    obj: t.Expression,\n    prop: t.ObjectMethod,\n  ) {\n    const type = prop.kind as \"get\" | \"set\";\n    const key =\n      !prop.computed && t.isIdentifier(prop.key)\n        ? t.stringLiteral(prop.key.name)\n        : prop.key;\n    const fn = getValue(prop);\n    if (process.env.BABEL_8_BREAKING) {\n      return t.callExpression(state.addHelper(\"defineAccessor\"), [\n        t.stringLiteral(type),\n        obj,\n        key,\n        fn,\n      ]);\n    } else {\n      let helper: t.Identifier;\n      if (state.availableHelper(\"defineAccessor\")) {\n        helper = state.addHelper(\"defineAccessor\");\n      } else {\n        // Fallback for @babel/helpers <= 7.20.6, manually add helper function\n        const file = state.file;\n        helper = file.get(\"fallbackDefineAccessorHelper\");\n        if (!helper) {\n          const id = file.scope.generateUidIdentifier(\"defineAccessor\");\n          file.scope.push({\n            id,\n            init: DefineAccessorHelper,\n          });\n          file.set(\"fallbackDefineAccessorHelper\", (helper = id));\n        }\n        helper = t.cloneNode(helper);\n      }\n\n      return t.callExpression(helper, [t.stringLiteral(type), obj, key, fn]);\n    }\n  }\n\n  /**\n   * Get value of an object member under object expression.\n   * Returns a function expression if prop is a ObjectMethod.\n   *\n   * @param {t.ObjectMember} prop\n   * @returns t.Expression\n   */\n  function getValue(prop: t.ObjectMember) {\n    if (t.isObjectProperty(prop)) {\n      return prop.value as t.Expression;\n    } else if (t.isObjectMethod(prop)) {\n      return t.functionExpression(\n        null,\n        prop.params,\n        prop.body,\n        prop.generator,\n        prop.async,\n      );\n    }\n  }\n\n  function pushAssign(\n    objId: t.Identifier,\n    prop: t.ObjectMember,\n    body: t.Statement[],\n  ) {\n    body.push(\n      t.expressionStatement(\n        t.assignmentExpression(\n          \"=\",\n          t.memberExpression(\n            t.cloneNode(objId),\n            prop.key,\n            prop.computed || t.isLiteral(prop.key),\n          ),\n          getValue(prop),\n        ),\n      ),\n    );\n  }\n\n  function pushComputedPropsLoose(info: PropertyInfo) {\n    const { computedProps, state, initPropExpression, objId, body } = info;\n\n    for (const prop of computedProps) {\n      if (\n        t.isObjectMethod(prop) &&\n        (prop.kind === \"get\" || prop.kind === \"set\")\n      ) {\n        if (computedProps.length === 1) {\n          return buildDefineAccessor(state, initPropExpression, prop);\n        } else {\n          body.push(\n            t.expressionStatement(\n              buildDefineAccessor(state, t.cloneNode(objId), prop),\n            ),\n          );\n        }\n      } else {\n        pushAssign(t.cloneNode(objId), prop, body);\n      }\n    }\n  }\n\n  function pushComputedPropsSpec(info: PropertyInfo) {\n    const { objId, body, computedProps, state } = info;\n\n    // To prevent too deep AST structures in case of large objects\n    const CHUNK_LENGTH_CAP = 10;\n\n    let currentChunk: t.ObjectMember[] = null;\n    const computedPropsChunks: Array<t.ObjectMember[]> = [];\n    for (const prop of computedProps) {\n      if (!currentChunk || currentChunk.length === CHUNK_LENGTH_CAP) {\n        currentChunk = [];\n        computedPropsChunks.push(currentChunk);\n      }\n      currentChunk.push(prop);\n    }\n\n    for (const chunk of computedPropsChunks) {\n      const single = computedPropsChunks.length === 1;\n      let node: t.Expression = single\n        ? info.initPropExpression\n        : t.cloneNode(objId);\n      for (const prop of chunk) {\n        if (\n          t.isObjectMethod(prop) &&\n          (prop.kind === \"get\" || prop.kind === \"set\")\n        ) {\n          node = buildDefineAccessor(info.state, node, prop);\n        } else {\n          node = t.callExpression(state.addHelper(\"defineProperty\"), [\n            node,\n            // PrivateName must not be in ObjectExpression\n            t.toComputedKey(prop) as t.Expression,\n            // the value of ObjectProperty in ObjectExpression must be an expression\n            getValue(prop),\n          ]);\n        }\n      }\n      if (single) return node;\n      body.push(t.expressionStatement(node));\n    }\n  }\n\n  return {\n    name: \"transform-computed-properties\",\n\n    visitor: {\n      ObjectExpression: {\n        exit(path, state) {\n          const { node, parent, scope } = path;\n          let hasComputed = false;\n          for (const prop of node.properties) {\n            // @ts-expect-error SpreadElement must not have computed property\n            hasComputed = prop.computed === true;\n            if (hasComputed) break;\n          }\n          if (!hasComputed) return;\n\n          // put all getters/setters into the first object expression as well as all initialisers up\n          // to the first computed property\n\n          const initProps: t.ObjectMember[] = [];\n          const computedProps: t.ObjectMember[] = [];\n          let foundComputed = false;\n\n          for (const prop of node.properties) {\n            if (t.isSpreadElement(prop)) {\n              continue;\n            }\n            if (prop.computed) {\n              foundComputed = true;\n            }\n\n            if (foundComputed) {\n              computedProps.push(prop);\n            } else {\n              initProps.push(prop);\n            }\n          }\n\n          const objId = scope.generateUidIdentifierBasedOnNode(parent);\n          const initPropExpression = t.objectExpression(initProps);\n          const body = [];\n\n          body.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(objId, initPropExpression),\n            ]),\n          );\n\n          const single = pushComputedProps({\n            scope,\n            objId,\n            body,\n            computedProps,\n            initPropExpression,\n            state,\n          });\n\n          if (single) {\n            path.replaceWith(single);\n          } else {\n            if (setComputedProperties) {\n              body.push(t.expressionStatement(t.cloneNode(objId)));\n            }\n            path.replaceWithMultiple(body);\n          }\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAemC;EAEjC,IAAIG,oBAAoB,GAAGC,iBAAQ,CAACC,UAAU,CAACC,GAAG;AACpD;AACA;AACA;AACA;AACA;AACA,GAAG;EAEDH,oBAAoB,CAACI,QAAQ,GAAG,IAAI;AACtC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA;EAChDF,GAAG,CAACG,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,qBAAqB,IAAAF,eAAA,GACzBF,GAAG,CAACK,UAAU,CAAC,uBAAuB,CAAC,YAAAH,eAAA,GAAID,OAAO,CAACK,KAAK;EAE1D,MAAMC,iBAAiB,GAAGH,qBAAqB,GAC3CI,sBAAsB,GACtBC,qBAAqB;EAEzB,SAASC,mBAAmBA,CAC1BC,KAAiB,EACjBC,GAAiB,EACjBC,IAAoB,EACpB;IACA,MAAMC,IAAI,GAAGD,IAAI,CAACE,IAAqB;IACvC,MAAMC,GAAG,GACP,CAACH,IAAI,CAACI,QAAQ,IAAIC,WAAC,CAACC,YAAY,CAACN,IAAI,CAACG,GAAG,CAAC,GACtCE,WAAC,CAACE,aAAa,CAACP,IAAI,CAACG,GAAG,CAACK,IAAI,CAAC,GAC9BR,IAAI,CAACG,GAAG;IACd,MAAMM,EAAE,GAAGC,QAAQ,CAACV,IAAI,CAAC;IAQlB;MACL,IAAIW,MAAoB;MACxB,IAAIb,KAAK,CAACc,eAAe,CAAC,gBAAgB,CAAC,EAAE;QAC3CD,MAAM,GAAGb,KAAK,CAACe,SAAS,CAAC,gBAAgB,CAAC;MAC5C,CAAC,MAAM;QAEL,MAAMC,IAAI,GAAGhB,KAAK,CAACgB,IAAI;QACvBH,MAAM,GAAGG,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC;QACjD,IAAI,CAACJ,MAAM,EAAE;UACX,MAAMK,EAAE,GAAGF,IAAI,CAACG,KAAK,CAACC,qBAAqB,CAAC,gBAAgB,CAAC;UAC7DJ,IAAI,CAACG,KAAK,CAACE,IAAI,CAAC;YACdH,EAAE;YACFI,IAAI,EAAE1C;UACR,CAAC,CAAC;UACFoC,IAAI,CAACO,GAAG,CAAC,8BAA8B,EAAGV,MAAM,GAAGK,EAAG,CAAC;QACzD;QACAL,MAAM,GAAGN,WAAC,CAACiB,SAAS,CAACX,MAAM,CAAC;MAC9B;MAEA,OAAON,WAAC,CAACkB,cAAc,CAACZ,MAAM,EAAE,CAACN,WAAC,CAACE,aAAa,CAACN,IAAI,CAAC,EAAEF,GAAG,EAAEI,GAAG,EAAEM,EAAE,CAAC,CAAC;IACxE;EACF;EASA,SAASC,QAAQA,CAACV,IAAoB,EAAE;IACtC,IAAIK,WAAC,CAACmB,gBAAgB,CAACxB,IAAI,CAAC,EAAE;MAC5B,OAAOA,IAAI,CAACyB,KAAK;IACnB,CAAC,MAAM,IAAIpB,WAAC,CAACqB,cAAc,CAAC1B,IAAI,CAAC,EAAE;MACjC,OAAOK,WAAC,CAACsB,kBAAkB,CACzB,IAAI,EACJ3B,IAAI,CAAC4B,MAAM,EACX5B,IAAI,CAAC6B,IAAI,EACT7B,IAAI,CAAC8B,SAAS,EACd9B,IAAI,CAAC+B,KACP,CAAC;IACH;EACF;EAEA,SAASC,UAAUA,CACjBC,KAAmB,EACnBjC,IAAoB,EACpB6B,IAAmB,EACnB;IACAA,IAAI,CAACV,IAAI,CACPd,WAAC,CAAC6B,mBAAmB,CACnB7B,WAAC,CAAC8B,oBAAoB,CACpB,GAAG,EACH9B,WAAC,CAAC+B,gBAAgB,CAChB/B,WAAC,CAACiB,SAAS,CAACW,KAAK,CAAC,EAClBjC,IAAI,CAACG,GAAG,EACRH,IAAI,CAACI,QAAQ,IAAIC,WAAC,CAACgC,SAAS,CAACrC,IAAI,CAACG,GAAG,CACvC,CAAC,EACDO,QAAQ,CAACV,IAAI,CACf,CACF,CACF,CAAC;EACH;EAEA,SAASL,sBAAsBA,CAAC2C,IAAkB,EAAE;IAClD,MAAM;MAAEC,aAAa;MAAEzC,KAAK;MAAE0C,kBAAkB;MAAEP,KAAK;MAAEJ;IAAK,CAAC,GAAGS,IAAI;IAEtE,KAAK,MAAMtC,IAAI,IAAIuC,aAAa,EAAE;MAChC,IACElC,WAAC,CAACqB,cAAc,CAAC1B,IAAI,CAAC,KACrBA,IAAI,CAACE,IAAI,KAAK,KAAK,IAAIF,IAAI,CAACE,IAAI,KAAK,KAAK,CAAC,EAC5C;QACA,IAAIqC,aAAa,CAACE,MAAM,KAAK,CAAC,EAAE;UAC9B,OAAO5C,mBAAmB,CAACC,KAAK,EAAE0C,kBAAkB,EAAExC,IAAI,CAAC;QAC7D,CAAC,MAAM;UACL6B,IAAI,CAACV,IAAI,CACPd,WAAC,CAAC6B,mBAAmB,CACnBrC,mBAAmB,CAACC,KAAK,EAAEO,WAAC,CAACiB,SAAS,CAACW,KAAK,CAAC,EAAEjC,IAAI,CACrD,CACF,CAAC;QACH;MACF,CAAC,MAAM;QACLgC,UAAU,CAAC3B,WAAC,CAACiB,SAAS,CAACW,KAAK,CAAC,EAAEjC,IAAI,EAAE6B,IAAI,CAAC;MAC5C;IACF;EACF;EAEA,SAASjC,qBAAqBA,CAAC0C,IAAkB,EAAE;IACjD,MAAM;MAAEL,KAAK;MAAEJ,IAAI;MAAEU,aAAa;MAAEzC;IAAM,CAAC,GAAGwC,IAAI;IAGlD,MAAMI,gBAAgB,GAAG,EAAE;IAE3B,IAAIC,YAA8B,GAAG,IAAI;IACzC,MAAMC,mBAA4C,GAAG,EAAE;IACvD,KAAK,MAAM5C,IAAI,IAAIuC,aAAa,EAAE;MAChC,IAAI,CAACI,YAAY,IAAIA,YAAY,CAACF,MAAM,KAAKC,gBAAgB,EAAE;QAC7DC,YAAY,GAAG,EAAE;QACjBC,mBAAmB,CAACzB,IAAI,CAACwB,YAAY,CAAC;MACxC;MACAA,YAAY,CAACxB,IAAI,CAACnB,IAAI,CAAC;IACzB;IAEA,KAAK,MAAM6C,KAAK,IAAID,mBAAmB,EAAE;MACvC,MAAME,MAAM,GAAGF,mBAAmB,CAACH,MAAM,KAAK,CAAC;MAC/C,IAAIM,IAAkB,GAAGD,MAAM,GAC3BR,IAAI,CAACE,kBAAkB,GACvBnC,WAAC,CAACiB,SAAS,CAACW,KAAK,CAAC;MACtB,KAAK,MAAMjC,IAAI,IAAI6C,KAAK,EAAE;QACxB,IACExC,WAAC,CAACqB,cAAc,CAAC1B,IAAI,CAAC,KACrBA,IAAI,CAACE,IAAI,KAAK,KAAK,IAAIF,IAAI,CAACE,IAAI,KAAK,KAAK,CAAC,EAC5C;UACA6C,IAAI,GAAGlD,mBAAmB,CAACyC,IAAI,CAACxC,KAAK,EAAEiD,IAAI,EAAE/C,IAAI,CAAC;QACpD,CAAC,MAAM;UACL+C,IAAI,GAAG1C,WAAC,CAACkB,cAAc,CAACzB,KAAK,CAACe,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACzDkC,IAAI,EAEJ1C,WAAC,CAAC2C,aAAa,CAAChD,IAAI,CAAC,EAErBU,QAAQ,CAACV,IAAI,CAAC,CACf,CAAC;QACJ;MACF;MACA,IAAI8C,MAAM,EAAE,OAAOC,IAAI;MACvBlB,IAAI,CAACV,IAAI,CAACd,WAAC,CAAC6B,mBAAmB,CAACa,IAAI,CAAC,CAAC;IACxC;EACF;EAEA,OAAO;IACLvC,IAAI,EAAE,+BAA+B;IAErCyC,OAAO,EAAE;MACPC,gBAAgB,EAAE;QAChBC,IAAIA,CAACC,IAAI,EAAEtD,KAAK,EAAE;UAChB,MAAM;YAAEiD,IAAI;YAAEM,MAAM;YAAEpC;UAAM,CAAC,GAAGmC,IAAI;UACpC,IAAIE,WAAW,GAAG,KAAK;UACvB,KAAK,MAAMtD,IAAI,IAAI+C,IAAI,CAACQ,UAAU,EAAE;YAElCD,WAAW,GAAGtD,IAAI,CAACI,QAAQ,KAAK,IAAI;YACpC,IAAIkD,WAAW,EAAE;UACnB;UACA,IAAI,CAACA,WAAW,EAAE;UAKlB,MAAME,SAA2B,GAAG,EAAE;UACtC,MAAMjB,aAA+B,GAAG,EAAE;UAC1C,IAAIkB,aAAa,GAAG,KAAK;UAEzB,KAAK,MAAMzD,IAAI,IAAI+C,IAAI,CAACQ,UAAU,EAAE;YAClC,IAAIlD,WAAC,CAACqD,eAAe,CAAC1D,IAAI,CAAC,EAAE;cAC3B;YACF;YACA,IAAIA,IAAI,CAACI,QAAQ,EAAE;cACjBqD,aAAa,GAAG,IAAI;YACtB;YAEA,IAAIA,aAAa,EAAE;cACjBlB,aAAa,CAACpB,IAAI,CAACnB,IAAI,CAAC;YAC1B,CAAC,MAAM;cACLwD,SAAS,CAACrC,IAAI,CAACnB,IAAI,CAAC;YACtB;UACF;UAEA,MAAMiC,KAAK,GAAGhB,KAAK,CAAC0C,gCAAgC,CAACN,MAAM,CAAC;UAC5D,MAAMb,kBAAkB,GAAGnC,WAAC,CAACuD,gBAAgB,CAACJ,SAAS,CAAC;UACxD,MAAM3B,IAAI,GAAG,EAAE;UAEfA,IAAI,CAACV,IAAI,CACPd,WAAC,CAACwD,mBAAmB,CAAC,KAAK,EAAE,CAC3BxD,WAAC,CAACyD,kBAAkB,CAAC7B,KAAK,EAAEO,kBAAkB,CAAC,CAChD,CACH,CAAC;UAED,MAAMM,MAAM,GAAGpD,iBAAiB,CAAC;YAC/BuB,KAAK;YACLgB,KAAK;YACLJ,IAAI;YACJU,aAAa;YACbC,kBAAkB;YAClB1C;UACF,CAAC,CAAC;UAEF,IAAIgD,MAAM,EAAE;YACVM,IAAI,CAACW,WAAW,CAACjB,MAAM,CAAC;UAC1B,CAAC,MAAM;YACL,IAAIvD,qBAAqB,EAAE;cACzBsC,IAAI,CAACV,IAAI,CAACd,WAAC,CAAC6B,mBAAmB,CAAC7B,WAAC,CAACiB,SAAS,CAACW,KAAK,CAAC,CAAC,CAAC;YACtD;YACAmB,IAAI,CAACY,mBAAmB,CAACnC,IAAI,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}