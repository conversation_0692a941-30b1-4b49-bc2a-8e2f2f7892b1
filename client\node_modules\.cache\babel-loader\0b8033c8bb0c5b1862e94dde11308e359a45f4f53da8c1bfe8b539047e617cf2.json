{"ast": null, "code": "import React,{useState}from'react';import{Link}from'react-router-dom';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";function OrderCard(_ref){let{order,onReorder,onReview}=_ref;const[showDetails,setShowDetails]=useState(false);const getStatusColor=status=>{switch(status.toLowerCase()){case'delivered':return'#10b981';case'shipped':return'#3b82f6';case'processing':return'#f59e0b';case'cancelled':return'#ef4444';default:return'#6b7280';}};const getStatusIcon=status=>{switch(status.toLowerCase()){case'delivered':return'fas fa-check-circle';case'shipped':return'fas fa-truck';case'processing':return'fas fa-clock';case'cancelled':return'fas fa-times-circle';default:return'fas fa-question-circle';}};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric'});};return/*#__PURE__*/_jsxs(\"div\",{className:\"order-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"order-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"order-main-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"order-id-section\",children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"Order #\",order.id]}),/*#__PURE__*/_jsx(\"span\",{className:\"order-date\",children:formatDate(order.date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-status-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-badge\",style:{backgroundColor:getStatusColor(order.status)},children:[/*#__PURE__*/_jsx(\"i\",{className:getStatusIcon(order.status)}),order.status]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-total\",children:[\"$\",order.total.toFixed(2)]})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"toggle-details-btn\",onClick:()=>setShowDetails(!showDetails),children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-\".concat(showDetails?'up':'down')})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-items-preview\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"items-images\",children:[order.items.slice(0,3).map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"item-image\",children:[/*#__PURE__*/_jsx(\"img\",{src:item.imageUrl,alt:item.name}),item.quantity>1&&/*#__PURE__*/_jsx(\"span\",{className:\"quantity-badge\",children:item.quantity})]},index)),order.items.length>3&&/*#__PURE__*/_jsxs(\"div\",{className:\"more-items\",children:[\"+\",order.items.length-3]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"items-summary\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"items-count\",children:[order.items.length,\" item\",order.items.length!==1?'s':'']}),order.trackingNumber&&/*#__PURE__*/_jsxs(\"div\",{className:\"tracking-info\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Tracking: \",order.trackingNumber]})]})]})]}),showDetails&&/*#__PURE__*/_jsxs(\"div\",{className:\"order-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"order-timeline\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-item completed\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"timeline-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-shopping-cart\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-content\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Order Placed\"}),/*#__PURE__*/_jsx(\"span\",{children:formatDate(order.date)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-item \".concat(['shipped','delivered'].includes(order.status.toLowerCase())?'completed':''),children:[/*#__PURE__*/_jsx(\"div\",{className:\"timeline-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-content\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Shipped\"}),/*#__PURE__*/_jsx(\"span\",{children:order.status==='Shipped'||order.status==='Delivered'?'In transit':'Pending'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-item \".concat(order.status.toLowerCase()==='delivered'?'completed':''),children:[/*#__PURE__*/_jsx(\"div\",{className:\"timeline-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-home\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-content\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Delivered\"}),/*#__PURE__*/_jsx(\"span\",{children:order.status==='Delivered'?'Package delivered':'Estimated delivery'})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-items-detailed\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Items in this order:\"}),order.items.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"detailed-item\",children:[/*#__PURE__*/_jsx(\"img\",{src:item.imageUrl,alt:item.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-info\",children:[/*#__PURE__*/_jsx(\"h6\",{children:item.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"item-price\",children:[\"$\",item.price,\" \\xD7 \",item.quantity,\" = $\",(item.price*item.quantity).toFixed(2)]})]}),/*#__PURE__*/_jsx(Link,{to:\"/product/\".concat(item.id),className:\"view-product-btn\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-eye\"})})]},index))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shipping-info\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Shipping Address:\"}),/*#__PURE__*/_jsx(\"p\",{children:order.shippingAddress})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onReorder(order),className:\"action-btn secondary\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-redo\"}),\"Reorder\"]}),order.trackingNumber&&/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn secondary\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-truck\"}),\"Track Package\"]}),order.status==='Delivered'&&/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onReview(order),className:\"action-btn primary\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star\"}),\"Write Review\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn secondary\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-download\"}),\"Invoice\"]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .order-card {\\n          background: white;\\n          border-radius: 1rem;\\n          border: 1px solid var(--border-color);\\n          overflow: hidden;\\n          transition: all 0.3s ease;\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .order-card:hover {\\n          box-shadow: var(--shadow-lg);\\n          transform: translateY(-2px);\\n        }\\n\\n        .order-header {\\n          padding: 1.5rem;\\n          border-bottom: 1px solid var(--border-color);\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n        }\\n\\n        .order-main-info {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          flex: 1;\\n        }\\n\\n        .order-id-section h4 {\\n          margin: 0 0 0.25rem 0;\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .order-date {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .order-status-section {\\n          text-align: right;\\n        }\\n\\n        .status-badge {\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          padding: 0.5rem 1rem;\\n          border-radius: 9999px;\\n          color: white;\\n          font-size: 0.875rem;\\n          font-weight: 600;\\n          margin-bottom: 0.5rem;\\n        }\\n\\n        .order-total {\\n          font-size: 1.5rem;\\n          font-weight: 700;\\n          color: var(--text-primary);\\n        }\\n\\n        .toggle-details-btn {\\n          background: var(--bg-secondary);\\n          border: 1px solid var(--border-color);\\n          border-radius: 50%;\\n          width: 40px;\\n          height: 40px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          cursor: pointer;\\n          transition: all 0.2s;\\n          margin-left: 1rem;\\n        }\\n\\n        .toggle-details-btn:hover {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .order-items-preview {\\n          padding: 1.5rem;\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          background: var(--bg-secondary);\\n        }\\n\\n        .items-images {\\n          display: flex;\\n          gap: 0.5rem;\\n          align-items: center;\\n        }\\n\\n        .item-image {\\n          position: relative;\\n          width: 50px;\\n          height: 50px;\\n          border-radius: 0.5rem;\\n          overflow: hidden;\\n          border: 2px solid white;\\n          box-shadow: var(--shadow-sm);\\n        }\\n\\n        .item-image img {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n        }\\n\\n        .quantity-badge {\\n          position: absolute;\\n          top: -8px;\\n          right: -8px;\\n          background: var(--primary-color);\\n          color: white;\\n          border-radius: 50%;\\n          width: 20px;\\n          height: 20px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 0.75rem;\\n          font-weight: 600;\\n        }\\n\\n        .more-items {\\n          width: 50px;\\n          height: 50px;\\n          border-radius: 0.5rem;\\n          background: var(--text-secondary);\\n          color: white;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-size: 0.75rem;\\n          font-weight: 600;\\n        }\\n\\n        .items-summary {\\n          text-align: right;\\n        }\\n\\n        .items-count {\\n          display: block;\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n          margin-bottom: 0.25rem;\\n        }\\n\\n        .tracking-info {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          color: var(--primary-color);\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n        }\\n\\n        .order-details {\\n          padding: 1.5rem;\\n          border-top: 1px solid var(--border-color);\\n          background: #fafbfc;\\n        }\\n\\n        .order-timeline {\\n          display: flex;\\n          justify-content: space-between;\\n          margin-bottom: 2rem;\\n          position: relative;\\n        }\\n\\n        .order-timeline::before {\\n          content: '';\\n          position: absolute;\\n          top: 20px;\\n          left: 20px;\\n          right: 20px;\\n          height: 2px;\\n          background: var(--border-color);\\n          z-index: 1;\\n        }\\n\\n        .timeline-item {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          text-align: center;\\n          flex: 1;\\n          position: relative;\\n          z-index: 2;\\n        }\\n\\n        .timeline-icon {\\n          width: 40px;\\n          height: 40px;\\n          border-radius: 50%;\\n          background: var(--bg-secondary);\\n          border: 2px solid var(--border-color);\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          margin-bottom: 0.75rem;\\n          transition: all 0.3s ease;\\n        }\\n\\n        .timeline-item.completed .timeline-icon {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .timeline-content h5 {\\n          margin: 0 0 0.25rem 0;\\n          font-size: 0.875rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .timeline-content span {\\n          font-size: 0.75rem;\\n          color: var(--text-secondary);\\n        }\\n\\n        .order-items-detailed h5 {\\n          margin: 0 0 1rem 0;\\n          font-size: 1rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .detailed-item {\\n          display: flex;\\n          align-items: center;\\n          gap: 1rem;\\n          padding: 0.75rem;\\n          background: white;\\n          border-radius: 0.5rem;\\n          margin-bottom: 0.75rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .detailed-item img {\\n          width: 60px;\\n          height: 60px;\\n          object-fit: cover;\\n          border-radius: 0.5rem;\\n        }\\n\\n        .item-info {\\n          flex: 1;\\n        }\\n\\n        .item-info h6 {\\n          margin: 0 0 0.25rem 0;\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .item-price {\\n          font-size: 0.75rem;\\n          color: var(--text-secondary);\\n        }\\n\\n        .view-product-btn {\\n          background: var(--bg-secondary);\\n          border: 1px solid var(--border-color);\\n          border-radius: 0.375rem;\\n          width: 36px;\\n          height: 36px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          color: var(--text-secondary);\\n          text-decoration: none;\\n          transition: all 0.2s;\\n        }\\n\\n        .view-product-btn:hover {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .shipping-info {\\n          margin-top: 1.5rem;\\n          padding-top: 1.5rem;\\n          border-top: 1px solid var(--border-color);\\n        }\\n\\n        .shipping-info h5 {\\n          margin: 0 0 0.5rem 0;\\n          font-size: 0.875rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n        }\\n\\n        .shipping-info p {\\n          margin: 0;\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .order-actions {\\n          padding: 1rem 1.5rem;\\n          background: var(--bg-secondary);\\n          border-top: 1px solid var(--border-color);\\n          display: flex;\\n          gap: 0.75rem;\\n          flex-wrap: wrap;\\n        }\\n\\n        .action-btn {\\n          background: white;\\n          border: 1px solid var(--border-color);\\n          padding: 0.5rem 1rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-size: 0.875rem;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n          text-decoration: none;\\n          color: var(--text-primary);\\n        }\\n\\n        .action-btn:hover {\\n          background: var(--bg-secondary);\\n          transform: translateY(-1px);\\n        }\\n\\n        .action-btn.primary {\\n          background: var(--primary-color);\\n          color: white;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .action-btn.primary:hover {\\n          background: var(--primary-dark);\\n        }\\n\\n        @media (max-width: 768px) {\\n          .order-header {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: flex-start;\\n          }\\n\\n          .order-main-info {\\n            width: 100%;\\n          }\\n\\n          .order-items-preview {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: flex-start;\\n          }\\n\\n          .order-timeline {\\n            flex-direction: column;\\n            gap: 1rem;\\n          }\\n\\n          .order-timeline::before {\\n            display: none;\\n          }\\n\\n          .timeline-item {\\n            flex-direction: row;\\n            text-align: left;\\n          }\\n\\n          .timeline-icon {\\n            margin-bottom: 0;\\n            margin-right: 1rem;\\n          }\\n\\n          .order-actions {\\n            flex-direction: column;\\n          }\\n\\n          .action-btn {\\n            justify-content: center;\\n          }\\n        }\\n      \"})]});}export default OrderCard;", "map": {"version": 3, "names": ["React", "useState", "Link", "jsxs", "_jsxs", "jsx", "_jsx", "OrderCard", "_ref", "order", "onReorder", "onReview", "showDetails", "setShowDetails", "getStatusColor", "status", "toLowerCase", "getStatusIcon", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "id", "date", "style", "backgroundColor", "total", "toFixed", "onClick", "concat", "items", "slice", "map", "item", "index", "src", "imageUrl", "alt", "name", "quantity", "length", "trackingNumber", "includes", "price", "to", "shippingAddress"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/OrderCard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\n\nfunction OrderCard({ order, onReorder, onReview }) {\n  const [showDetails, setShowDetails] = useState(false);\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'delivered': return '#10b981';\n      case 'shipped': return '#3b82f6';\n      case 'processing': return '#f59e0b';\n      case 'cancelled': return '#ef4444';\n      default: return '#6b7280';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status.toLowerCase()) {\n      case 'delivered': return 'fas fa-check-circle';\n      case 'shipped': return 'fas fa-truck';\n      case 'processing': return 'fas fa-clock';\n      case 'cancelled': return 'fas fa-times-circle';\n      default: return 'fas fa-question-circle';\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <div className=\"order-card\">\n      <div className=\"order-header\">\n        <div className=\"order-main-info\">\n          <div className=\"order-id-section\">\n            <h4>Order #{order.id}</h4>\n            <span className=\"order-date\">{formatDate(order.date)}</span>\n          </div>\n          <div className=\"order-status-section\">\n            <div \n              className=\"status-badge\"\n              style={{ backgroundColor: getStatusColor(order.status) }}\n            >\n              <i className={getStatusIcon(order.status)}></i>\n              {order.status}\n            </div>\n            <div className=\"order-total\">${order.total.toFixed(2)}</div>\n          </div>\n        </div>\n        \n        <button \n          className=\"toggle-details-btn\"\n          onClick={() => setShowDetails(!showDetails)}\n        >\n          <i className={`fas fa-chevron-${showDetails ? 'up' : 'down'}`}></i>\n        </button>\n      </div>\n\n      <div className=\"order-items-preview\">\n        <div className=\"items-images\">\n          {order.items.slice(0, 3).map((item, index) => (\n            <div key={index} className=\"item-image\">\n              <img src={item.imageUrl} alt={item.name} />\n              {item.quantity > 1 && (\n                <span className=\"quantity-badge\">{item.quantity}</span>\n              )}\n            </div>\n          ))}\n          {order.items.length > 3 && (\n            <div className=\"more-items\">\n              +{order.items.length - 3}\n            </div>\n          )}\n        </div>\n        \n        <div className=\"items-summary\">\n          <span className=\"items-count\">\n            {order.items.length} item{order.items.length !== 1 ? 's' : ''}\n          </span>\n          {order.trackingNumber && (\n            <div className=\"tracking-info\">\n              <i className=\"fas fa-truck\"></i>\n              <span>Tracking: {order.trackingNumber}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {showDetails && (\n        <div className=\"order-details\">\n          <div className=\"order-timeline\">\n            <div className=\"timeline-item completed\">\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-shopping-cart\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Order Placed</h5>\n                <span>{formatDate(order.date)}</span>\n              </div>\n            </div>\n            \n            <div className={`timeline-item ${['shipped', 'delivered'].includes(order.status.toLowerCase()) ? 'completed' : ''}`}>\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-truck\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Shipped</h5>\n                <span>{order.status === 'Shipped' || order.status === 'Delivered' ? 'In transit' : 'Pending'}</span>\n              </div>\n            </div>\n            \n            <div className={`timeline-item ${order.status.toLowerCase() === 'delivered' ? 'completed' : ''}`}>\n              <div className=\"timeline-icon\">\n                <i className=\"fas fa-home\"></i>\n              </div>\n              <div className=\"timeline-content\">\n                <h5>Delivered</h5>\n                <span>{order.status === 'Delivered' ? 'Package delivered' : 'Estimated delivery'}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"order-items-detailed\">\n            <h5>Items in this order:</h5>\n            {order.items.map((item, index) => (\n              <div key={index} className=\"detailed-item\">\n                <img src={item.imageUrl} alt={item.name} />\n                <div className=\"item-info\">\n                  <h6>{item.name}</h6>\n                  <span className=\"item-price\">\n                    ${item.price} × {item.quantity} = ${(item.price * item.quantity).toFixed(2)}\n                  </span>\n                </div>\n                <Link to={`/product/${item.id}`} className=\"view-product-btn\">\n                  <i className=\"fas fa-eye\"></i>\n                </Link>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"shipping-info\">\n            <h5>Shipping Address:</h5>\n            <p>{order.shippingAddress}</p>\n          </div>\n        </div>\n      )}\n\n      <div className=\"order-actions\">\n        <button onClick={() => onReorder(order)} className=\"action-btn secondary\">\n          <i className=\"fas fa-redo\"></i>\n          Reorder\n        </button>\n        \n        {order.trackingNumber && (\n          <button className=\"action-btn secondary\">\n            <i className=\"fas fa-truck\"></i>\n            Track Package\n          </button>\n        )}\n        \n        {order.status === 'Delivered' && (\n          <button onClick={() => onReview(order)} className=\"action-btn primary\">\n            <i className=\"fas fa-star\"></i>\n            Write Review\n          </button>\n        )}\n        \n        <button className=\"action-btn secondary\">\n          <i className=\"fas fa-download\"></i>\n          Invoice\n        </button>\n      </div>\n\n      <style jsx>{`\n        .order-card {\n          background: white;\n          border-radius: 1rem;\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n          transition: all 0.3s ease;\n          margin-bottom: 1.5rem;\n        }\n\n        .order-card:hover {\n          box-shadow: var(--shadow-lg);\n          transform: translateY(-2px);\n        }\n\n        .order-header {\n          padding: 1.5rem;\n          border-bottom: 1px solid var(--border-color);\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .order-main-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          flex: 1;\n        }\n\n        .order-id-section h4 {\n          margin: 0 0 0.25rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .order-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-status-section {\n          text-align: right;\n        }\n\n        .status-badge {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.5rem 1rem;\n          border-radius: 9999px;\n          color: white;\n          font-size: 0.875rem;\n          font-weight: 600;\n          margin-bottom: 0.5rem;\n        }\n\n        .order-total {\n          font-size: 1.5rem;\n          font-weight: 700;\n          color: var(--text-primary);\n        }\n\n        .toggle-details-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 50%;\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-left: 1rem;\n        }\n\n        .toggle-details-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .order-items-preview {\n          padding: 1.5rem;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: var(--bg-secondary);\n        }\n\n        .items-images {\n          display: flex;\n          gap: 0.5rem;\n          align-items: center;\n        }\n\n        .item-image {\n          position: relative;\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          overflow: hidden;\n          border: 2px solid white;\n          box-shadow: var(--shadow-sm);\n        }\n\n        .item-image img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .quantity-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          width: 20px;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .more-items {\n          width: 50px;\n          height: 50px;\n          border-radius: 0.5rem;\n          background: var(--text-secondary);\n          color: white;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .items-summary {\n          text-align: right;\n        }\n\n        .items-count {\n          display: block;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          margin-bottom: 0.25rem;\n        }\n\n        .tracking-info {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          color: var(--primary-color);\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        .order-details {\n          padding: 1.5rem;\n          border-top: 1px solid var(--border-color);\n          background: #fafbfc;\n        }\n\n        .order-timeline {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 2rem;\n          position: relative;\n        }\n\n        .order-timeline::before {\n          content: '';\n          position: absolute;\n          top: 20px;\n          left: 20px;\n          right: 20px;\n          height: 2px;\n          background: var(--border-color);\n          z-index: 1;\n        }\n\n        .timeline-item {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          flex: 1;\n          position: relative;\n          z-index: 2;\n        }\n\n        .timeline-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: var(--bg-secondary);\n          border: 2px solid var(--border-color);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 0.75rem;\n          transition: all 0.3s ease;\n        }\n\n        .timeline-item.completed .timeline-icon {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .timeline-content h5 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .timeline-content span {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .order-items-detailed h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .detailed-item {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 0.75rem;\n          background: white;\n          border-radius: 0.5rem;\n          margin-bottom: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .detailed-item img {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .item-info {\n          flex: 1;\n        }\n\n        .item-info h6 {\n          margin: 0 0 0.25rem 0;\n          font-size: 0.875rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .item-price {\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .view-product-btn {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          border-radius: 0.375rem;\n          width: 36px;\n          height: 36px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: var(--text-secondary);\n          text-decoration: none;\n          transition: all 0.2s;\n        }\n\n        .view-product-btn:hover {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .shipping-info {\n          margin-top: 1.5rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .shipping-info h5 {\n          margin: 0 0 0.5rem 0;\n          font-size: 0.875rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .shipping-info p {\n          margin: 0;\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .order-actions {\n          padding: 1rem 1.5rem;\n          background: var(--bg-secondary);\n          border-top: 1px solid var(--border-color);\n          display: flex;\n          gap: 0.75rem;\n          flex-wrap: wrap;\n        }\n\n        .action-btn {\n          background: white;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem 1rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-size: 0.875rem;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n          text-decoration: none;\n          color: var(--text-primary);\n        }\n\n        .action-btn:hover {\n          background: var(--bg-secondary);\n          transform: translateY(-1px);\n        }\n\n        .action-btn.primary {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .action-btn.primary:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .order-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-main-info {\n            width: 100%;\n          }\n\n          .order-items-preview {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .order-timeline {\n            flex-direction: column;\n            gap: 1rem;\n          }\n\n          .order-timeline::before {\n            display: none;\n          }\n\n          .timeline-item {\n            flex-direction: row;\n            text-align: left;\n          }\n\n          .timeline-icon {\n            margin-bottom: 0;\n            margin-right: 1rem;\n          }\n\n          .order-actions {\n            flex-direction: column;\n          }\n\n          .action-btn {\n            justify-content: center;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default OrderCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAExC,QAAS,CAAAC,SAASA,CAAAC,IAAA,CAAiC,IAAhC,CAAEC,KAAK,CAAEC,SAAS,CAAEC,QAAS,CAAC,CAAAH,IAAA,CAC/C,KAAM,CAACI,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAAa,cAAc,CAAIC,MAAM,EAAK,CACjC,OAAQA,MAAM,CAACC,WAAW,CAAC,CAAC,EAC1B,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,YAAY,CAAE,MAAO,SAAS,CACnC,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIF,MAAM,EAAK,CAChC,OAAQA,MAAM,CAACC,WAAW,CAAC,CAAC,EAC1B,IAAK,WAAW,CAAE,MAAO,qBAAqB,CAC9C,IAAK,SAAS,CAAE,MAAO,cAAc,CACrC,IAAK,YAAY,CAAE,MAAO,cAAc,CACxC,IAAK,WAAW,CAAE,MAAO,qBAAqB,CAC9C,QAAS,MAAO,wBAAwB,CAC1C,CACF,CAAC,CAED,KAAM,CAAAE,UAAU,CAAIC,UAAU,EAAK,CACjC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,CAED,mBACEpB,KAAA,QAAKqB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBtB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtB,KAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtB,KAAA,QAAKqB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,KAAA,OAAAsB,QAAA,EAAI,SAAO,CAACjB,KAAK,CAACkB,EAAE,EAAK,CAAC,cAC1BrB,IAAA,SAAMmB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAER,UAAU,CAACT,KAAK,CAACmB,IAAI,CAAC,CAAO,CAAC,EACzD,CAAC,cACNxB,KAAA,QAAKqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCtB,KAAA,QACEqB,SAAS,CAAC,cAAc,CACxBI,KAAK,CAAE,CAAEC,eAAe,CAAEhB,cAAc,CAACL,KAAK,CAACM,MAAM,CAAE,CAAE,CAAAW,QAAA,eAEzDpB,IAAA,MAAGmB,SAAS,CAAER,aAAa,CAACR,KAAK,CAACM,MAAM,CAAE,CAAI,CAAC,CAC9CN,KAAK,CAACM,MAAM,EACV,CAAC,cACNX,KAAA,QAAKqB,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,GAAC,CAACjB,KAAK,CAACsB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,EACzD,CAAC,EACH,CAAC,cAEN1B,IAAA,WACEmB,SAAS,CAAC,oBAAoB,CAC9BQ,OAAO,CAAEA,CAAA,GAAMpB,cAAc,CAAC,CAACD,WAAW,CAAE,CAAAc,QAAA,cAE5CpB,IAAA,MAAGmB,SAAS,mBAAAS,MAAA,CAAoBtB,WAAW,CAAG,IAAI,CAAG,MAAM,CAAG,CAAI,CAAC,CAC7D,CAAC,EACN,CAAC,cAENR,KAAA,QAAKqB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCtB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BjB,KAAK,CAAC0B,KAAK,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACvCnC,KAAA,QAAiBqB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACrCpB,IAAA,QAAKkC,GAAG,CAAEF,IAAI,CAACG,QAAS,CAACC,GAAG,CAAEJ,IAAI,CAACK,IAAK,CAAE,CAAC,CAC1CL,IAAI,CAACM,QAAQ,CAAG,CAAC,eAChBtC,IAAA,SAAMmB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEY,IAAI,CAACM,QAAQ,CAAO,CACvD,GAJOL,KAKL,CACN,CAAC,CACD9B,KAAK,CAAC0B,KAAK,CAACU,MAAM,CAAG,CAAC,eACrBzC,KAAA,QAAKqB,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GACzB,CAACjB,KAAK,CAAC0B,KAAK,CAACU,MAAM,CAAG,CAAC,EACrB,CACN,EACE,CAAC,cAENzC,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtB,KAAA,SAAMqB,SAAS,CAAC,aAAa,CAAAC,QAAA,EAC1BjB,KAAK,CAAC0B,KAAK,CAACU,MAAM,CAAC,OAAK,CAACpC,KAAK,CAAC0B,KAAK,CAACU,MAAM,GAAK,CAAC,CAAG,GAAG,CAAG,EAAE,EACzD,CAAC,CACNpC,KAAK,CAACqC,cAAc,eACnB1C,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpB,IAAA,MAAGmB,SAAS,CAAC,cAAc,CAAI,CAAC,cAChCrB,KAAA,SAAAsB,QAAA,EAAM,YAAU,CAACjB,KAAK,CAACqC,cAAc,EAAO,CAAC,EAC1C,CACN,EACE,CAAC,EACH,CAAC,CAELlC,WAAW,eACVR,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtB,KAAA,QAAKqB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtB,KAAA,QAAKqB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCpB,IAAA,QAAKmB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BpB,IAAA,MAAGmB,SAAS,CAAC,sBAAsB,CAAI,CAAC,CACrC,CAAC,cACNrB,KAAA,QAAKqB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BpB,IAAA,OAAAoB,QAAA,CAAI,cAAY,CAAI,CAAC,cACrBpB,IAAA,SAAAoB,QAAA,CAAOR,UAAU,CAACT,KAAK,CAACmB,IAAI,CAAC,CAAO,CAAC,EAClC,CAAC,EACH,CAAC,cAENxB,KAAA,QAAKqB,SAAS,kBAAAS,MAAA,CAAmB,CAAC,SAAS,CAAE,WAAW,CAAC,CAACa,QAAQ,CAACtC,KAAK,CAACM,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAAG,WAAW,CAAG,EAAE,CAAG,CAAAU,QAAA,eAClHpB,IAAA,QAAKmB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BpB,IAAA,MAAGmB,SAAS,CAAC,cAAc,CAAI,CAAC,CAC7B,CAAC,cACNrB,KAAA,QAAKqB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BpB,IAAA,OAAAoB,QAAA,CAAI,SAAO,CAAI,CAAC,cAChBpB,IAAA,SAAAoB,QAAA,CAAOjB,KAAK,CAACM,MAAM,GAAK,SAAS,EAAIN,KAAK,CAACM,MAAM,GAAK,WAAW,CAAG,YAAY,CAAG,SAAS,CAAO,CAAC,EACjG,CAAC,EACH,CAAC,cAENX,KAAA,QAAKqB,SAAS,kBAAAS,MAAA,CAAmBzB,KAAK,CAACM,MAAM,CAACC,WAAW,CAAC,CAAC,GAAK,WAAW,CAAG,WAAW,CAAG,EAAE,CAAG,CAAAU,QAAA,eAC/FpB,IAAA,QAAKmB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BpB,IAAA,MAAGmB,SAAS,CAAC,aAAa,CAAI,CAAC,CAC5B,CAAC,cACNrB,KAAA,QAAKqB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BpB,IAAA,OAAAoB,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBpB,IAAA,SAAAoB,QAAA,CAAOjB,KAAK,CAACM,MAAM,GAAK,WAAW,CAAG,mBAAmB,CAAG,oBAAoB,CAAO,CAAC,EACrF,CAAC,EACH,CAAC,EACH,CAAC,cAENX,KAAA,QAAKqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpB,IAAA,OAAAoB,QAAA,CAAI,sBAAoB,CAAI,CAAC,CAC5BjB,KAAK,CAAC0B,KAAK,CAACE,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC3BnC,KAAA,QAAiBqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eACxCpB,IAAA,QAAKkC,GAAG,CAAEF,IAAI,CAACG,QAAS,CAACC,GAAG,CAAEJ,IAAI,CAACK,IAAK,CAAE,CAAC,cAC3CvC,KAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,IAAA,OAAAoB,QAAA,CAAKY,IAAI,CAACK,IAAI,CAAK,CAAC,cACpBvC,KAAA,SAAMqB,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GAC1B,CAACY,IAAI,CAACU,KAAK,CAAC,QAAG,CAACV,IAAI,CAACM,QAAQ,CAAC,MAAI,CAAC,CAACN,IAAI,CAACU,KAAK,CAAGV,IAAI,CAACM,QAAQ,EAAEZ,OAAO,CAAC,CAAC,CAAC,EACvE,CAAC,EACJ,CAAC,cACN1B,IAAA,CAACJ,IAAI,EAAC+C,EAAE,aAAAf,MAAA,CAAcI,IAAI,CAACX,EAAE,CAAG,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC3DpB,IAAA,MAAGmB,SAAS,CAAC,YAAY,CAAI,CAAC,CAC1B,CAAC,GAVCc,KAWL,CACN,CAAC,EACC,CAAC,cAENnC,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpB,IAAA,OAAAoB,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BpB,IAAA,MAAAoB,QAAA,CAAIjB,KAAK,CAACyC,eAAe,CAAI,CAAC,EAC3B,CAAC,EACH,CACN,cAED9C,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtB,KAAA,WAAQ6B,OAAO,CAAEA,CAAA,GAAMvB,SAAS,CAACD,KAAK,CAAE,CAACgB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACvEpB,IAAA,MAAGmB,SAAS,CAAC,aAAa,CAAI,CAAC,UAEjC,EAAQ,CAAC,CAERhB,KAAK,CAACqC,cAAc,eACnB1C,KAAA,WAAQqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACtCpB,IAAA,MAAGmB,SAAS,CAAC,cAAc,CAAI,CAAC,gBAElC,EAAQ,CACT,CAEAhB,KAAK,CAACM,MAAM,GAAK,WAAW,eAC3BX,KAAA,WAAQ6B,OAAO,CAAEA,CAAA,GAAMtB,QAAQ,CAACF,KAAK,CAAE,CAACgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACpEpB,IAAA,MAAGmB,SAAS,CAAC,aAAa,CAAI,CAAC,eAEjC,EAAQ,CACT,cAEDrB,KAAA,WAAQqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACtCpB,IAAA,MAAGmB,SAAS,CAAC,iBAAiB,CAAI,CAAC,UAErC,EAAQ,CAAC,EACN,CAAC,cAENnB,IAAA,UAAOD,GAAG,MAAAqB,QAAA,2nTAwYD,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAnB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}