const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// In-memory data store for demo purposes
global.products = [
  {
    _id: '1',
    name: "Eco-Friendly Water Bottle",
    description: "Reusable stainless steel water bottle that keeps drinks cold for 24 hours and hot for 12 hours. Perfect for daily hydration and reducing plastic waste.",
    imageUrl: "https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400",
    price: 2499,
    tags: ["eco-friendly", "reusable", "stainless steel", "hydration"],
    isEcoFriendly: true
  },
  {
    _id: '2',
    name: "Organic Cotton T-Shirt",
    description: "Soft, comfortable t-shirt made from 100% organic cotton. Available in multiple colors. Ethically sourced and fair trade certified.",
    imageUrl: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400",
    price: 1999,
    tags: ["organic", "cotton", "clothing", "fair trade"],
    isEcoFriendly: true
  },
  {
    _id: '3',
    name: "Bamboo Phone Case",
    description: "Protective phone case made from sustainable bamboo. Compatible with most phone models. Naturally antimicrobial and biodegradable.",
    imageUrl: "https://images.unsplash.com/photo-**********-08538906a9f8?w=400",
    price: 1599,
    tags: ["bamboo", "phone case", "sustainable", "biodegradable"],
    isEcoFriendly: true
  },
  {
    _id: '4',
    name: "Solar Power Bank",
    description: "Portable solar-powered charger for your devices. Perfect for outdoor adventures. 20,000mAh capacity with fast charging technology.",
    imageUrl: "https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=400",
    price: 4199,
    tags: ["solar", "power bank", "portable", "outdoor"],
    isEcoFriendly: true
  },
  {
    _id: '5',
    name: "Recycled Notebook",
    description: "High-quality notebook made from 100% recycled paper. Perfect for journaling or note-taking. Includes plantable seed paper bookmark.",
    imageUrl: "https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=400",
    price: 999,
    tags: ["recycled", "notebook", "paper", "plantable"],
    isEcoFriendly: true
  },
  {
    _id: '6',
    name: "LED Desk Lamp",
    description: "Energy-efficient LED desk lamp with adjustable brightness and color temperature. USB charging port included for convenience.",
    imageUrl: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=400",
    price: 3299,
    tags: ["LED", "desk lamp", "energy efficient", "USB"],
    isEcoFriendly: false
  },
  {
    _id: '7',
    name: "Reusable Food Wraps",
    description: "Set of 3 beeswax food wraps in different sizes. Perfect alternative to plastic wrap. Keeps food fresh naturally.",
    imageUrl: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400",
    price: 1549,
    tags: ["beeswax", "food wrap", "reusable", "kitchen"],
    isEcoFriendly: true
  },
  {
    _id: '8',
    name: "Bamboo Toothbrush Set",
    description: "Pack of 4 biodegradable bamboo toothbrushes with soft bristles. Plastic-free oral care for the whole family.",
    imageUrl: "https://images.unsplash.com/photo-1607613009820-a29f7bb81c04?w=400",
    price: 1299,
    tags: ["bamboo", "toothbrush", "biodegradable", "oral care"],
    isEcoFriendly: true
  },
  {
    _id: '9',
    name: "Organic Hemp Backpack",
    description: "Durable backpack made from organic hemp fiber. Water-resistant and perfect for daily commute or hiking adventures.",
    imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400",
    price: 6599,
    tags: ["hemp", "backpack", "organic", "water-resistant"],
    isEcoFriendly: true
  },
  {
    _id: '10',
    name: "Stainless Steel Lunch Box",
    description: "Leak-proof stainless steel lunch container with compartments. BPA-free and dishwasher safe. Perfect for meal prep.",
    imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
    price: 2899,
    tags: ["stainless steel", "lunch box", "BPA-free", "meal prep"],
    isEcoFriendly: true
  },
  {
    _id: '11',
    name: "Eco-Friendly Yoga Mat",
    description: "Non-toxic yoga mat made from natural rubber and cork. Provides excellent grip and cushioning for all yoga practices.",
    imageUrl: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400",
    price: 5699,
    tags: ["yoga", "natural rubber", "cork", "non-toxic"],
    isEcoFriendly: true
  },
  {
    _id: '12',
    name: "Seed Starter Kit",
    description: "Complete kit for growing your own herbs and vegetables. Includes organic seeds, biodegradable pots, and growing guide.",
    imageUrl: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400",
    price: 1899,
    tags: ["seeds", "gardening", "organic", "herbs"],
    isEcoFriendly: true
  },
  {
    _id: '13',
    name: "Wireless Charging Pad",
    description: "Fast wireless charging pad compatible with all Qi-enabled devices. Made with recycled materials and energy-efficient design.",
    imageUrl: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400",
    price: 3799,
    tags: ["wireless charging", "recycled materials", "Qi-enabled", "fast charging"],
    isEcoFriendly: true
  },
  {
    _id: '14',
    name: "Compostable Phone Case",
    description: "Fully compostable phone case made from plant-based materials. Breaks down naturally in 6 months when composted.",
    imageUrl: "https://images.unsplash.com/photo-1601593346740-************?w=400",
    price: 2299,
    tags: ["compostable", "plant-based", "phone case", "biodegradable"],
    isEcoFriendly: true
  },
  {
    _id: '15',
    name: "Sustainable Sneakers",
    description: "Comfortable sneakers made from recycled ocean plastic and organic cotton. Stylish and environmentally conscious footwear.",
    imageUrl: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400",
    price: 7499,
    tags: ["sneakers", "recycled plastic", "ocean plastic", "footwear"],
    isEcoFriendly: true
  },
  {
    _id: '16',
    name: "Glass Water Bottles Set",
    description: "Set of 2 borosilicate glass water bottles with silicone sleeves. Heat-resistant and perfect for hot or cold beverages.",
    imageUrl: "https://images.unsplash.com/photo-1523362628745-0c100150b504?w=400",
    price: 3549,
    tags: ["glass", "water bottle", "borosilicate", "heat-resistant"],
    isEcoFriendly: true
  },
  {
    _id: '17',
    name: "Organic Cotton Tote Bag",
    description: "Spacious tote bag made from certified organic cotton. Perfect for grocery shopping and daily errands. Machine washable.",
    imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400",
    price: 1399,
    tags: ["organic cotton", "tote bag", "shopping", "machine washable"],
    isEcoFriendly: true
  },
  {
    _id: '18',
    name: "Solar Garden Lights",
    description: "Set of 6 solar-powered LED garden lights. Automatic on/off with dusk sensor. Weather-resistant and easy installation.",
    imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    price: 2999,
    tags: ["solar", "garden lights", "LED", "weather-resistant"],
    isEcoFriendly: true
  },
  {
    _id: '19',
    name: "Bamboo Cutting Board Set",
    description: "Set of 3 bamboo cutting boards in different sizes. Naturally antimicrobial and knife-friendly. Includes hanging loops.",
    imageUrl: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400",
    price: 2399,
    tags: ["bamboo", "cutting board", "antimicrobial", "kitchen"],
    isEcoFriendly: true
  },
  {
    _id: '20',
    name: "Eco-Friendly Cleaning Kit",
    description: "Complete cleaning kit with plant-based cleaners and reusable microfiber cloths. Safe for family and pets.",
    imageUrl: "https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400",
    price: 2649,
    tags: ["cleaning", "plant-based", "microfiber", "family-safe"],
    isEcoFriendly: true
  },
  // New products added
  {
    _id: '21',
    name: "Copper Water Bottle",
    description: "Traditional copper water bottle with Ayurvedic benefits. Naturally antimicrobial and improves water quality. Handcrafted design.",
    imageUrl: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
    price: 1899,
    tags: ["copper", "ayurvedic", "antimicrobial", "traditional"],
    isEcoFriendly: true
  },
  {
    _id: '22',
    name: "Jute Shopping Bags Set",
    description: "Set of 3 jute shopping bags in different sizes. Strong, durable, and completely biodegradable. Perfect for Indian markets.",
    imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400",
    price: 899,
    tags: ["jute", "shopping bags", "biodegradable", "indian"],
    isEcoFriendly: true
  },
  {
    _id: '23',
    name: "Khadi Cotton Kurta",
    description: "Handwoven khadi cotton kurta supporting local artisans. Breathable, comfortable, and perfect for Indian climate.",
    imageUrl: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400",
    price: 2499,
    tags: ["khadi", "cotton", "handwoven", "artisan"],
    isEcoFriendly: true
  },
  {
    _id: '24',
    name: "Neem Wood Comb",
    description: "Natural neem wood comb with anti-dandruff properties. Gentle on hair and scalp. Traditional Indian hair care.",
    imageUrl: "https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=400",
    price: 399,
    tags: ["neem", "wood", "comb", "anti-dandruff"],
    isEcoFriendly: true
  },
  {
    _id: '25',
    name: "Terracotta Water Cooler",
    description: "Traditional clay water cooler that naturally cools water. Eco-friendly alternative to plastic coolers. Handmade by Indian potters.",
    imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
    price: 1599,
    tags: ["terracotta", "clay", "water cooler", "traditional"],
    isEcoFriendly: true
  },
  {
    _id: '26',
    name: "Organic Turmeric Soap",
    description: "Handmade soap with organic turmeric and neem. Natural antibacterial properties. Chemical-free skincare.",
    imageUrl: "https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400",
    price: 299,
    tags: ["turmeric", "neem", "organic", "antibacterial"],
    isEcoFriendly: true
  },
  {
    _id: '27',
    name: "Bamboo Laptop Stand",
    description: "Ergonomic laptop stand made from sustainable bamboo. Adjustable height and angle. Perfect for work from home setup.",
    imageUrl: "https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400",
    price: 2199,
    tags: ["bamboo", "laptop stand", "ergonomic", "work from home"],
    isEcoFriendly: true
  },
  {
    _id: '28',
    name: "Coconut Coir Doormat",
    description: "Natural coconut coir doormat with beautiful patterns. Biodegradable and durable. Supports coconut farmers.",
    imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    price: 799,
    tags: ["coconut", "coir", "doormat", "biodegradable"],
    isEcoFriendly: true
  },
  {
    _id: '29',
    name: "Handloom Cotton Bedsheet",
    description: "Pure cotton bedsheet woven on traditional handlooms. Soft, breathable, and supports rural weavers. Available in multiple colors.",
    imageUrl: "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400",
    price: 3499,
    tags: ["handloom", "cotton", "bedsheet", "traditional"],
    isEcoFriendly: true
  },
  {
    _id: '30',
    name: "Brass Dinner Set",
    description: "Traditional brass dinner set with plates, bowls, and glasses. Naturally antimicrobial and adds elegance to dining.",
    imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
    price: 4999,
    tags: ["brass", "dinner set", "antimicrobial", "traditional"],
    isEcoFriendly: true
  }
];

global.users = [];
global.orders = [];

console.log('Using in-memory data store for demo purposes');

// Routes
const productRoutes = require('./routes/productRoutes');
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');
const orderRoutes = require('./routes/orderRoutes');

// API Routes
app.use('/api/products', productRoutes);
app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/orders', orderRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API endpoints available at http://localhost:${PORT}/api/`);
}).on('error', (err) => {
  console.error('Server failed to start:', err);
});