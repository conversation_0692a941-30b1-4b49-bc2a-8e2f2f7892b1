import { formatCurrency } from './currency';

// Generate and download invoice PDF
export const downloadInvoice = (order) => {
  // Create invoice content
  const invoiceContent = generateInvoiceHTML(order);
  
  // Create a new window for printing
  const printWindow = window.open('', '_blank');
  printWindow.document.write(invoiceContent);
  printWindow.document.close();
  
  // Wait for content to load then print
  printWindow.onload = () => {
    printWindow.print();
    // Close window after printing (optional)
    setTimeout(() => {
      printWindow.close();
    }, 1000);
  };
};

// Generate invoice HTML content
const generateInvoiceHTML = (order) => {
  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = subtotal > 4000 ? 0 : 199;
  const gst = subtotal * 0.18;
  const total = subtotal + shipping + gst;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Invoice - Order #${order.id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          color: #333;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .company-name {
          font-size: 28px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 5px;
        }
        .invoice-title {
          font-size: 24px;
          margin: 20px 0;
        }
        .order-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .info-section {
          flex: 1;
        }
        .info-section h3 {
          color: #2563eb;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #e5e7eb;
          padding: 12px;
          text-align: left;
        }
        .items-table th {
          background-color: #f3f4f6;
          font-weight: bold;
        }
        .total-section {
          margin-top: 30px;
          text-align: right;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          border-bottom: 1px solid #e5e7eb;
        }
        .total-row.final {
          font-weight: bold;
          font-size: 18px;
          border-bottom: 2px solid #2563eb;
          color: #2563eb;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #6b7280;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
          color: white;
          background-color: ${getStatusColor(order.status)};
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-name">🌱 EcoStore</div>
        <div>Sustainable Shopping for a Better Tomorrow</div>
        <div class="invoice-title">INVOICE</div>
      </div>

      <div class="order-info">
        <div class="info-section">
          <h3>Order Details</h3>
          <p><strong>Order ID:</strong> ${order.id}</p>
          <p><strong>Order Date:</strong> ${new Date(order.date).toLocaleDateString('en-IN')}</p>
          <p><strong>Status:</strong> <span class="status-badge">${order.status}</span></p>
          ${order.trackingNumber ? `<p><strong>Tracking:</strong> ${order.trackingNumber}</p>` : ''}
        </div>
        
        <div class="info-section">
          <h3>Shipping Address</h3>
          <p>${order.shippingAddress}</p>
        </div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>${formatCurrency(item.price)}</td>
              <td>${formatCurrency(item.price * item.quantity)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="total-section">
        <div class="total-row">
          <span>Subtotal:</span>
          <span>${formatCurrency(subtotal)}</span>
        </div>
        <div class="total-row">
          <span>Shipping:</span>
          <span>${shipping === 0 ? 'FREE' : formatCurrency(shipping)}</span>
        </div>
        <div class="total-row">
          <span>GST (18%):</span>
          <span>${formatCurrency(gst)}</span>
        </div>
        <div class="total-row final">
          <span>Total Amount:</span>
          <span>${formatCurrency(total)}</span>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing EcoStore!</p>
        <p>For any queries, contact <NAME_EMAIL> | +91-1234567890</p>
        <p>This is a computer-generated invoice.</p>
      </div>
    </body>
    </html>
  `;
};

// Get status color for invoice
const getStatusColor = (status) => {
  switch (status.toLowerCase()) {
    case 'delivered': return '#10b981';
    case 'shipped': return '#3b82f6';
    case 'processing': return '#f59e0b';
    case 'cancelled': return '#ef4444';
    default: return '#6b7280';
  }
};

// Open order tracking modal/page
export const trackOrder = (order) => {
  if (!order.trackingNumber) {
    alert('Tracking number not available for this order yet.');
    return;
  }

  // Create tracking modal content
  const trackingContent = generateTrackingHTML(order);
  
  // Create modal window
  const trackingWindow = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');
  trackingWindow.document.write(trackingContent);
  trackingWindow.document.close();
};

// Generate tracking HTML content
const generateTrackingHTML = (order) => {
  const trackingEvents = generateTrackingEvents(order);
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Track Order #${order.id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f9fafb;
        }
        .tracking-header {
          background: linear-gradient(135deg, #2563eb, #1d4ed8);
          color: white;
          padding: 20px;
          border-radius: 10px;
          text-align: center;
          margin-bottom: 20px;
        }
        .tracking-number {
          font-size: 18px;
          font-weight: bold;
          margin-top: 10px;
        }
        .timeline {
          background: white;
          border-radius: 10px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .timeline-item {
          display: flex;
          margin-bottom: 20px;
          position: relative;
        }
        .timeline-item:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 20px;
          top: 40px;
          width: 2px;
          height: 40px;
          background: #e5e7eb;
        }
        .timeline-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          flex-shrink: 0;
        }
        .timeline-icon.completed {
          background: #10b981;
          color: white;
        }
        .timeline-icon.current {
          background: #3b82f6;
          color: white;
        }
        .timeline-icon.pending {
          background: #e5e7eb;
          color: #6b7280;
        }
        .timeline-content h4 {
          margin: 0 0 5px 0;
          color: #1f2937;
        }
        .timeline-content p {
          margin: 0;
          color: #6b7280;
          font-size: 14px;
        }
        .estimated-delivery {
          background: #eff6ff;
          border: 1px solid #bfdbfe;
          border-radius: 8px;
          padding: 15px;
          margin-top: 20px;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="tracking-header">
        <h2>Order Tracking</h2>
        <div>Order #${order.id}</div>
        <div class="tracking-number">Tracking: ${order.trackingNumber}</div>
      </div>

      <div class="timeline">
        ${trackingEvents.map(event => `
          <div class="timeline-item">
            <div class="timeline-icon ${event.status}">
              <i class="fas ${event.icon}"></i>
            </div>
            <div class="timeline-content">
              <h4>${event.title}</h4>
              <p>${event.description}</p>
              <p><strong>${event.date}</strong></p>
            </div>
          </div>
        `).join('')}
      </div>

      ${order.status !== 'Delivered' ? `
        <div class="estimated-delivery">
          <strong>Estimated Delivery:</strong> ${getEstimatedDelivery(order)}
        </div>
      ` : ''}

      <script>
        // Add Font Awesome for icons
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(link);
      </script>
    </body>
    </html>
  `;
};

// Generate tracking events based on order status
const generateTrackingEvents = (order) => {
  const orderDate = new Date(order.date);
  const events = [
    {
      title: 'Order Placed',
      description: 'Your order has been successfully placed',
      date: orderDate.toLocaleDateString('en-IN'),
      icon: 'fa-shopping-cart',
      status: 'completed'
    }
  ];

  if (['Shipped', 'Delivered'].includes(order.status)) {
    const shippedDate = new Date(orderDate.getTime() + 24 * 60 * 60 * 1000);
    events.push({
      title: 'Order Shipped',
      description: 'Your order is on its way',
      date: shippedDate.toLocaleDateString('en-IN'),
      icon: 'fa-truck',
      status: order.status === 'Delivered' ? 'completed' : 'current'
    });
  }

  if (order.status === 'Delivered') {
    const deliveredDate = new Date(orderDate.getTime() + 3 * 24 * 60 * 60 * 1000);
    events.push({
      title: 'Delivered',
      description: 'Package delivered successfully',
      date: deliveredDate.toLocaleDateString('en-IN'),
      icon: 'fa-home',
      status: 'completed'
    });
  } else if (order.status === 'Processing') {
    events.push({
      title: 'Processing',
      description: 'Your order is being prepared',
      date: 'In progress',
      icon: 'fa-cog',
      status: 'current'
    });
  }

  return events;
};

// Get estimated delivery date
const getEstimatedDelivery = (order) => {
  const orderDate = new Date(order.date);
  const estimatedDate = new Date(orderDate.getTime() + 5 * 24 * 60 * 60 * 1000);
  return estimatedDate.toLocaleDateString('en-IN');
};
