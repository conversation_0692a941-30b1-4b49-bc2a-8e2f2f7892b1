{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\pages\\\\AdminPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminPage() {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    imageUrl: '',\n    price: '',\n    tags: '',\n    isEcoFriendly: false\n  });\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)\n      };\n      if (editingProduct) {\n        // Update existing product (simulated)\n        const updatedProducts = products.map(p => p._id === editingProduct._id ? {\n          ...p,\n          ...productData\n        } : p);\n        setProducts(updatedProducts);\n      } else {\n        // Add new product (simulated)\n        const newProduct = {\n          _id: Date.now().toString(),\n          ...productData\n        };\n        setProducts([newProduct, ...products]);\n      }\n      resetForm();\n    } catch (err) {\n      setError('Failed to save product');\n    }\n  };\n  const handleEdit = product => {\n    var _product$tags;\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description,\n      imageUrl: product.imageUrl,\n      price: product.price.toString(),\n      tags: ((_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.join(', ')) || '',\n      isEcoFriendly: product.isEcoFriendly\n    });\n    setShowAddForm(true);\n  };\n  const handleDelete = productId => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      setProducts(products.filter(p => p._id !== productId));\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      imageUrl: '',\n      price: '',\n      tags: '',\n      isEcoFriendly: false\n    });\n    setEditingProduct(null);\n    setShowAddForm(false);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      size: \"large\",\n      message: \"Loading admin panel...\",\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      title: \"Admin Panel Error\",\n      message: error,\n      onRetry: fetchProducts\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-cog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), \"Product Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(true),\n          className: \"add-product-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-plus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), \"Add New Product\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-form-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: editingProduct ? 'Edit Product' : 'Add New Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetForm,\n              className: \"close-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-times\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"product-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"name\",\n                  children: \"Product Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"price\",\n                  children: \"Price *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"price\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"imageUrl\",\n                children: \"Image URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                id: \"imageUrl\",\n                name: \"imageUrl\",\n                value: formData.imageUrl,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"tags\",\n                children: \"Tags (comma-separated)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"tags\",\n                name: \"tags\",\n                value: formData.tags,\n                onChange: handleInputChange,\n                placeholder: \"eco-friendly, sustainable, organic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"checkbox-label\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"isEcoFriendly\",\n                  checked: formData.isEcoFriendly,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"checkmark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), \"Eco-Friendly Product\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: resetForm,\n                className: \"cancel-btn\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"save-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-save\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), editingProduct ? 'Update Product' : 'Add Product']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"All Products (\", products.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Eco-Friendly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Tags\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: products.map(product => {\n                var _product$description, _product$tags2, _product$tags3;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: product.imageUrl,\n                      alt: product.name,\n                      className: \"product-thumbnail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-name\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-description\",\n                      children: [(_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.substring(0, 50), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"price-cell\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: product.isEcoFriendly ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"eco-badge\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-leaf\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 27\n                      }, this), \"Yes\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"not-eco\",\n                      children: \"No\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"tags-cell\",\n                      children: [(_product$tags2 = product.tags) === null || _product$tags2 === void 0 ? void 0 : _product$tags2.slice(0, 2).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"tag\",\n                        children: tag\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 278,\n                        columnNumber: 27\n                      }, this)), ((_product$tags3 = product.tags) === null || _product$tags3 === void 0 ? void 0 : _product$tags3.length) > 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"tag-more\",\n                        children: [\"+\", product.tags.length - 2]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleEdit(product),\n                        className: \"edit-btn\",\n                        title: \"Edit product\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-edit\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 292,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 287,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleDelete(product._id),\n                        className: \"delete-btn\",\n                        title: \"Delete product\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-trash\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 299,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)]\n                }, product._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .admin-container {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .admin-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 2px solid var(--border-color);\n        }\n\n        .admin-header h1 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0;\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .add-product-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .add-product-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .product-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .product-form {\n          padding: 2rem;\n        }\n\n        .form-row {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 1rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input,\n        .form-group textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus,\n        .form-group textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .checkbox-label {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          cursor: pointer;\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n          margin-top: 2rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .products-table {\n          background: white;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n        }\n\n        .table-header {\n          padding: 1.5rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .table-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .table-container {\n          overflow-x: auto;\n        }\n\n        table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        th, td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        th {\n          background: var(--bg-secondary);\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .product-thumbnail {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .product-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          margin-bottom: 0.25rem;\n        }\n\n        .product-description {\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .price-cell {\n          font-weight: 600;\n          color: var(--primary-color);\n          font-size: 1.125rem;\n        }\n\n        .eco-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          display: inline-flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .not-eco {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .tags-cell {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.25rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .tag-more {\n          background: var(--text-secondary);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .edit-btn, .delete-btn {\n          background: none;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .edit-btn {\n          color: var(--primary-color);\n        }\n\n        .edit-btn:hover {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .delete-btn {\n          color: #ef4444;\n        }\n\n        .delete-btn:hover {\n          background: #ef4444;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .admin-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .form-row {\n            grid-template-columns: 1fr;\n          }\n\n          .table-container {\n            font-size: 0.875rem;\n          }\n\n          th, td {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminPage, \"BrVuPr819nydYhpfVw5Wzom2StM=\");\n_c = AdminPage;\nexport default AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "AdminPage", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "showAddForm", "setShowAddForm", "editingProduct", "setEditingProduct", "formData", "setFormData", "name", "description", "imageUrl", "price", "tags", "isEcoFriendly", "fetchProducts", "response", "get", "data", "err", "handleInputChange", "e", "value", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "productData", "parseFloat", "split", "map", "tag", "trim", "filter", "updatedProducts", "p", "_id", "newProduct", "Date", "now", "toString", "resetForm", "handleEdit", "product", "_product$tags", "join", "handleDelete", "productId", "window", "confirm", "size", "message", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onRetry", "children", "className", "onClick", "onSubmit", "htmlFor", "id", "onChange", "required", "step", "min", "rows", "placeholder", "length", "_product$description", "_product$tags2", "_product$tags3", "src", "alt", "substring", "slice", "index", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/pages/AdminPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nfunction AdminPage() {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    imageUrl: '',\n    price: '',\n    tags: '',\n    isEcoFriendly: false\n  });\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)\n      };\n\n      if (editingProduct) {\n        // Update existing product (simulated)\n        const updatedProducts = products.map(p => \n          p._id === editingProduct._id ? { ...p, ...productData } : p\n        );\n        setProducts(updatedProducts);\n      } else {\n        // Add new product (simulated)\n        const newProduct = {\n          _id: Date.now().toString(),\n          ...productData\n        };\n        setProducts([newProduct, ...products]);\n      }\n\n      resetForm();\n    } catch (err) {\n      setError('Failed to save product');\n    }\n  };\n\n  const handleEdit = (product) => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description,\n      imageUrl: product.imageUrl,\n      price: product.price.toString(),\n      tags: product.tags?.join(', ') || '',\n      isEcoFriendly: product.isEcoFriendly\n    });\n    setShowAddForm(true);\n  };\n\n  const handleDelete = (productId) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      setProducts(products.filter(p => p._id !== productId));\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      imageUrl: '',\n      price: '',\n      tags: '',\n      isEcoFriendly: false\n    });\n    setEditingProduct(null);\n    setShowAddForm(false);\n  };\n\n  if (loading) {\n    return <LoadingSpinner size=\"large\" message=\"Loading admin panel...\" fullScreen />;\n  }\n\n  if (error) {\n    return <ErrorMessage title=\"Admin Panel Error\" message={error} onRetry={fetchProducts} />;\n  }\n\n  return (\n    <main>\n      <div className=\"admin-container\">\n        <div className=\"admin-header\">\n          <h1>\n            <i className=\"fas fa-cog\"></i>\n            Product Management\n          </h1>\n          <button \n            onClick={() => setShowAddForm(true)}\n            className=\"add-product-btn\"\n          >\n            <i className=\"fas fa-plus\"></i>\n            Add New Product\n          </button>\n        </div>\n\n        {showAddForm && (\n          <div className=\"product-form-modal\">\n            <div className=\"modal-content\">\n              <div className=\"modal-header\">\n                <h2>{editingProduct ? 'Edit Product' : 'Add New Product'}</h2>\n                <button onClick={resetForm} className=\"close-btn\">\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit} className=\"product-form\">\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label htmlFor=\"name\">Product Name *</label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label htmlFor=\"price\">Price *</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"description\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    rows=\"3\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"imageUrl\">Image URL</label>\n                  <input\n                    type=\"url\"\n                    id=\"imageUrl\"\n                    name=\"imageUrl\"\n                    value={formData.imageUrl}\n                    onChange={handleInputChange}\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"tags\">Tags (comma-separated)</label>\n                  <input\n                    type=\"text\"\n                    id=\"tags\"\n                    name=\"tags\"\n                    value={formData.tags}\n                    onChange={handleInputChange}\n                    placeholder=\"eco-friendly, sustainable, organic\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label className=\"checkbox-label\">\n                    <input\n                      type=\"checkbox\"\n                      name=\"isEcoFriendly\"\n                      checked={formData.isEcoFriendly}\n                      onChange={handleInputChange}\n                    />\n                    <span className=\"checkmark\"></span>\n                    Eco-Friendly Product\n                  </label>\n                </div>\n\n                <div className=\"form-actions\">\n                  <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                    Cancel\n                  </button>\n                  <button type=\"submit\" className=\"save-btn\">\n                    <i className=\"fas fa-save\"></i>\n                    {editingProduct ? 'Update Product' : 'Add Product'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        <div className=\"products-table\">\n          <div className=\"table-header\">\n            <h3>All Products ({products.length})</h3>\n          </div>\n\n          <div className=\"table-container\">\n            <table>\n              <thead>\n                <tr>\n                  <th>Image</th>\n                  <th>Name</th>\n                  <th>Price</th>\n                  <th>Eco-Friendly</th>\n                  <th>Tags</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map(product => (\n                  <tr key={product._id}>\n                    <td>\n                      <img \n                        src={product.imageUrl} \n                        alt={product.name}\n                        className=\"product-thumbnail\"\n                      />\n                    </td>\n                    <td>\n                      <div className=\"product-name\">{product.name}</div>\n                      <div className=\"product-description\">\n                        {product.description?.substring(0, 50)}...\n                      </div>\n                    </td>\n                    <td className=\"price-cell\">${product.price}</td>\n                    <td>\n                      {product.isEcoFriendly ? (\n                        <span className=\"eco-badge\">\n                          <i className=\"fas fa-leaf\"></i>\n                          Yes\n                        </span>\n                      ) : (\n                        <span className=\"not-eco\">No</span>\n                      )}\n                    </td>\n                    <td>\n                      <div className=\"tags-cell\">\n                        {product.tags?.slice(0, 2).map((tag, index) => (\n                          <span key={index} className=\"tag\">{tag}</span>\n                        ))}\n                        {product.tags?.length > 2 && (\n                          <span className=\"tag-more\">+{product.tags.length - 2}</span>\n                        )}\n                      </div>\n                    </td>\n                    <td>\n                      <div className=\"action-buttons\">\n                        <button \n                          onClick={() => handleEdit(product)}\n                          className=\"edit-btn\"\n                          title=\"Edit product\"\n                        >\n                          <i className=\"fas fa-edit\"></i>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(product._id)}\n                          className=\"delete-btn\"\n                          title=\"Delete product\"\n                        >\n                          <i className=\"fas fa-trash\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .admin-container {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 2rem;\n        }\n\n        .admin-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 2px solid var(--border-color);\n        }\n\n        .admin-header h1 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: var(--text-primary);\n          margin: 0;\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .add-product-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .add-product-btn:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        .product-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 1rem;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .product-form {\n          padding: 2rem;\n        }\n\n        .form-row {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 1rem;\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .form-group input,\n        .form-group textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-size: 1rem;\n          transition: border-color 0.2s;\n        }\n\n        .form-group input:focus,\n        .form-group textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .checkbox-label {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          cursor: pointer;\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n          margin-top: 2rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .products-table {\n          background: white;\n          border-radius: 1rem;\n          box-shadow: var(--shadow-sm);\n          border: 1px solid var(--border-color);\n          overflow: hidden;\n        }\n\n        .table-header {\n          padding: 1.5rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .table-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .table-container {\n          overflow-x: auto;\n        }\n\n        table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        th, td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        th {\n          background: var(--bg-secondary);\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .product-thumbnail {\n          width: 60px;\n          height: 60px;\n          object-fit: cover;\n          border-radius: 0.5rem;\n        }\n\n        .product-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          margin-bottom: 0.25rem;\n        }\n\n        .product-description {\n          font-size: 0.875rem;\n          color: var(--text-secondary);\n        }\n\n        .price-cell {\n          font-weight: 600;\n          color: var(--primary-color);\n          font-size: 1.125rem;\n        }\n\n        .eco-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          display: inline-flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .not-eco {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .tags-cell {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.25rem;\n        }\n\n        .tag {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .tag-more {\n          background: var(--text-secondary);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 0.375rem;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .edit-btn, .delete-btn {\n          background: none;\n          border: 1px solid var(--border-color);\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .edit-btn {\n          color: var(--primary-color);\n        }\n\n        .edit-btn:hover {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .delete-btn {\n          color: #ef4444;\n        }\n\n        .delete-btn:hover {\n          background: #ef4444;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .admin-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n\n          .form-row {\n            grid-template-columns: 1fr;\n          }\n\n          .table-container {\n            font-size: 0.875rem;\n          }\n\n          th, td {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n      `}</style>\n    </main>\n  );\n}\n\nexport default AdminPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEFzB,SAAS,CAAC,MAAM;IACd0B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,oCAAoC,CAAC;MACtEnB,WAAW,CAACkB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZjB,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEZ,IAAI;MAAEa,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACjB,IAAI,GAAGc,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGtB,QAAQ;QACXK,KAAK,EAAEkB,UAAU,CAACvB,QAAQ,CAACK,KAAK,CAAC;QACjCC,IAAI,EAAEN,QAAQ,CAACM,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACF,GAAG,IAAIA,GAAG;MACzE,CAAC;MAED,IAAI5B,cAAc,EAAE;QAClB;QACA,MAAM+B,eAAe,GAAGvC,QAAQ,CAACmC,GAAG,CAACK,CAAC,IACpCA,CAAC,CAACC,GAAG,KAAKjC,cAAc,CAACiC,GAAG,GAAG;UAAE,GAAGD,CAAC;UAAE,GAAGR;QAAY,CAAC,GAAGQ,CAC5D,CAAC;QACDvC,WAAW,CAACsC,eAAe,CAAC;MAC9B,CAAC,MAAM;QACL;QACA,MAAMG,UAAU,GAAG;UACjBD,GAAG,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC1B,GAAGb;QACL,CAAC;QACD/B,WAAW,CAAC,CAACyC,UAAU,EAAE,GAAG1C,QAAQ,CAAC,CAAC;MACxC;MAEA8C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZjB,QAAQ,CAAC,wBAAwB,CAAC;IACpC;EACF,CAAC;EAED,MAAM0C,UAAU,GAAIC,OAAO,IAAK;IAAA,IAAAC,aAAA;IAC9BxC,iBAAiB,CAACuC,OAAO,CAAC;IAC1BrC,WAAW,CAAC;MACVC,IAAI,EAAEoC,OAAO,CAACpC,IAAI;MAClBC,WAAW,EAAEmC,OAAO,CAACnC,WAAW;MAChCC,QAAQ,EAAEkC,OAAO,CAAClC,QAAQ;MAC1BC,KAAK,EAAEiC,OAAO,CAACjC,KAAK,CAAC8B,QAAQ,CAAC,CAAC;MAC/B7B,IAAI,EAAE,EAAAiC,aAAA,GAAAD,OAAO,CAAChC,IAAI,cAAAiC,aAAA,uBAAZA,aAAA,CAAcC,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;MACpCjC,aAAa,EAAE+B,OAAO,CAAC/B;IACzB,CAAC,CAAC;IACFV,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM4C,YAAY,GAAIC,SAAS,IAAK;IAClC,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnErD,WAAW,CAACD,QAAQ,CAACsC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKW,SAAS,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMN,SAAS,GAAGA,CAAA,KAAM;IACtBnC,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE;IACjB,CAAC,CAAC;IACFR,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBAAOL,OAAA,CAACH,cAAc;MAAC6D,IAAI,EAAC,OAAO;MAACC,OAAO,EAAC,wBAAwB;MAACC,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpF;EAEA,IAAIzD,KAAK,EAAE;IACT,oBAAOP,OAAA,CAACF,YAAY;MAACmE,KAAK,EAAC,mBAAmB;MAACN,OAAO,EAAEpD,KAAM;MAAC2D,OAAO,EAAE7C;IAAc;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F;EAEA,oBACEhE,OAAA;IAAAmE,QAAA,gBACEnE,OAAA;MAAKoE,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC9BnE,OAAA;QAAKoE,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BnE,OAAA;UAAAmE,QAAA,gBACEnE,OAAA;YAAGoE,SAAS,EAAC;UAAY;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhE,OAAA;UACEqE,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,IAAI,CAAE;UACpC0D,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAE3BnE,OAAA;YAAGoE,SAAS,EAAC;UAAa;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,mBAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvD,WAAW,iBACVT,OAAA;QAAKoE,SAAS,EAAC,oBAAoB;QAAAD,QAAA,eACjCnE,OAAA;UAAKoE,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5BnE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3BnE,OAAA;cAAAmE,QAAA,EAAKxD,cAAc,GAAG,cAAc,GAAG;YAAiB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DhE,OAAA;cAAQqE,OAAO,EAAEpB,SAAU;cAACmB,SAAS,EAAC,WAAW;cAAAD,QAAA,eAC/CnE,OAAA;gBAAGoE,SAAS,EAAC;cAAc;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhE,OAAA;YAAMsE,QAAQ,EAAErC,YAAa;YAACmC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACpDnE,OAAA;cAAKoE,SAAS,EAAC,UAAU;cAAAD,QAAA,gBACvBnE,OAAA;gBAAKoE,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBnE,OAAA;kBAAOuE,OAAO,EAAC,MAAM;kBAAAJ,QAAA,EAAC;gBAAc;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5ChE,OAAA;kBACE6B,IAAI,EAAC,MAAM;kBACX2C,EAAE,EAAC,MAAM;kBACTzD,IAAI,EAAC,MAAM;kBACXa,KAAK,EAAEf,QAAQ,CAACE,IAAK;kBACrB0D,QAAQ,EAAE/C,iBAAkB;kBAC5BgD,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhE,OAAA;gBAAKoE,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBnE,OAAA;kBAAOuE,OAAO,EAAC,OAAO;kBAAAJ,QAAA,EAAC;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtChE,OAAA;kBACE6B,IAAI,EAAC,QAAQ;kBACb2C,EAAE,EAAC,OAAO;kBACVzD,IAAI,EAAC,OAAO;kBACZa,KAAK,EAAEf,QAAQ,CAACK,KAAM;kBACtBuD,QAAQ,EAAE/C,iBAAkB;kBAC5BiD,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPF,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBnE,OAAA;gBAAOuE,OAAO,EAAC,aAAa;gBAAAJ,QAAA,EAAC;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDhE,OAAA;gBACEwE,EAAE,EAAC,aAAa;gBAChBzD,IAAI,EAAC,aAAa;gBAClBa,KAAK,EAAEf,QAAQ,CAACG,WAAY;gBAC5ByD,QAAQ,EAAE/C,iBAAkB;gBAC5BmD,IAAI,EAAC;cAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBnE,OAAA;gBAAOuE,OAAO,EAAC,UAAU;gBAAAJ,QAAA,EAAC;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ChE,OAAA;gBACE6B,IAAI,EAAC,KAAK;gBACV2C,EAAE,EAAC,UAAU;gBACbzD,IAAI,EAAC,UAAU;gBACfa,KAAK,EAAEf,QAAQ,CAACI,QAAS;gBACzBwD,QAAQ,EAAE/C;cAAkB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBnE,OAAA;gBAAOuE,OAAO,EAAC,MAAM;gBAAAJ,QAAA,EAAC;cAAsB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDhE,OAAA;gBACE6B,IAAI,EAAC,MAAM;gBACX2C,EAAE,EAAC,MAAM;gBACTzD,IAAI,EAAC,MAAM;gBACXa,KAAK,EAAEf,QAAQ,CAACM,IAAK;gBACrBsD,QAAQ,EAAE/C,iBAAkB;gBAC5BoD,WAAW,EAAC;cAAoC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAD,QAAA,eACzBnE,OAAA;gBAAOoE,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC/BnE,OAAA;kBACE6B,IAAI,EAAC,UAAU;kBACfd,IAAI,EAAC,eAAe;kBACpBe,OAAO,EAAEjB,QAAQ,CAACO,aAAc;kBAChCqD,QAAQ,EAAE/C;gBAAkB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACFhE,OAAA;kBAAMoE,SAAS,EAAC;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,wBAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC3BnE,OAAA;gBAAQ6B,IAAI,EAAC,QAAQ;gBAACwC,OAAO,EAAEpB,SAAU;gBAACmB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAEjE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA;gBAAQ6B,IAAI,EAAC,QAAQ;gBAACuC,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACxCnE,OAAA;kBAAGoE,SAAS,EAAC;gBAAa;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9BrD,cAAc,GAAG,gBAAgB,GAAG,aAAa;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDhE,OAAA;QAAKoE,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7BnE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC3BnE,OAAA;YAAAmE,QAAA,GAAI,gBAAc,EAAChE,QAAQ,CAAC4E,MAAM,EAAC,GAAC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAENhE,OAAA;UAAKoE,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAAmE,QAAA,eACEnE,OAAA;gBAAAmE,QAAA,gBACEnE,OAAA;kBAAAmE,QAAA,EAAI;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdhE,OAAA;kBAAAmE,QAAA,EAAI;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbhE,OAAA;kBAAAmE,QAAA,EAAI;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdhE,OAAA;kBAAAmE,QAAA,EAAI;gBAAY;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBhE,OAAA;kBAAAmE,QAAA,EAAI;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbhE,OAAA;kBAAAmE,QAAA,EAAI;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhE,OAAA;cAAAmE,QAAA,EACGhE,QAAQ,CAACmC,GAAG,CAACa,OAAO;gBAAA,IAAA6B,oBAAA,EAAAC,cAAA,EAAAC,cAAA;gBAAA,oBACnBlF,OAAA;kBAAAmE,QAAA,gBACEnE,OAAA;oBAAAmE,QAAA,eACEnE,OAAA;sBACEmF,GAAG,EAAEhC,OAAO,CAAClC,QAAS;sBACtBmE,GAAG,EAAEjC,OAAO,CAACpC,IAAK;sBAClBqD,SAAS,EAAC;oBAAmB;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACLhE,OAAA;oBAAAmE,QAAA,gBACEnE,OAAA;sBAAKoE,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAEhB,OAAO,CAACpC;oBAAI;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClDhE,OAAA;sBAAKoE,SAAS,EAAC,qBAAqB;sBAAAD,QAAA,IAAAa,oBAAA,GACjC7B,OAAO,CAACnC,WAAW,cAAAgE,oBAAA,uBAAnBA,oBAAA,CAAqBK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACzC;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhE,OAAA;oBAAIoE,SAAS,EAAC,YAAY;oBAAAD,QAAA,GAAC,GAAC,EAAChB,OAAO,CAACjC,KAAK;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDhE,OAAA;oBAAAmE,QAAA,EACGhB,OAAO,CAAC/B,aAAa,gBACpBpB,OAAA;sBAAMoE,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACzBnE,OAAA;wBAAGoE,SAAS,EAAC;sBAAa;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,OAEjC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEPhE,OAAA;sBAAMoE,SAAS,EAAC,SAAS;sBAAAD,QAAA,EAAC;oBAAE;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACnC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLhE,OAAA;oBAAAmE,QAAA,eACEnE,OAAA;sBAAKoE,SAAS,EAAC,WAAW;sBAAAD,QAAA,IAAAc,cAAA,GACvB9B,OAAO,CAAChC,IAAI,cAAA8D,cAAA,uBAAZA,cAAA,CAAcK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChD,GAAG,CAAC,CAACC,GAAG,EAAEgD,KAAK,kBACxCvF,OAAA;wBAAkBoE,SAAS,EAAC,KAAK;wBAAAD,QAAA,EAAE5B;sBAAG,GAA3BgD,KAAK;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAA6B,CAC9C,CAAC,EACD,EAAAkB,cAAA,GAAA/B,OAAO,CAAChC,IAAI,cAAA+D,cAAA,uBAAZA,cAAA,CAAcH,MAAM,IAAG,CAAC,iBACvB/E,OAAA;wBAAMoE,SAAS,EAAC,UAAU;wBAAAD,QAAA,GAAC,GAAC,EAAChB,OAAO,CAAChC,IAAI,CAAC4D,MAAM,GAAG,CAAC;sBAAA;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAC5D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhE,OAAA;oBAAAmE,QAAA,eACEnE,OAAA;sBAAKoE,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,gBAC7BnE,OAAA;wBACEqE,OAAO,EAAEA,CAAA,KAAMnB,UAAU,CAACC,OAAO,CAAE;wBACnCiB,SAAS,EAAC,UAAU;wBACpBH,KAAK,EAAC,cAAc;wBAAAE,QAAA,eAEpBnE,OAAA;0BAAGoE,SAAS,EAAC;wBAAa;0BAAAP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACThE,OAAA;wBACEqE,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACH,OAAO,CAACP,GAAG,CAAE;wBACzCwB,SAAS,EAAC,YAAY;wBACtBH,KAAK,EAAC,gBAAgB;wBAAAE,QAAA,eAEtBnE,OAAA;0BAAGoE,SAAS,EAAC;wBAAc;0BAAAP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GApDEb,OAAO,CAACP,GAAG;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqDhB,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhE,OAAA;MAAOwF,GAAG;MAAArB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX;AAAC9D,EAAA,CAhoBQD,SAAS;AAAAwF,EAAA,GAATxF,SAAS;AAkoBlB,eAAeA,SAAS;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}