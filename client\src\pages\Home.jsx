import React, { useState, useEffect } from 'react';
import ProductCard from '../components/ProductCard';
import SearchAndFilter from '../components/SearchAndFilter';
import axios from 'axios';

function Home() {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/products');
      setProducts(response.data);
      setFilteredProducts(response.data);
    } catch (err) {
      setError('Failed to load products. Please try again later.');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <main>
        <div className="loading-container">
          <div className="loading-spinner">
            <i className="fas fa-spinner fa-spin"></i>
          </div>
          <p>Loading amazing products...</p>
        </div>
        <style jsx>{`
          .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            gap: 1rem;
          }
          .loading-spinner {
            font-size: 2rem;
            color: var(--primary-color);
          }
        `}</style>
      </main>
    );
  }

  if (error) {
    return (
      <main>
        <div className="error-container">
          <div className="error-icon">
            <i className="fas fa-exclamation-triangle"></i>
          </div>
          <h2>Oops! Something went wrong</h2>
          <p>{error}</p>
          <button onClick={fetchProducts} className="retry-btn">
            <i className="fas fa-redo"></i>
            Try Again
          </button>
        </div>
        <style jsx>{`
          .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            gap: 1rem;
            text-align: center;
          }
          .error-icon {
            font-size: 3rem;
            color: #ef4444;
          }
          .retry-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }
        `}</style>
      </main>
    );
  }

  return (
    <main>
      <div className="hero-section">
        <h1>Welcome to EcoCommerce</h1>
        <p>Discover sustainable products for a better tomorrow 🌱</p>
      </div>

      <SearchAndFilter
        products={products}
        onFilteredProducts={setFilteredProducts}
      />

      <div className="products-section">
        <div className="section-header">
          <h2>
            {filteredProducts.length === products.length
              ? 'All Products'
              : `Found ${filteredProducts.length} product${filteredProducts.length !== 1 ? 's' : ''}`
            }
          </h2>
          <span className="product-count">
            {filteredProducts.length} of {products.length} products
          </span>
        </div>

        {filteredProducts.length === 0 ? (
          <div className="no-products">
            <div className="no-products-icon">
              <i className="fas fa-search"></i>
            </div>
            <h3>No products found</h3>
            <p>Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          <div className="product-list">
            {filteredProducts.map(product => (
              <ProductCard key={product._id} product={product} />
            ))}
          </div>
        )}
      </div>

      <style jsx>{`
        .hero-section {
          text-align: center;
          padding: 3rem 0;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border-radius: 1rem;
          margin-bottom: 2rem;
        }

        .hero-section h1 {
          font-size: 2.5rem;
          font-weight: 700;
          color: var(--text-primary);
          margin: 0 0 1rem 0;
        }

        .hero-section p {
          font-size: 1.25rem;
          color: var(--text-secondary);
          margin: 0;
        }

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .section-header h2 {
          font-size: 1.75rem;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0;
        }

        .product-count {
          color: var(--text-secondary);
          font-size: 0.875rem;
          background: var(--bg-secondary);
          padding: 0.5rem 1rem;
          border-radius: 9999px;
        }

        .no-products {
          text-align: center;
          padding: 4rem 2rem;
          color: var(--text-secondary);
        }

        .no-products-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }

        .no-products h3 {
          font-size: 1.5rem;
          margin: 0 0 0.5rem 0;
        }

        @media (max-width: 768px) {
          .hero-section h1 {
            font-size: 2rem;
          }

          .section-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
          }
        }
      `}</style>
    </main>
  );
}

export default Home;