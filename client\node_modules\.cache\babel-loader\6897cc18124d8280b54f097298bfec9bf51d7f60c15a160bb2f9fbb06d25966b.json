{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\exam\\\\e-commerce website\\\\ecommerce-app\\\\client\\\\src\\\\components\\\\ImageUpload.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ImageUpload({\n  currentImage,\n  onImageChange,\n  onClose\n}) {\n  _s();\n  var _imageRef$current, _imageRef$current2;\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(currentImage || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [cropArea, setCropArea] = useState({\n    x: 0,\n    y: 0,\n    width: 200,\n    height: 200\n  });\n  const [isResizing, setIsResizing] = useState(false);\n  const fileInputRef = useRef(null);\n  const imageRef = useRef(null);\n  const cropRef = useRef(null);\n  const handleFileSelect = file => {\n    if (file && file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setSelectedImage(file);\n        setPreviewUrl(e.target.result);\n        // Reset crop area when new image is loaded\n        setCropArea({\n          x: 50,\n          y: 50,\n          width: 200,\n          height: 200\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleFileInputChange = e => {\n    const file = e.target.files[0];\n    handleFileSelect(file);\n  };\n  const handleDragOver = useCallback(e => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n  const handleDragLeave = useCallback(e => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n  const handleDrop = useCallback(e => {\n    e.preventDefault();\n    setIsDragging(false);\n    const file = e.dataTransfer.files[0];\n    handleFileSelect(file);\n  }, []);\n  const handleCropMouseDown = e => {\n    e.preventDefault();\n    setIsResizing(true);\n    const startX = e.clientX;\n    const startY = e.clientY;\n    const startCrop = {\n      ...cropArea\n    };\n    const handleMouseMove = e => {\n      const deltaX = e.clientX - startX;\n      const deltaY = e.clientY - startY;\n      if (e.target.classList.contains('crop-handle')) {\n        // Resize crop area\n        const newWidth = Math.max(100, startCrop.width + deltaX);\n        const newHeight = Math.max(100, startCrop.height + deltaY);\n        setCropArea(prev => ({\n          ...prev,\n          width: newWidth,\n          height: newHeight\n        }));\n      } else {\n        // Move crop area\n        setCropArea(prev => ({\n          ...prev,\n          x: Math.max(0, startCrop.x + deltaX),\n          y: Math.max(0, startCrop.y + deltaY)\n        }));\n      }\n    };\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n  const cropImage = () => {\n    if (!previewUrl || !imageRef.current) return null;\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = imageRef.current;\n\n    // Set canvas size to crop area\n    canvas.width = cropArea.width;\n    canvas.height = cropArea.height;\n\n    // Calculate scale factor\n    const scaleX = img.naturalWidth / img.offsetWidth;\n    const scaleY = img.naturalHeight / img.offsetHeight;\n\n    // Draw cropped image\n    ctx.drawImage(img, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, cropArea.width, cropArea.height);\n    return canvas.toDataURL('image/jpeg', 0.9);\n  };\n  const handleSave = () => {\n    const croppedImage = cropImage();\n    if (croppedImage) {\n      onImageChange(croppedImage);\n      onClose();\n    }\n  };\n  const handleRemoveImage = () => {\n    onImageChange(null);\n    onClose();\n  };\n  const predefinedAvatars = ['https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"image-upload-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-camera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), \"Edit Profile Picture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-btn\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-times\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tab active\",\n            children: \"Upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tab\",\n            children: \"Choose Avatar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-area\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `drop-zone ${isDragging ? 'dragging' : ''}`,\n            onDragOver: handleDragOver,\n            onDragLeave: handleDragLeave,\n            onDrop: handleDrop,\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cloud-upload-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Drag & drop an image here or click to browse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Supports: JPG, PNG, GIF (Max 5MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleFileInputChange,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"predefined-avatars\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Or choose a predefined avatar:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"avatar-grid\",\n            children: predefinedAvatars.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"avatar-option\",\n              onClick: () => {\n                onImageChange(avatar);\n                onClose();\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: avatar,\n                alt: `Avatar ${index + 1}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Crop your image:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"crop-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              ref: imageRef,\n              src: previewUrl,\n              alt: \"Preview\",\n              className: \"preview-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: cropRef,\n              className: \"crop-overlay\",\n              style: {\n                left: cropArea.x,\n                top: cropArea.y,\n                width: cropArea.width,\n                height: cropArea.height\n              },\n              onMouseDown: handleCropMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"crop-handle crop-handle-br\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"crop-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Preview:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"avatar-preview\",\n              children: previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cropped-preview\",\n                style: {\n                  backgroundImage: `url(${previewUrl})`,\n                  backgroundPosition: `-${cropArea.x}px -${cropArea.y}px`,\n                  backgroundSize: `${((_imageRef$current = imageRef.current) === null || _imageRef$current === void 0 ? void 0 : _imageRef$current.offsetWidth) || 300}px ${((_imageRef$current2 = imageRef.current) === null || _imageRef$current2 === void 0 ? void 0 : _imageRef$current2.offsetHeight) || 300}px`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-actions\",\n        children: [currentImage && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRemoveImage,\n          className: \"remove-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-trash\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), \"Remove Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"cancel-btn\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            className: \"save-btn\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), \"Save Photo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .image-upload-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 10000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.7);\n          backdrop-filter: blur(4px);\n        }\n\n        .modal-content {\n          position: relative;\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 800px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .upload-section {\n          padding: 2rem;\n        }\n\n        .upload-tabs {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 2rem;\n        }\n\n        .tab {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .tab.active {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .drop-zone {\n          border: 2px dashed var(--border-color);\n          border-radius: 1rem;\n          padding: 3rem 2rem;\n          text-align: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-bottom: 2rem;\n        }\n\n        .drop-zone:hover,\n        .drop-zone.dragging {\n          border-color: var(--primary-color);\n          background: var(--bg-secondary);\n        }\n\n        .drop-zone i {\n          font-size: 3rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .drop-zone p {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.125rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .drop-zone small {\n          color: var(--text-secondary);\n        }\n\n        .predefined-avatars h4 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\n          gap: 1rem;\n        }\n\n        .avatar-option {\n          background: none;\n          border: 2px solid var(--border-color);\n          border-radius: 50%;\n          padding: 4px;\n          cursor: pointer;\n          transition: all 0.2s;\n          width: 80px;\n          height: 80px;\n        }\n\n        .avatar-option:hover {\n          border-color: var(--primary-color);\n          transform: scale(1.05);\n        }\n\n        .avatar-option img {\n          width: 100%;\n          height: 100%;\n          border-radius: 50%;\n          object-fit: cover;\n        }\n\n        .preview-section {\n          padding: 0 2rem 2rem 2rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .preview-section h4 {\n          margin: 2rem 0 1rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .image-preview {\n          display: grid;\n          grid-template-columns: 1fr 200px;\n          gap: 2rem;\n          align-items: start;\n        }\n\n        .crop-container {\n          position: relative;\n          display: inline-block;\n          max-width: 100%;\n        }\n\n        .preview-image {\n          max-width: 100%;\n          max-height: 400px;\n          border-radius: 0.5rem;\n          user-select: none;\n        }\n\n        .crop-overlay {\n          position: absolute;\n          border: 2px solid var(--primary-color);\n          background: rgba(37, 99, 235, 0.1);\n          cursor: move;\n          min-width: 100px;\n          min-height: 100px;\n        }\n\n        .crop-handle {\n          position: absolute;\n          width: 12px;\n          height: 12px;\n          background: var(--primary-color);\n          border: 2px solid white;\n          border-radius: 50%;\n        }\n\n        .crop-handle-br {\n          bottom: -6px;\n          right: -6px;\n          cursor: se-resize;\n        }\n\n        .crop-preview h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-preview {\n          display: flex;\n          justify-content: center;\n        }\n\n        .cropped-preview {\n          width: 150px;\n          height: 150px;\n          border-radius: 50%;\n          border: 3px solid var(--border-color);\n          background-repeat: no-repeat;\n          overflow: hidden;\n        }\n\n        .modal-actions {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem 2rem;\n          border-top: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .remove-btn {\n          background: #ef4444;\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .remove-btn:hover {\n          background: #dc2626;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .save-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .modal-content {\n            margin: 1rem;\n            max-height: calc(100vh - 2rem);\n          }\n\n          .image-preview {\n            grid-template-columns: 1fr;\n            gap: 1rem;\n          }\n\n          .modal-actions {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: stretch;\n          }\n\n          .action-buttons {\n            justify-content: center;\n          }\n\n          .avatar-grid {\n            grid-template-columns: repeat(4, 1fr);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n}\n_s(ImageUpload, \"Y+9fSesKuWKdh5uf9C/2e33MA8s=\");\n_c = ImageUpload;\nexport default ImageUpload;\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "jsxDEV", "_jsxDEV", "ImageUpload", "currentImage", "onImageChange", "onClose", "_s", "_imageRef$current", "_imageRef$current2", "selectedImage", "setSelectedImage", "previewUrl", "setPreviewUrl", "isDragging", "setIsDragging", "cropArea", "setCropArea", "x", "y", "width", "height", "isResizing", "setIsResizing", "fileInputRef", "imageRef", "cropRef", "handleFileSelect", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "handleFileInputChange", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleCropMouseDown", "startX", "clientX", "startY", "clientY", "startCrop", "handleMouseMove", "deltaX", "deltaY", "classList", "contains", "newWidth", "Math", "max", "newHeight", "prev", "handleMouseUp", "document", "removeEventListener", "addEventListener", "cropImage", "current", "canvas", "createElement", "ctx", "getContext", "img", "scaleX", "naturalWidth", "offsetWidth", "scaleY", "naturalHeight", "offsetHeight", "drawImage", "toDataURL", "handleSave", "croppedImage", "handleRemoveImage", "predefinedAvatars", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDragOver", "onDragLeave", "onDrop", "_fileInputRef$current", "click", "ref", "accept", "onChange", "style", "display", "map", "avatar", "index", "src", "alt", "left", "top", "onMouseDown", "backgroundImage", "backgroundPosition", "backgroundSize", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ImageUpload.jsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\n\nfunction ImageUpload({ currentImage, onImageChange, onClose }) {\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(currentImage || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [cropArea, setCropArea] = useState({ x: 0, y: 0, width: 200, height: 200 });\n  const [isResizing, setIsResizing] = useState(false);\n  const fileInputRef = useRef(null);\n  const imageRef = useRef(null);\n  const cropRef = useRef(null);\n\n  const handleFileSelect = (file) => {\n    if (file && file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setSelectedImage(file);\n        setPreviewUrl(e.target.result);\n        // Reset crop area when new image is loaded\n        setCropArea({ x: 50, y: 50, width: 200, height: 200 });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleFileInputChange = (e) => {\n    const file = e.target.files[0];\n    handleFileSelect(file);\n  };\n\n  const handleDragOver = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const handleDrop = useCallback((e) => {\n    e.preventDefault();\n    setIsDragging(false);\n    const file = e.dataTransfer.files[0];\n    handleFileSelect(file);\n  }, []);\n\n  const handleCropMouseDown = (e) => {\n    e.preventDefault();\n    setIsResizing(true);\n    \n    const startX = e.clientX;\n    const startY = e.clientY;\n    const startCrop = { ...cropArea };\n\n    const handleMouseMove = (e) => {\n      const deltaX = e.clientX - startX;\n      const deltaY = e.clientY - startY;\n      \n      if (e.target.classList.contains('crop-handle')) {\n        // Resize crop area\n        const newWidth = Math.max(100, startCrop.width + deltaX);\n        const newHeight = Math.max(100, startCrop.height + deltaY);\n        setCropArea(prev => ({\n          ...prev,\n          width: newWidth,\n          height: newHeight\n        }));\n      } else {\n        // Move crop area\n        setCropArea(prev => ({\n          ...prev,\n          x: Math.max(0, startCrop.x + deltaX),\n          y: Math.max(0, startCrop.y + deltaY)\n        }));\n      }\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  const cropImage = () => {\n    if (!previewUrl || !imageRef.current) return null;\n\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = imageRef.current;\n    \n    // Set canvas size to crop area\n    canvas.width = cropArea.width;\n    canvas.height = cropArea.height;\n    \n    // Calculate scale factor\n    const scaleX = img.naturalWidth / img.offsetWidth;\n    const scaleY = img.naturalHeight / img.offsetHeight;\n    \n    // Draw cropped image\n    ctx.drawImage(\n      img,\n      cropArea.x * scaleX,\n      cropArea.y * scaleY,\n      cropArea.width * scaleX,\n      cropArea.height * scaleY,\n      0,\n      0,\n      cropArea.width,\n      cropArea.height\n    );\n    \n    return canvas.toDataURL('image/jpeg', 0.9);\n  };\n\n  const handleSave = () => {\n    const croppedImage = cropImage();\n    if (croppedImage) {\n      onImageChange(croppedImage);\n      onClose();\n    }\n  };\n\n  const handleRemoveImage = () => {\n    onImageChange(null);\n    onClose();\n  };\n\n  const predefinedAvatars = [\n    'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'\n  ];\n\n  return (\n    <div className=\"image-upload-modal\">\n      <div className=\"modal-overlay\" onClick={onClose}></div>\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h3>\n            <i className=\"fas fa-camera\"></i>\n            Edit Profile Picture\n          </h3>\n          <button onClick={onClose} className=\"close-btn\">\n            <i className=\"fas fa-times\"></i>\n          </button>\n        </div>\n\n        <div className=\"upload-section\">\n          <div className=\"upload-tabs\">\n            <button className=\"tab active\">Upload</button>\n            <button className=\"tab\">Choose Avatar</button>\n          </div>\n\n          <div className=\"upload-area\">\n            <div \n              className={`drop-zone ${isDragging ? 'dragging' : ''}`}\n              onDragOver={handleDragOver}\n              onDragLeave={handleDragLeave}\n              onDrop={handleDrop}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <i className=\"fas fa-cloud-upload-alt\"></i>\n              <p>Drag & drop an image here or click to browse</p>\n              <small>Supports: JPG, PNG, GIF (Max 5MB)</small>\n            </div>\n            \n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileInputChange}\n              style={{ display: 'none' }}\n            />\n          </div>\n\n          <div className=\"predefined-avatars\">\n            <h4>Or choose a predefined avatar:</h4>\n            <div className=\"avatar-grid\">\n              {predefinedAvatars.map((avatar, index) => (\n                <button\n                  key={index}\n                  className=\"avatar-option\"\n                  onClick={() => {\n                    onImageChange(avatar);\n                    onClose();\n                  }}\n                >\n                  <img src={avatar} alt={`Avatar ${index + 1}`} />\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {previewUrl && (\n          <div className=\"preview-section\">\n            <h4>Crop your image:</h4>\n            <div className=\"image-preview\">\n              <div className=\"crop-container\">\n                <img\n                  ref={imageRef}\n                  src={previewUrl}\n                  alt=\"Preview\"\n                  className=\"preview-image\"\n                />\n                <div\n                  ref={cropRef}\n                  className=\"crop-overlay\"\n                  style={{\n                    left: cropArea.x,\n                    top: cropArea.y,\n                    width: cropArea.width,\n                    height: cropArea.height\n                  }}\n                  onMouseDown={handleCropMouseDown}\n                >\n                  <div className=\"crop-handle crop-handle-br\"></div>\n                </div>\n              </div>\n              \n              <div className=\"crop-preview\">\n                <h5>Preview:</h5>\n                <div className=\"avatar-preview\">\n                  {previewUrl && (\n                    <div \n                      className=\"cropped-preview\"\n                      style={{\n                        backgroundImage: `url(${previewUrl})`,\n                        backgroundPosition: `-${cropArea.x}px -${cropArea.y}px`,\n                        backgroundSize: `${imageRef.current?.offsetWidth || 300}px ${imageRef.current?.offsetHeight || 300}px`\n                      }}\n                    ></div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"modal-actions\">\n          {currentImage && (\n            <button onClick={handleRemoveImage} className=\"remove-btn\">\n              <i className=\"fas fa-trash\"></i>\n              Remove Photo\n            </button>\n          )}\n          <div className=\"action-buttons\">\n            <button onClick={onClose} className=\"cancel-btn\">\n              Cancel\n            </button>\n            {previewUrl && (\n              <button onClick={handleSave} className=\"save-btn\">\n                <i className=\"fas fa-save\"></i>\n                Save Photo\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .image-upload-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 10000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.7);\n          backdrop-filter: blur(4px);\n        }\n\n        .modal-content {\n          position: relative;\n          background: white;\n          border-radius: 1rem;\n          width: 100%;\n          max-width: 800px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 2rem 2rem 1rem 2rem;\n          border-bottom: 1px solid var(--border-color);\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n        }\n\n        .close-btn {\n          background: none;\n          border: none;\n          font-size: 1.25rem;\n          color: var(--text-secondary);\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .close-btn:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .upload-section {\n          padding: 2rem;\n        }\n\n        .upload-tabs {\n          display: flex;\n          gap: 0.5rem;\n          margin-bottom: 2rem;\n        }\n\n        .tab {\n          background: var(--bg-secondary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .tab.active {\n          background: var(--primary-color);\n          color: white;\n          border-color: var(--primary-color);\n        }\n\n        .drop-zone {\n          border: 2px dashed var(--border-color);\n          border-radius: 1rem;\n          padding: 3rem 2rem;\n          text-align: center;\n          cursor: pointer;\n          transition: all 0.2s;\n          margin-bottom: 2rem;\n        }\n\n        .drop-zone:hover,\n        .drop-zone.dragging {\n          border-color: var(--primary-color);\n          background: var(--bg-secondary);\n        }\n\n        .drop-zone i {\n          font-size: 3rem;\n          color: var(--primary-color);\n          margin-bottom: 1rem;\n        }\n\n        .drop-zone p {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.125rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .drop-zone small {\n          color: var(--text-secondary);\n        }\n\n        .predefined-avatars h4 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\n          gap: 1rem;\n        }\n\n        .avatar-option {\n          background: none;\n          border: 2px solid var(--border-color);\n          border-radius: 50%;\n          padding: 4px;\n          cursor: pointer;\n          transition: all 0.2s;\n          width: 80px;\n          height: 80px;\n        }\n\n        .avatar-option:hover {\n          border-color: var(--primary-color);\n          transform: scale(1.05);\n        }\n\n        .avatar-option img {\n          width: 100%;\n          height: 100%;\n          border-radius: 50%;\n          object-fit: cover;\n        }\n\n        .preview-section {\n          padding: 0 2rem 2rem 2rem;\n          border-top: 1px solid var(--border-color);\n        }\n\n        .preview-section h4 {\n          margin: 2rem 0 1rem 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .image-preview {\n          display: grid;\n          grid-template-columns: 1fr 200px;\n          gap: 2rem;\n          align-items: start;\n        }\n\n        .crop-container {\n          position: relative;\n          display: inline-block;\n          max-width: 100%;\n        }\n\n        .preview-image {\n          max-width: 100%;\n          max-height: 400px;\n          border-radius: 0.5rem;\n          user-select: none;\n        }\n\n        .crop-overlay {\n          position: absolute;\n          border: 2px solid var(--primary-color);\n          background: rgba(37, 99, 235, 0.1);\n          cursor: move;\n          min-width: 100px;\n          min-height: 100px;\n        }\n\n        .crop-handle {\n          position: absolute;\n          width: 12px;\n          height: 12px;\n          background: var(--primary-color);\n          border: 2px solid white;\n          border-radius: 50%;\n        }\n\n        .crop-handle-br {\n          bottom: -6px;\n          right: -6px;\n          cursor: se-resize;\n        }\n\n        .crop-preview h5 {\n          margin: 0 0 1rem 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: var(--text-primary);\n        }\n\n        .avatar-preview {\n          display: flex;\n          justify-content: center;\n        }\n\n        .cropped-preview {\n          width: 150px;\n          height: 150px;\n          border-radius: 50%;\n          border: 3px solid var(--border-color);\n          background-repeat: no-repeat;\n          overflow: hidden;\n        }\n\n        .modal-actions {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem 2rem;\n          border-top: 1px solid var(--border-color);\n          background: var(--bg-secondary);\n        }\n\n        .remove-btn {\n          background: #ef4444;\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .remove-btn:hover {\n          background: #dc2626;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 1rem;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .save-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .save-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        @media (max-width: 768px) {\n          .modal-content {\n            margin: 1rem;\n            max-height: calc(100vh - 2rem);\n          }\n\n          .image-preview {\n            grid-template-columns: 1fr;\n            gap: 1rem;\n          }\n\n          .modal-actions {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: stretch;\n          }\n\n          .action-buttons {\n            justify-content: center;\n          }\n\n          .avatar-grid {\n            grid-template-columns: repeat(4, 1fr);\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ImageUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,SAASC,WAAWA,CAAC;EAAEC,YAAY;EAAEC,aAAa;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EAC7D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAACM,YAAY,IAAI,IAAI,CAAC;EAClE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IAAEoB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,CAAC;EACjF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM0B,YAAY,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0B,QAAQ,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2B,OAAO,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAE5B,MAAM4B,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1C,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrBvB,gBAAgB,CAACiB,IAAI,CAAC;QACtBf,aAAa,CAACqB,CAAC,CAACC,MAAM,CAACC,MAAM,CAAC;QAC9B;QACAnB,WAAW,CAAC;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAC,CAAC;MACxD,CAAC;MACDU,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMU,qBAAqB,GAAIJ,CAAC,IAAK;IACnC,MAAMN,IAAI,GAAGM,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9BZ,gBAAgB,CAACC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMY,cAAc,GAAGxC,WAAW,CAAEkC,CAAC,IAAK;IACxCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB1B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,eAAe,GAAG1C,WAAW,CAAEkC,CAAC,IAAK;IACzCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB1B,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,UAAU,GAAG3C,WAAW,CAAEkC,CAAC,IAAK;IACpCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB1B,aAAa,CAAC,KAAK,CAAC;IACpB,MAAMa,IAAI,GAAGM,CAAC,CAACU,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpCZ,gBAAgB,CAACC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,mBAAmB,GAAIX,CAAC,IAAK;IACjCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBlB,aAAa,CAAC,IAAI,CAAC;IAEnB,MAAMuB,MAAM,GAAGZ,CAAC,CAACa,OAAO;IACxB,MAAMC,MAAM,GAAGd,CAAC,CAACe,OAAO;IACxB,MAAMC,SAAS,GAAG;MAAE,GAAGlC;IAAS,CAAC;IAEjC,MAAMmC,eAAe,GAAIjB,CAAC,IAAK;MAC7B,MAAMkB,MAAM,GAAGlB,CAAC,CAACa,OAAO,GAAGD,MAAM;MACjC,MAAMO,MAAM,GAAGnB,CAAC,CAACe,OAAO,GAAGD,MAAM;MAEjC,IAAId,CAAC,CAACC,MAAM,CAACmB,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC9C;QACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAER,SAAS,CAAC9B,KAAK,GAAGgC,MAAM,CAAC;QACxD,MAAMO,SAAS,GAAGF,IAAI,CAACC,GAAG,CAAC,GAAG,EAAER,SAAS,CAAC7B,MAAM,GAAGgC,MAAM,CAAC;QAC1DpC,WAAW,CAAC2C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPxC,KAAK,EAAEoC,QAAQ;UACfnC,MAAM,EAAEsC;QACV,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACA1C,WAAW,CAAC2C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP1C,CAAC,EAAEuC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,SAAS,CAAChC,CAAC,GAAGkC,MAAM,CAAC;UACpCjC,CAAC,EAAEsC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,SAAS,CAAC/B,CAAC,GAAGkC,MAAM;QACrC,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAED,MAAMQ,aAAa,GAAGA,CAAA,KAAM;MAC1BtC,aAAa,CAAC,KAAK,CAAC;MACpBuC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MAC1DW,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;IACxD,CAAC;IAEDC,QAAQ,CAACE,gBAAgB,CAAC,WAAW,EAAEb,eAAe,CAAC;IACvDW,QAAQ,CAACE,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;EACrD,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACrD,UAAU,IAAI,CAACa,QAAQ,CAACyC,OAAO,EAAE,OAAO,IAAI;IAEjD,MAAMC,MAAM,GAAGL,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG9C,QAAQ,CAACyC,OAAO;;IAE5B;IACAC,MAAM,CAAC/C,KAAK,GAAGJ,QAAQ,CAACI,KAAK;IAC7B+C,MAAM,CAAC9C,MAAM,GAAGL,QAAQ,CAACK,MAAM;;IAE/B;IACA,MAAMmD,MAAM,GAAGD,GAAG,CAACE,YAAY,GAAGF,GAAG,CAACG,WAAW;IACjD,MAAMC,MAAM,GAAGJ,GAAG,CAACK,aAAa,GAAGL,GAAG,CAACM,YAAY;;IAEnD;IACAR,GAAG,CAACS,SAAS,CACXP,GAAG,EACHvD,QAAQ,CAACE,CAAC,GAAGsD,MAAM,EACnBxD,QAAQ,CAACG,CAAC,GAAGwD,MAAM,EACnB3D,QAAQ,CAACI,KAAK,GAAGoD,MAAM,EACvBxD,QAAQ,CAACK,MAAM,GAAGsD,MAAM,EACxB,CAAC,EACD,CAAC,EACD3D,QAAQ,CAACI,KAAK,EACdJ,QAAQ,CAACK,MACX,CAAC;IAED,OAAO8C,MAAM,CAACY,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC;EAC5C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAGhB,SAAS,CAAC,CAAC;IAChC,IAAIgB,YAAY,EAAE;MAChB5E,aAAa,CAAC4E,YAAY,CAAC;MAC3B3E,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAM4E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7E,aAAa,CAAC,IAAI,CAAC;IACnBC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM6E,iBAAiB,GAAG,CACxB,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,CAC9F;EAED,oBACEjF,OAAA;IAAKkF,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCnF,OAAA;MAAKkF,SAAS,EAAC,eAAe;MAACE,OAAO,EAAEhF;IAAQ;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvDxF,OAAA;MAAKkF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BnF,OAAA;QAAKkF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnF,OAAA;UAAAmF,QAAA,gBACEnF,OAAA;YAAGkF,SAAS,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,wBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxF,OAAA;UAAQoF,OAAO,EAAEhF,OAAQ;UAAC8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eAC7CnF,OAAA;YAAGkF,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxF,OAAA;QAAKkF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnF,OAAA;UAAKkF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnF,OAAA;YAAQkF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9CxF,OAAA;YAAQkF,SAAS,EAAC,KAAK;YAAAC,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAENxF,OAAA;UAAKkF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnF,OAAA;YACEkF,SAAS,EAAE,aAAatE,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YACvD6E,UAAU,EAAEnD,cAAe;YAC3BoD,WAAW,EAAElD,eAAgB;YAC7BmD,MAAM,EAAElD,UAAW;YACnB2C,OAAO,EAAEA,CAAA;cAAA,IAAAQ,qBAAA;cAAA,QAAAA,qBAAA,GAAMtE,YAAY,CAAC0C,OAAO,cAAA4B,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAAAV,QAAA,gBAE7CnF,OAAA;cAAGkF,SAAS,EAAC;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CxF,OAAA;cAAAmF,QAAA,EAAG;YAA4C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDxF,OAAA;cAAAmF,QAAA,EAAO;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAENxF,OAAA;YACE8F,GAAG,EAAExE,YAAa;YAClBK,IAAI,EAAC,MAAM;YACXoE,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAE5D,qBAAsB;YAChC6D,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAO;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKkF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCnF,OAAA;YAAAmF,QAAA,EAAI;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvCxF,OAAA;YAAKkF,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBF,iBAAiB,CAACkB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACnCrG,OAAA;cAEEkF,SAAS,EAAC,eAAe;cACzBE,OAAO,EAAEA,CAAA,KAAM;gBACbjF,aAAa,CAACiG,MAAM,CAAC;gBACrBhG,OAAO,CAAC,CAAC;cACX,CAAE;cAAA+E,QAAA,eAEFnF,OAAA;gBAAKsG,GAAG,EAAEF,MAAO;gBAACG,GAAG,EAAE,UAAUF,KAAK,GAAG,CAAC;cAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAP3Ca,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9E,UAAU,iBACTV,OAAA;QAAKkF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnF,OAAA;UAAAmF,QAAA,EAAI;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBxF,OAAA;UAAKkF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnF,OAAA;YAAKkF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnF,OAAA;cACE8F,GAAG,EAAEvE,QAAS;cACd+E,GAAG,EAAE5F,UAAW;cAChB6F,GAAG,EAAC,SAAS;cACbrB,SAAS,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACFxF,OAAA;cACE8F,GAAG,EAAEtE,OAAQ;cACb0D,SAAS,EAAC,cAAc;cACxBe,KAAK,EAAE;gBACLO,IAAI,EAAE1F,QAAQ,CAACE,CAAC;gBAChByF,GAAG,EAAE3F,QAAQ,CAACG,CAAC;gBACfC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;gBACrBC,MAAM,EAAEL,QAAQ,CAACK;cACnB,CAAE;cACFuF,WAAW,EAAE/D,mBAAoB;cAAAwC,QAAA,eAEjCnF,OAAA;gBAAKkF,SAAS,EAAC;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKkF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnF,OAAA;cAAAmF,QAAA,EAAI;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBxF,OAAA;cAAKkF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BzE,UAAU,iBACTV,OAAA;gBACEkF,SAAS,EAAC,iBAAiB;gBAC3Be,KAAK,EAAE;kBACLU,eAAe,EAAE,OAAOjG,UAAU,GAAG;kBACrCkG,kBAAkB,EAAE,IAAI9F,QAAQ,CAACE,CAAC,OAAOF,QAAQ,CAACG,CAAC,IAAI;kBACvD4F,cAAc,EAAE,GAAG,EAAAvG,iBAAA,GAAAiB,QAAQ,CAACyC,OAAO,cAAA1D,iBAAA,uBAAhBA,iBAAA,CAAkBkE,WAAW,KAAI,GAAG,MAAM,EAAAjE,kBAAA,GAAAgB,QAAQ,CAACyC,OAAO,cAAAzD,kBAAA,uBAAhBA,kBAAA,CAAkBoE,YAAY,KAAI,GAAG;gBACpG;cAAE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDxF,OAAA;QAAKkF,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3BjF,YAAY,iBACXF,OAAA;UAAQoF,OAAO,EAAEJ,iBAAkB;UAACE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxDnF,OAAA;YAAGkF,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDxF,OAAA;UAAKkF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnF,OAAA;YAAQoF,OAAO,EAAEhF,OAAQ;YAAC8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR9E,UAAU,iBACTV,OAAA;YAAQoF,OAAO,EAAEN,UAAW;YAACI,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAC/CnF,OAAA;cAAGkF,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxF,OAAA;MAAO8G,GAAG;MAAA3B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACnF,EAAA,CAllBQJ,WAAW;AAAA8G,EAAA,GAAX9G,WAAW;AAolBpB,eAAeA,WAAW;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}