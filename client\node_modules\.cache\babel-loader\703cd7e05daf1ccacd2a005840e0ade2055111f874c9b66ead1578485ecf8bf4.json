{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useContext}from'react';import{AuthContext}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ProductReviews(_ref){let{productId}=_ref;const{user}=useContext(AuthContext);const[reviews,setReviews]=useState([{id:1,user:'<PERSON>',rating:5,comment:'Amazing product! Really love the eco-friendly materials and the quality is outstanding.',date:'2024-01-15',verified:true},{id:2,user:'<PERSON>',rating:4,comment:'Good value for money. Shipping was fast and packaging was minimal which I appreciate.',date:'2024-01-10',verified:true},{id:3,user:'<PERSON>',rating:5,comment:'Exceeded my expectations! Will definitely buy again and recommend to friends.',date:'2024-01-08',verified:false}]);const[newReview,setNewReview]=useState({rating:5,comment:''});const[showReviewForm,setShowReviewForm]=useState(false);const averageRating=reviews.reduce((sum,review)=>sum+review.rating,0)/reviews.length;const ratingDistribution=[5,4,3,2,1].map(rating=>({rating,count:reviews.filter(r=>r.rating===rating).length,percentage:reviews.filter(r=>r.rating===rating).length/reviews.length*100}));const handleSubmitReview=e=>{e.preventDefault();if(!user){alert('Please login to submit a review');return;}const review={id:Date.now(),user:user.name,rating:newReview.rating,comment:newReview.comment,date:new Date().toISOString().split('T')[0],verified:true};setReviews([review,...reviews]);setNewReview({rating:5,comment:''});setShowReviewForm(false);};const renderStars=function(rating){let interactive=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;let onRatingChange=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;const stars=[];for(let i=1;i<=5;i++){stars.push(/*#__PURE__*/_jsx(\"button\",{type:interactive?'button':undefined,className:\"star \".concat(i<=rating?'filled':'empty',\" \").concat(interactive?'interactive':''),onClick:interactive?()=>onRatingChange(i):undefined,disabled:!interactive,children:/*#__PURE__*/_jsx(\"i\",{className:i<=rating?'fas fa-star':'far fa-star'})},i));}return stars;};return/*#__PURE__*/_jsxs(\"div\",{className:\"product-reviews\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"reviews-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Customer Reviews\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"write-review-btn\",onClick:()=>setShowReviewForm(!showReviewForm),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-edit\"}),\"Write a Review\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"reviews-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"average-rating\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rating-number\",children:averageRating.toFixed(1)}),/*#__PURE__*/_jsx(\"div\",{className:\"rating-stars\",children:renderStars(Math.round(averageRating))}),/*#__PURE__*/_jsxs(\"div\",{className:\"total-reviews\",children:[reviews.length,\" reviews\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"rating-breakdown\",children:ratingDistribution.map(_ref2=>{let{rating,count,percentage}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"rating-bar\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"rating-label\",children:[rating,\" star\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"bar-container\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bar-fill\",style:{width:\"\".concat(percentage,\"%\")}})}),/*#__PURE__*/_jsxs(\"span\",{className:\"rating-count\",children:[\"(\",count,\")\"]})]},rating);})})]}),showReviewForm&&/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmitReview,className:\"review-form\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Write Your Review\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Rating:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"rating-input\",children:renderStars(newReview.rating,true,rating=>setNewReview(_objectSpread(_objectSpread({},newReview),{},{rating})))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"comment\",children:\"Your Review:\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"comment\",value:newReview.comment,onChange:e=>setNewReview(_objectSpread(_objectSpread({},newReview),{},{comment:e.target.value})),placeholder:\"Share your experience with this product...\",required:true,rows:\"4\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowReviewForm(false),className:\"cancel-btn\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"submit-btn\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-paper-plane\"}),\"Submit Review\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"reviews-list\",children:reviews.map(review=>/*#__PURE__*/_jsxs(\"div\",{className:\"review-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"review-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"reviewer-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"reviewer-avatar\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"reviewer-name\",children:[review.user,review.verified&&/*#__PURE__*/_jsxs(\"span\",{className:\"verified-badge\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check-circle\"}),\"Verified Purchase\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"review-date\",children:review.date})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"review-rating\",children:renderStars(review.rating)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"review-comment\",children:review.comment})]},review.id))}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .product-reviews {\\n          margin-top: 3rem;\\n          padding-top: 2rem;\\n          border-top: 2px solid var(--border-color);\\n        }\\n\\n        .reviews-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 2rem;\\n        }\\n\\n        .reviews-header h3 {\\n          font-size: 1.5rem;\\n          font-weight: 600;\\n          color: var(--text-primary);\\n          margin: 0;\\n        }\\n\\n        .write-review-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          font-weight: 500;\\n          transition: all 0.2s;\\n        }\\n\\n        .write-review-btn:hover {\\n          background: var(--primary-dark);\\n        }\\n\\n        .reviews-summary {\\n          display: grid;\\n          grid-template-columns: auto 1fr;\\n          gap: 2rem;\\n          margin-bottom: 2rem;\\n          padding: 1.5rem;\\n          background: var(--bg-secondary);\\n          border-radius: 0.75rem;\\n        }\\n\\n        .average-rating {\\n          text-align: center;\\n        }\\n\\n        .rating-number {\\n          font-size: 3rem;\\n          font-weight: 700;\\n          color: var(--primary-color);\\n          line-height: 1;\\n        }\\n\\n        .rating-stars {\\n          display: flex;\\n          justify-content: center;\\n          gap: 0.25rem;\\n          margin: 0.5rem 0;\\n        }\\n\\n        .star {\\n          background: none;\\n          border: none;\\n          color: #fbbf24;\\n          font-size: 1.25rem;\\n          cursor: default;\\n        }\\n\\n        .star.interactive {\\n          cursor: pointer;\\n          transition: transform 0.1s;\\n        }\\n\\n        .star.interactive:hover {\\n          transform: scale(1.2);\\n        }\\n\\n        .star.empty {\\n          color: #d1d5db;\\n        }\\n\\n        .total-reviews {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .rating-breakdown {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 0.5rem;\\n        }\\n\\n        .rating-bar {\\n          display: grid;\\n          grid-template-columns: 60px 1fr 40px;\\n          align-items: center;\\n          gap: 0.75rem;\\n          font-size: 0.875rem;\\n        }\\n\\n        .rating-label {\\n          color: var(--text-secondary);\\n        }\\n\\n        .bar-container {\\n          height: 8px;\\n          background: #e5e7eb;\\n          border-radius: 4px;\\n          overflow: hidden;\\n        }\\n\\n        .bar-fill {\\n          height: 100%;\\n          background: #fbbf24;\\n          transition: width 0.3s ease;\\n        }\\n\\n        .rating-count {\\n          color: var(--text-secondary);\\n          text-align: right;\\n        }\\n\\n        .review-form {\\n          background: white;\\n          padding: 2rem;\\n          border-radius: 0.75rem;\\n          border: 2px solid var(--border-color);\\n          margin-bottom: 2rem;\\n        }\\n\\n        .review-form h4 {\\n          margin: 0 0 1.5rem 0;\\n          color: var(--text-primary);\\n        }\\n\\n        .form-group {\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .form-group label {\\n          display: block;\\n          margin-bottom: 0.5rem;\\n          font-weight: 500;\\n          color: var(--text-primary);\\n        }\\n\\n        .rating-input {\\n          display: flex;\\n          gap: 0.25rem;\\n        }\\n\\n        .rating-input .star {\\n          font-size: 1.5rem;\\n        }\\n\\n        textarea {\\n          width: 100%;\\n          padding: 0.75rem;\\n          border: 2px solid var(--border-color);\\n          border-radius: 0.5rem;\\n          font-family: inherit;\\n          font-size: 1rem;\\n          resize: vertical;\\n        }\\n\\n        textarea:focus {\\n          outline: none;\\n          border-color: var(--primary-color);\\n        }\\n\\n        .form-actions {\\n          display: flex;\\n          gap: 1rem;\\n          justify-content: flex-end;\\n        }\\n\\n        .cancel-btn {\\n          background: var(--bg-secondary);\\n          color: var(--text-primary);\\n          border: 1px solid var(--border-color);\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n        }\\n\\n        .submit-btn {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .reviews-list {\\n          display: flex;\\n          flex-direction: column;\\n          gap: 1.5rem;\\n        }\\n\\n        .review-item {\\n          background: white;\\n          padding: 1.5rem;\\n          border-radius: 0.75rem;\\n          border: 1px solid var(--border-color);\\n        }\\n\\n        .review-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: flex-start;\\n          margin-bottom: 1rem;\\n        }\\n\\n        .reviewer-info {\\n          display: flex;\\n          gap: 0.75rem;\\n        }\\n\\n        .reviewer-avatar {\\n          width: 40px;\\n          height: 40px;\\n          background: var(--primary-color);\\n          color: white;\\n          border-radius: 50%;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n        }\\n\\n        .reviewer-name {\\n          font-weight: 500;\\n          color: var(--text-primary);\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n        }\\n\\n        .verified-badge {\\n          background: var(--secondary-color);\\n          color: white;\\n          padding: 0.125rem 0.5rem;\\n          border-radius: 9999px;\\n          font-size: 0.75rem;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.25rem;\\n        }\\n\\n        .review-date {\\n          color: var(--text-secondary);\\n          font-size: 0.875rem;\\n        }\\n\\n        .review-rating {\\n          display: flex;\\n          gap: 0.125rem;\\n        }\\n\\n        .review-comment {\\n          color: var(--text-primary);\\n          line-height: 1.6;\\n        }\\n\\n        @media (max-width: 768px) {\\n          .reviews-summary {\\n            grid-template-columns: 1fr;\\n            text-align: center;\\n          }\\n\\n          .reviews-header {\\n            flex-direction: column;\\n            gap: 1rem;\\n            align-items: flex-start;\\n          }\\n        }\\n      \"})]});}export default ProductReviews;", "map": {"version": 3, "names": ["React", "useState", "useContext", "AuthContext", "jsx", "_jsx", "jsxs", "_jsxs", "ProductReviews", "_ref", "productId", "user", "reviews", "setReviews", "id", "rating", "comment", "date", "verified", "newReview", "set<PERSON>ew<PERSON><PERSON>iew", "showReviewForm", "setShowReviewForm", "averageRating", "reduce", "sum", "review", "length", "ratingDistribution", "map", "count", "filter", "r", "percentage", "handleSubmitReview", "e", "preventDefault", "alert", "Date", "now", "name", "toISOString", "split", "renderStars", "interactive", "arguments", "undefined", "onRatingChange", "stars", "i", "push", "type", "className", "concat", "onClick", "disabled", "children", "toFixed", "Math", "round", "_ref2", "style", "width", "onSubmit", "_objectSpread", "htmlFor", "value", "onChange", "target", "placeholder", "required", "rows"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ProductReviews.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\n\nfunction ProductReviews({ productId }) {\n  const { user } = useContext(AuthContext);\n  const [reviews, setReviews] = useState([\n    {\n      id: 1,\n      user: '<PERSON>',\n      rating: 5,\n      comment: 'Amazing product! Really love the eco-friendly materials and the quality is outstanding.',\n      date: '2024-01-15',\n      verified: true\n    },\n    {\n      id: 2,\n      user: '<PERSON>',\n      rating: 4,\n      comment: 'Good value for money. Shipping was fast and packaging was minimal which I appreciate.',\n      date: '2024-01-10',\n      verified: true\n    },\n    {\n      id: 3,\n      user: '<PERSON>',\n      rating: 5,\n      comment: 'Exceeded my expectations! Will definitely buy again and recommend to friends.',\n      date: '2024-01-08',\n      verified: false\n    }\n  ]);\n  \n  const [newReview, setNewReview] = useState({\n    rating: 5,\n    comment: ''\n  });\n  \n  const [showReviewForm, setShowReviewForm] = useState(false);\n\n  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;\n  \n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\n    rating,\n    count: reviews.filter(r => r.rating === rating).length,\n    percentage: (reviews.filter(r => r.rating === rating).length / reviews.length) * 100\n  }));\n\n  const handleSubmitReview = (e) => {\n    e.preventDefault();\n    if (!user) {\n      alert('Please login to submit a review');\n      return;\n    }\n    \n    const review = {\n      id: Date.now(),\n      user: user.name,\n      rating: newReview.rating,\n      comment: newReview.comment,\n      date: new Date().toISOString().split('T')[0],\n      verified: true\n    };\n    \n    setReviews([review, ...reviews]);\n    setNewReview({ rating: 5, comment: '' });\n    setShowReviewForm(false);\n  };\n\n  const renderStars = (rating, interactive = false, onRatingChange = null) => {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(\n        <button\n          key={i}\n          type={interactive ? 'button' : undefined}\n          className={`star ${i <= rating ? 'filled' : 'empty'} ${interactive ? 'interactive' : ''}`}\n          onClick={interactive ? () => onRatingChange(i) : undefined}\n          disabled={!interactive}\n        >\n          <i className={i <= rating ? 'fas fa-star' : 'far fa-star'}></i>\n        </button>\n      );\n    }\n    return stars;\n  };\n\n  return (\n    <div className=\"product-reviews\">\n      <div className=\"reviews-header\">\n        <h3>Customer Reviews</h3>\n        <button \n          className=\"write-review-btn\"\n          onClick={() => setShowReviewForm(!showReviewForm)}\n        >\n          <i className=\"fas fa-edit\"></i>\n          Write a Review\n        </button>\n      </div>\n\n      <div className=\"reviews-summary\">\n        <div className=\"average-rating\">\n          <div className=\"rating-number\">{averageRating.toFixed(1)}</div>\n          <div className=\"rating-stars\">\n            {renderStars(Math.round(averageRating))}\n          </div>\n          <div className=\"total-reviews\">{reviews.length} reviews</div>\n        </div>\n\n        <div className=\"rating-breakdown\">\n          {ratingDistribution.map(({ rating, count, percentage }) => (\n            <div key={rating} className=\"rating-bar\">\n              <span className=\"rating-label\">{rating} star</span>\n              <div className=\"bar-container\">\n                <div className=\"bar-fill\" style={{ width: `${percentage}%` }}></div>\n              </div>\n              <span className=\"rating-count\">({count})</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {showReviewForm && (\n        <form onSubmit={handleSubmitReview} className=\"review-form\">\n          <h4>Write Your Review</h4>\n          \n          <div className=\"form-group\">\n            <label>Rating:</label>\n            <div className=\"rating-input\">\n              {renderStars(newReview.rating, true, (rating) => \n                setNewReview({ ...newReview, rating })\n              )}\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"comment\">Your Review:</label>\n            <textarea\n              id=\"comment\"\n              value={newReview.comment}\n              onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}\n              placeholder=\"Share your experience with this product...\"\n              required\n              rows=\"4\"\n            />\n          </div>\n\n          <div className=\"form-actions\">\n            <button type=\"button\" onClick={() => setShowReviewForm(false)} className=\"cancel-btn\">\n              Cancel\n            </button>\n            <button type=\"submit\" className=\"submit-btn\">\n              <i className=\"fas fa-paper-plane\"></i>\n              Submit Review\n            </button>\n          </div>\n        </form>\n      )}\n\n      <div className=\"reviews-list\">\n        {reviews.map(review => (\n          <div key={review.id} className=\"review-item\">\n            <div className=\"review-header\">\n              <div className=\"reviewer-info\">\n                <div className=\"reviewer-avatar\">\n                  <i className=\"fas fa-user\"></i>\n                </div>\n                <div>\n                  <div className=\"reviewer-name\">\n                    {review.user}\n                    {review.verified && (\n                      <span className=\"verified-badge\">\n                        <i className=\"fas fa-check-circle\"></i>\n                        Verified Purchase\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"review-date\">{review.date}</div>\n                </div>\n              </div>\n              <div className=\"review-rating\">\n                {renderStars(review.rating)}\n              </div>\n            </div>\n            <div className=\"review-comment\">\n              {review.comment}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <style jsx>{`\n        .product-reviews {\n          margin-top: 3rem;\n          padding-top: 2rem;\n          border-top: 2px solid var(--border-color);\n        }\n\n        .reviews-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n        }\n\n        .reviews-header h3 {\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin: 0;\n        }\n\n        .write-review-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .write-review-btn:hover {\n          background: var(--primary-dark);\n        }\n\n        .reviews-summary {\n          display: grid;\n          grid-template-columns: auto 1fr;\n          gap: 2rem;\n          margin-bottom: 2rem;\n          padding: 1.5rem;\n          background: var(--bg-secondary);\n          border-radius: 0.75rem;\n        }\n\n        .average-rating {\n          text-align: center;\n        }\n\n        .rating-number {\n          font-size: 3rem;\n          font-weight: 700;\n          color: var(--primary-color);\n          line-height: 1;\n        }\n\n        .rating-stars {\n          display: flex;\n          justify-content: center;\n          gap: 0.25rem;\n          margin: 0.5rem 0;\n        }\n\n        .star {\n          background: none;\n          border: none;\n          color: #fbbf24;\n          font-size: 1.25rem;\n          cursor: default;\n        }\n\n        .star.interactive {\n          cursor: pointer;\n          transition: transform 0.1s;\n        }\n\n        .star.interactive:hover {\n          transform: scale(1.2);\n        }\n\n        .star.empty {\n          color: #d1d5db;\n        }\n\n        .total-reviews {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .rating-breakdown {\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .rating-bar {\n          display: grid;\n          grid-template-columns: 60px 1fr 40px;\n          align-items: center;\n          gap: 0.75rem;\n          font-size: 0.875rem;\n        }\n\n        .rating-label {\n          color: var(--text-secondary);\n        }\n\n        .bar-container {\n          height: 8px;\n          background: #e5e7eb;\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .bar-fill {\n          height: 100%;\n          background: #fbbf24;\n          transition: width 0.3s ease;\n        }\n\n        .rating-count {\n          color: var(--text-secondary);\n          text-align: right;\n        }\n\n        .review-form {\n          background: white;\n          padding: 2rem;\n          border-radius: 0.75rem;\n          border: 2px solid var(--border-color);\n          margin-bottom: 2rem;\n        }\n\n        .review-form h4 {\n          margin: 0 0 1.5rem 0;\n          color: var(--text-primary);\n        }\n\n        .form-group {\n          margin-bottom: 1.5rem;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 0.5rem;\n          font-weight: 500;\n          color: var(--text-primary);\n        }\n\n        .rating-input {\n          display: flex;\n          gap: 0.25rem;\n        }\n\n        .rating-input .star {\n          font-size: 1.5rem;\n        }\n\n        textarea {\n          width: 100%;\n          padding: 0.75rem;\n          border: 2px solid var(--border-color);\n          border-radius: 0.5rem;\n          font-family: inherit;\n          font-size: 1rem;\n          resize: vertical;\n        }\n\n        textarea:focus {\n          outline: none;\n          border-color: var(--primary-color);\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 1rem;\n          justify-content: flex-end;\n        }\n\n        .cancel-btn {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n        }\n\n        .submit-btn {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .reviews-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1.5rem;\n        }\n\n        .review-item {\n          background: white;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          border: 1px solid var(--border-color);\n        }\n\n        .review-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 1rem;\n        }\n\n        .reviewer-info {\n          display: flex;\n          gap: 0.75rem;\n        }\n\n        .reviewer-avatar {\n          width: 40px;\n          height: 40px;\n          background: var(--primary-color);\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .reviewer-name {\n          font-weight: 500;\n          color: var(--text-primary);\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .verified-badge {\n          background: var(--secondary-color);\n          color: white;\n          padding: 0.125rem 0.5rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .review-date {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .review-rating {\n          display: flex;\n          gap: 0.125rem;\n        }\n\n        .review-comment {\n          color: var(--text-primary);\n          line-height: 1.6;\n        }\n\n        @media (max-width: 768px) {\n          .reviews-summary {\n            grid-template-columns: 1fr;\n            text-align: center;\n          }\n\n          .reviews-header {\n            flex-direction: column;\n            gap: 1rem;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ProductReviews;\n"], "mappings": "iKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,UAAU,KAAQ,OAAO,CACnD,OAASC,WAAW,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,QAAS,CAAAC,cAAcA,CAAAC,IAAA,CAAgB,IAAf,CAAEC,SAAU,CAAC,CAAAD,IAAA,CACnC,KAAM,CAAEE,IAAK,CAAC,CAAGT,UAAU,CAACC,WAAW,CAAC,CACxC,KAAM,CAACS,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,CACrC,CACEa,EAAE,CAAE,CAAC,CACLH,IAAI,CAAE,eAAe,CACrBI,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,yFAAyF,CAClGC,IAAI,CAAE,YAAY,CAClBC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLH,IAAI,CAAE,WAAW,CACjBI,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,uFAAuF,CAChGC,IAAI,CAAE,YAAY,CAClBC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLH,IAAI,CAAE,aAAa,CACnBI,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,+EAA+E,CACxFC,IAAI,CAAE,YAAY,CAClBC,QAAQ,CAAE,KACZ,CAAC,CACF,CAAC,CAEF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAC,CACzCc,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,EACX,CAAC,CAAC,CAEF,KAAM,CAACK,cAAc,CAAEC,iBAAiB,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAAsB,aAAa,CAAGX,OAAO,CAACY,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAKD,GAAG,CAAGC,MAAM,CAACX,MAAM,CAAE,CAAC,CAAC,CAAGH,OAAO,CAACe,MAAM,CAE9F,KAAM,CAAAC,kBAAkB,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAACd,MAAM,GAAK,CACxDA,MAAM,CACNe,KAAK,CAAElB,OAAO,CAACmB,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACjB,MAAM,GAAKA,MAAM,CAAC,CAACY,MAAM,CACtDM,UAAU,CAAGrB,OAAO,CAACmB,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACjB,MAAM,GAAKA,MAAM,CAAC,CAACY,MAAM,CAAGf,OAAO,CAACe,MAAM,CAAI,GACnF,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAO,kBAAkB,CAAIC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACzB,IAAI,CAAE,CACT0B,KAAK,CAAC,iCAAiC,CAAC,CACxC,OACF,CAEA,KAAM,CAAAX,MAAM,CAAG,CACbZ,EAAE,CAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC,CACd5B,IAAI,CAAEA,IAAI,CAAC6B,IAAI,CACfzB,MAAM,CAAEI,SAAS,CAACJ,MAAM,CACxBC,OAAO,CAAEG,SAAS,CAACH,OAAO,CAC1BC,IAAI,CAAE,GAAI,CAAAqB,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5CxB,QAAQ,CAAE,IACZ,CAAC,CAEDL,UAAU,CAAC,CAACa,MAAM,CAAE,GAAGd,OAAO,CAAC,CAAC,CAChCQ,YAAY,CAAC,CAAEL,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CACxCM,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAqB,WAAW,CAAG,QAAAA,CAAC5B,MAAM,CAAiD,IAA/C,CAAA6B,WAAW,CAAAC,SAAA,CAAAlB,MAAA,IAAAkB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,IAAE,CAAAE,cAAc,CAAAF,SAAA,CAAAlB,MAAA,IAAAkB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,IAAI,CACrE,KAAM,CAAAG,KAAK,CAAG,EAAE,CAChB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC3BD,KAAK,CAACE,IAAI,cACR7C,IAAA,WAEE8C,IAAI,CAAEP,WAAW,CAAG,QAAQ,CAAGE,SAAU,CACzCM,SAAS,SAAAC,MAAA,CAAUJ,CAAC,EAAIlC,MAAM,CAAG,QAAQ,CAAG,OAAO,MAAAsC,MAAA,CAAIT,WAAW,CAAG,aAAa,CAAG,EAAE,CAAG,CAC1FU,OAAO,CAAEV,WAAW,CAAG,IAAMG,cAAc,CAACE,CAAC,CAAC,CAAGH,SAAU,CAC3DS,QAAQ,CAAE,CAACX,WAAY,CAAAY,QAAA,cAEvBnD,IAAA,MAAG+C,SAAS,CAAEH,CAAC,EAAIlC,MAAM,CAAG,aAAa,CAAG,aAAc,CAAI,CAAC,EAN1DkC,CAOC,CACV,CAAC,CACH,CACA,MAAO,CAAAD,KAAK,CACd,CAAC,CAED,mBACEzC,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAI,QAAA,eAC9BjD,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAI,QAAA,eAC7BnD,IAAA,OAAAmD,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBjD,KAAA,WACE6C,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEA,CAAA,GAAMhC,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAAAmC,QAAA,eAElDnD,IAAA,MAAG+C,SAAS,CAAC,aAAa,CAAI,CAAC,iBAEjC,EAAQ,CAAC,EACN,CAAC,cAEN7C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAI,QAAA,eAC9BjD,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAI,QAAA,eAC7BnD,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAI,QAAA,CAAEjC,aAAa,CAACkC,OAAO,CAAC,CAAC,CAAC,CAAM,CAAC,cAC/DpD,IAAA,QAAK+C,SAAS,CAAC,cAAc,CAAAI,QAAA,CAC1Bb,WAAW,CAACe,IAAI,CAACC,KAAK,CAACpC,aAAa,CAAC,CAAC,CACpC,CAAC,cACNhB,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAI,QAAA,EAAE5C,OAAO,CAACe,MAAM,CAAC,UAAQ,EAAK,CAAC,EAC1D,CAAC,cAENtB,IAAA,QAAK+C,SAAS,CAAC,kBAAkB,CAAAI,QAAA,CAC9B5B,kBAAkB,CAACC,GAAG,CAAC+B,KAAA,MAAC,CAAE7C,MAAM,CAAEe,KAAK,CAAEG,UAAW,CAAC,CAAA2B,KAAA,oBACpDrD,KAAA,QAAkB6C,SAAS,CAAC,YAAY,CAAAI,QAAA,eACtCjD,KAAA,SAAM6C,SAAS,CAAC,cAAc,CAAAI,QAAA,EAAEzC,MAAM,CAAC,OAAK,EAAM,CAAC,cACnDV,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAI,QAAA,cAC5BnD,IAAA,QAAK+C,SAAS,CAAC,UAAU,CAACS,KAAK,CAAE,CAAEC,KAAK,IAAAT,MAAA,CAAKpB,UAAU,KAAI,CAAE,CAAM,CAAC,CACjE,CAAC,cACN1B,KAAA,SAAM6C,SAAS,CAAC,cAAc,CAAAI,QAAA,EAAC,GAAC,CAAC1B,KAAK,CAAC,GAAC,EAAM,CAAC,GALvCf,MAML,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,CAELM,cAAc,eACbd,KAAA,SAAMwD,QAAQ,CAAE7B,kBAAmB,CAACkB,SAAS,CAAC,aAAa,CAAAI,QAAA,eACzDnD,IAAA,OAAAmD,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAE1BjD,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAI,QAAA,eACzBnD,IAAA,UAAAmD,QAAA,CAAO,SAAO,CAAO,CAAC,cACtBnD,IAAA,QAAK+C,SAAS,CAAC,cAAc,CAAAI,QAAA,CAC1Bb,WAAW,CAACxB,SAAS,CAACJ,MAAM,CAAE,IAAI,CAAGA,MAAM,EAC1CK,YAAY,CAAA4C,aAAA,CAAAA,aAAA,IAAM7C,SAAS,MAAEJ,MAAM,EAAE,CACvC,CAAC,CACE,CAAC,EACH,CAAC,cAENR,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAI,QAAA,eACzBnD,IAAA,UAAO4D,OAAO,CAAC,SAAS,CAAAT,QAAA,CAAC,cAAY,CAAO,CAAC,cAC7CnD,IAAA,aACES,EAAE,CAAC,SAAS,CACZoD,KAAK,CAAE/C,SAAS,CAACH,OAAQ,CACzBmD,QAAQ,CAAGhC,CAAC,EAAKf,YAAY,CAAA4C,aAAA,CAAAA,aAAA,IAAM7C,SAAS,MAAEH,OAAO,CAAEmB,CAAC,CAACiC,MAAM,CAACF,KAAK,EAAE,CAAE,CACzEG,WAAW,CAAC,4CAA4C,CACxDC,QAAQ,MACRC,IAAI,CAAC,GAAG,CACT,CAAC,EACC,CAAC,cAENhE,KAAA,QAAK6C,SAAS,CAAC,cAAc,CAAAI,QAAA,eAC3BnD,IAAA,WAAQ8C,IAAI,CAAC,QAAQ,CAACG,OAAO,CAAEA,CAAA,GAAMhC,iBAAiB,CAAC,KAAK,CAAE,CAAC8B,SAAS,CAAC,YAAY,CAAAI,QAAA,CAAC,QAEtF,CAAQ,CAAC,cACTjD,KAAA,WAAQ4C,IAAI,CAAC,QAAQ,CAACC,SAAS,CAAC,YAAY,CAAAI,QAAA,eAC1CnD,IAAA,MAAG+C,SAAS,CAAC,oBAAoB,CAAI,CAAC,gBAExC,EAAQ,CAAC,EACN,CAAC,EACF,CACP,cAED/C,IAAA,QAAK+C,SAAS,CAAC,cAAc,CAAAI,QAAA,CAC1B5C,OAAO,CAACiB,GAAG,CAACH,MAAM,eACjBnB,KAAA,QAAqB6C,SAAS,CAAC,aAAa,CAAAI,QAAA,eAC1CjD,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAI,QAAA,eAC5BjD,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAI,QAAA,eAC5BnD,IAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAI,QAAA,cAC9BnD,IAAA,MAAG+C,SAAS,CAAC,aAAa,CAAI,CAAC,CAC5B,CAAC,cACN7C,KAAA,QAAAiD,QAAA,eACEjD,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAI,QAAA,EAC3B9B,MAAM,CAACf,IAAI,CACXe,MAAM,CAACR,QAAQ,eACdX,KAAA,SAAM6C,SAAS,CAAC,gBAAgB,CAAAI,QAAA,eAC9BnD,IAAA,MAAG+C,SAAS,CAAC,qBAAqB,CAAI,CAAC,oBAEzC,EAAM,CACP,EACE,CAAC,cACN/C,IAAA,QAAK+C,SAAS,CAAC,aAAa,CAAAI,QAAA,CAAE9B,MAAM,CAACT,IAAI,CAAM,CAAC,EAC7C,CAAC,EACH,CAAC,cACNZ,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAI,QAAA,CAC3Bb,WAAW,CAACjB,MAAM,CAACX,MAAM,CAAC,CACxB,CAAC,EACH,CAAC,cACNV,IAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAAAI,QAAA,CAC5B9B,MAAM,CAACV,OAAO,CACZ,CAAC,GAzBEU,MAAM,CAACZ,EA0BZ,CACN,CAAC,CACC,CAAC,cAENT,IAAA,UAAOD,GAAG,MAAAoD,QAAA,ukNA8RD,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAhD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}