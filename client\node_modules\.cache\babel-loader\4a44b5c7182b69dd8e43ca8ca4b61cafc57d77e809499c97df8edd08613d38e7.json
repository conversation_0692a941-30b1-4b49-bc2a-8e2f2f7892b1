{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import App from'./App';import{AuthProvider}from'./context/AuthContext';import{CartProvider}from'./context/CartContext';import{WishlistProvider}from'./context/WishlistContext';import{UserStatsProvider}from'./context/UserStatsContext';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(UserStatsProvider,{children:/*#__PURE__*/_jsx(CartProvider,{children:/*#__PURE__*/_jsx(WishlistProvider,{children:/*#__PURE__*/_jsx(App,{})})})})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON>th<PERSON><PERSON><PERSON>", "CartProvider", "WishlistProvider", "UserStatsProvider", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "children"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport App from './App';\r\nimport { AuthProvider } from './context/AuthContext';\r\nimport { CartProvider } from './context/CartContext';\r\nimport { WishlistProvider } from './context/WishlistContext';\r\nimport { UserStatsProvider } from './context/UserStatsContext';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n  <AuthProvider>\r\n    <UserStatsProvider>\r\n      <CartProvider>\r\n        <WishlistProvider>\r\n          <App />\r\n        </WishlistProvider>\r\n      </CartProvider>\r\n    </UserStatsProvider>\r\n  </AuthProvider>\r\n);"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,YAAY,KAAQ,uBAAuB,CACpD,OAASC,YAAY,KAAQ,uBAAuB,CACpD,OAASC,gBAAgB,KAAQ,2BAA2B,CAC5D,OAASC,iBAAiB,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE/D,KAAM,CAAAC,IAAI,CAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACL,YAAY,EAAAW,QAAA,cACXN,IAAA,CAACF,iBAAiB,EAAAQ,QAAA,cAChBN,IAAA,CAACJ,YAAY,EAAAU,QAAA,cACXN,IAAA,CAACH,gBAAgB,EAAAS,QAAA,cACfN,IAAA,CAACN,GAAG,GAAE,CAAC,CACS,CAAC,CACP,CAAC,CACE,CAAC,CACR,CAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}