{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ErrorMessage(_ref){let{title='Something went wrong',message='An unexpected error occurred. Please try again.',onRetry=null,type='error'// 'error', 'warning', 'info'\n}=_ref;const getIcon=()=>{switch(type){case'warning':return'fas fa-exclamation-triangle';case'info':return'fas fa-info-circle';default:return'fas fa-exclamation-circle';}};const getColorClass=()=>{switch(type){case'warning':return'warning';case'info':return'info';default:return'error';}};return/*#__PURE__*/_jsxs(\"div\",{className:\"error-container \".concat(getColorClass()),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"error-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-icon\",children:/*#__PURE__*/_jsx(\"i\",{className:getIcon()})}),/*#__PURE__*/_jsxs(\"div\",{className:\"error-text\",children:[/*#__PURE__*/_jsx(\"h3\",{children:title}),/*#__PURE__*/_jsx(\"p\",{children:message})]})]}),onRetry&&/*#__PURE__*/_jsxs(\"button\",{onClick:onRetry,className:\"retry-button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-redo\"}),\"Try Again\"]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        .error-container {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          justify-content: center;\\n          padding: 2rem;\\n          border-radius: 1rem;\\n          border: 2px solid;\\n          background: white;\\n          text-align: center;\\n          max-width: 500px;\\n          margin: 2rem auto;\\n        }\\n\\n        .error-container.error {\\n          border-color: #fecaca;\\n          background: #fef2f2;\\n        }\\n\\n        .error-container.warning {\\n          border-color: #fed7aa;\\n          background: #fffbeb;\\n        }\\n\\n        .error-container.info {\\n          border-color: #bfdbfe;\\n          background: #eff6ff;\\n        }\\n\\n        .error-content {\\n          display: flex;\\n          align-items: center;\\n          gap: 1rem;\\n          margin-bottom: 1.5rem;\\n        }\\n\\n        .error-icon {\\n          font-size: 2.5rem;\\n          flex-shrink: 0;\\n        }\\n\\n        .error-container.error .error-icon {\\n          color: #dc2626;\\n        }\\n\\n        .error-container.warning .error-icon {\\n          color: #d97706;\\n        }\\n\\n        .error-container.info .error-icon {\\n          color: #2563eb;\\n        }\\n\\n        .error-text {\\n          text-align: left;\\n        }\\n\\n        .error-text h3 {\\n          margin: 0 0 0.5rem 0;\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n        }\\n\\n        .error-container.error .error-text h3 {\\n          color: #991b1b;\\n        }\\n\\n        .error-container.warning .error-text h3 {\\n          color: #92400e;\\n        }\\n\\n        .error-container.info .error-text h3 {\\n          color: #1e40af;\\n        }\\n\\n        .error-text p {\\n          margin: 0;\\n          color: var(--text-secondary);\\n          line-height: 1.5;\\n        }\\n\\n        .retry-button {\\n          background: var(--primary-color);\\n          color: white;\\n          border: none;\\n          padding: 0.75rem 1.5rem;\\n          border-radius: 0.5rem;\\n          cursor: pointer;\\n          font-weight: 500;\\n          display: flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          transition: all 0.2s;\\n        }\\n\\n        .retry-button:hover {\\n          background: var(--primary-dark);\\n          transform: translateY(-1px);\\n        }\\n\\n        @media (max-width: 768px) {\\n          .error-content {\\n            flex-direction: column;\\n            text-align: center;\\n          }\\n\\n          .error-text {\\n            text-align: center;\\n          }\\n\\n          .error-icon {\\n            font-size: 3rem;\\n          }\\n        }\\n      \"})]});}export default ErrorMessage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ErrorMessage", "_ref", "title", "message", "onRetry", "type", "getIcon", "getColorClass", "className", "concat", "children", "onClick"], "sources": ["C:/Users/<USER>/Desktop/exam/e-commerce website/ecommerce-app/client/src/components/ErrorMessage.jsx"], "sourcesContent": ["import React from 'react';\n\nfunction ErrorMessage({ \n  title = 'Something went wrong', \n  message = 'An unexpected error occurred. Please try again.', \n  onRetry = null,\n  type = 'error' // 'error', 'warning', 'info'\n}) {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n        return 'fas fa-info-circle';\n      default:\n        return 'fas fa-exclamation-circle';\n    }\n  };\n\n  const getColorClass = () => {\n    switch (type) {\n      case 'warning':\n        return 'warning';\n      case 'info':\n        return 'info';\n      default:\n        return 'error';\n    }\n  };\n\n  return (\n    <div className={`error-container ${getColorClass()}`}>\n      <div className=\"error-content\">\n        <div className=\"error-icon\">\n          <i className={getIcon()}></i>\n        </div>\n        <div className=\"error-text\">\n          <h3>{title}</h3>\n          <p>{message}</p>\n        </div>\n      </div>\n      {onRetry && (\n        <button onClick={onRetry} className=\"retry-button\">\n          <i className=\"fas fa-redo\"></i>\n          Try Again\n        </button>\n      )}\n\n      <style jsx>{`\n        .error-container {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          padding: 2rem;\n          border-radius: 1rem;\n          border: 2px solid;\n          background: white;\n          text-align: center;\n          max-width: 500px;\n          margin: 2rem auto;\n        }\n\n        .error-container.error {\n          border-color: #fecaca;\n          background: #fef2f2;\n        }\n\n        .error-container.warning {\n          border-color: #fed7aa;\n          background: #fffbeb;\n        }\n\n        .error-container.info {\n          border-color: #bfdbfe;\n          background: #eff6ff;\n        }\n\n        .error-content {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          margin-bottom: 1.5rem;\n        }\n\n        .error-icon {\n          font-size: 2.5rem;\n          flex-shrink: 0;\n        }\n\n        .error-container.error .error-icon {\n          color: #dc2626;\n        }\n\n        .error-container.warning .error-icon {\n          color: #d97706;\n        }\n\n        .error-container.info .error-icon {\n          color: #2563eb;\n        }\n\n        .error-text {\n          text-align: left;\n        }\n\n        .error-text h3 {\n          margin: 0 0 0.5rem 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n        }\n\n        .error-container.error .error-text h3 {\n          color: #991b1b;\n        }\n\n        .error-container.warning .error-text h3 {\n          color: #92400e;\n        }\n\n        .error-container.info .error-text h3 {\n          color: #1e40af;\n        }\n\n        .error-text p {\n          margin: 0;\n          color: var(--text-secondary);\n          line-height: 1.5;\n        }\n\n        .retry-button {\n          background: var(--primary-color);\n          color: white;\n          border: none;\n          padding: 0.75rem 1.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          transition: all 0.2s;\n        }\n\n        .retry-button:hover {\n          background: var(--primary-dark);\n          transform: translateY(-1px);\n        }\n\n        @media (max-width: 768px) {\n          .error-content {\n            flex-direction: column;\n            text-align: center;\n          }\n\n          .error-text {\n            text-align: center;\n          }\n\n          .error-icon {\n            font-size: 3rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ErrorMessage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,YAAYA,CAAAC,IAAA,CAKlB,IALmB,CACpBC,KAAK,CAAG,sBAAsB,CAC9BC,OAAO,CAAG,iDAAiD,CAC3DC,OAAO,CAAG,IAAI,CACdC,IAAI,CAAG,OAAQ;AACjB,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,OAAO,CAAGA,CAAA,GAAM,CACpB,OAAQD,IAAI,EACV,IAAK,SAAS,CACZ,MAAO,6BAA6B,CACtC,IAAK,MAAM,CACT,MAAO,oBAAoB,CAC7B,QACE,MAAO,2BAA2B,CACtC,CACF,CAAC,CAED,KAAM,CAAAE,aAAa,CAAGA,CAAA,GAAM,CAC1B,OAAQF,IAAI,EACV,IAAK,SAAS,CACZ,MAAO,SAAS,CAClB,IAAK,MAAM,CACT,MAAO,MAAM,CACf,QACE,MAAO,OAAO,CAClB,CACF,CAAC,CAED,mBACEN,KAAA,QAAKS,SAAS,oBAAAC,MAAA,CAAqBF,aAAa,CAAC,CAAC,CAAG,CAAAG,QAAA,eACnDX,KAAA,QAAKS,SAAS,CAAC,eAAe,CAAAE,QAAA,eAC5Bb,IAAA,QAAKW,SAAS,CAAC,YAAY,CAAAE,QAAA,cACzBb,IAAA,MAAGW,SAAS,CAAEF,OAAO,CAAC,CAAE,CAAI,CAAC,CAC1B,CAAC,cACNP,KAAA,QAAKS,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzBb,IAAA,OAAAa,QAAA,CAAKR,KAAK,CAAK,CAAC,cAChBL,IAAA,MAAAa,QAAA,CAAIP,OAAO,CAAI,CAAC,EACb,CAAC,EACH,CAAC,CACLC,OAAO,eACNL,KAAA,WAAQY,OAAO,CAAEP,OAAQ,CAACI,SAAS,CAAC,cAAc,CAAAE,QAAA,eAChDb,IAAA,MAAGW,SAAS,CAAC,aAAa,CAAI,CAAC,YAEjC,EAAQ,CACT,cAEDX,IAAA,UAAOD,GAAG,MAAAc,QAAA,2kFAmHD,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}