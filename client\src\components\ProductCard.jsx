import React, { useContext } from 'react';
import { CartContext } from '../context/CartContext';
import { Link } from 'react-router-dom';

function ProductCard({ product }) {
  const { addToCart } = useContext(CartContext);

  return (
    <div className="product-card">
      <img src={product.imageUrl} alt={product.name} width="200" />
      <h2>{product.name}</h2>
      <p>${product.price}</p>
      {product.isEcoFriendly && <span>🌱 Eco-Friendly</span>}
      <div>
        <Link to={`/product/${product._id}`}>View</Link>
        <button onClick={() => addToCart(product)} style={{ marginLeft: '10px' }}>
          Add to Cart
        </button>
      </div>
    </div>
  );
}

export default ProductCard;
